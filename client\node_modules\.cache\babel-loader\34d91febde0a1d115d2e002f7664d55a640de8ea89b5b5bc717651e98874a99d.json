{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowRight, TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbStar, TbLogin, TbMenu2, TbX } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Refs for smooth scrolling\n  const featuresRef = useRef(null);\n  const coursesRef = useRef(null);\n  const successStoriesRef = useRef(null);\n  const contactRef = useRef(null);\n  const scrollToSection = ref => {\n    var _ref$current;\n    (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        message.error(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      message.error(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-green-200/50 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://flagcdn.com/w40/tz.png\",\n                alt: \"Tanzania Flag\",\n                className: \"w-full h-full object-cover\",\n                onError: e => {\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'flex';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\",\n                children: \"TZ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(featuresRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(coursesRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(successStoriesRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl\",\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\",\n            children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"md:hidden py-4 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(featuresRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(coursesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(successStoriesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(contactRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 pt-4 border-t border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium text-center\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"text-center lg:text-left\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.3\n              },\n              className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\",\n              style: {\n                fontFamily: \"'SF Pro Display', 'Inter', sans-serif\"\n              },\n              children: [\"\\uD83C\\uDF93 Study smarter,\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600\",\n                children: \"not harder.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.4\n              },\n              className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n              children: \"Access high-quality study materials, quizzes, and academic guidance\\u2014anywhere, anytime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.5\n              },\n              className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-blue-600 text-white px-8 py-4 rounded-2xl hover:bg-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(coursesRef),\n                className: \"bg-white text-gray-900 px-8 py-4 rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\",\n                children: \"Explore Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 shadow-2xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-32 h-32 mx-auto mb-6 bg-blue-600 rounded-full flex items-center justify-center shadow-xl\",\n                  children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-16 h-16 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-900 mb-4\",\n                  children: \"Real students. Real success.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Join thousands of students achieving their academic goals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 10000,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-6 h-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Study Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-6 h-6 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Courses Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"99%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Success Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: featuresRef,\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Brainwave?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Experience the future of education with our comprehensive learning platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-8 h-8 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Quality Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Access comprehensive study materials designed by education experts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-green-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-8 h-8 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Interactive Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Test your knowledge with engaging quizzes and track your progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-8 h-8 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Expert Guidance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Get personalized academic guidance from experienced educators\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: successStoriesRef,\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Success Stories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Hear from students who transformed their academic journey with Brainwave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"Amina Hassan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Form 4 Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Brainwave helped me improve my grades significantly. The study materials are excellent!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"J\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"John Mwalimu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Form 6 Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"The interactive quizzes made learning fun and helped me understand complex topics easily.\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"Maria Kimaro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"University Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Thanks to Brainwave, I passed my national exams with flying colors and got into university!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactRef,\n      className: \"py-20 bg-gray-900 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"max-w-2xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                required: true,\n                rows: 6,\n                className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                placeholder: \"Your message...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\",\n                children: loading ? 'Sending...' : 'Send Message'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-black text-white py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 mb-6\",\n            children: \"Empowering students across Tanzania with quality education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-8 text-sm text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"hover:text-white transition-colors\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"hover:text-white transition-colors\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"hover:text-white transition-colors\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8 pt-8 border-t border-gray-800 text-center text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Brainwave. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"g7KKyuBBBvcIXU/tEQbEtuqSJCU=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useNavigate", "motion", "TbArrowRight", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbStar", "<PERSON>b<PERSON><PERSON><PERSON>", "TbMenu2", "TbX", "message", "useSelector", "contactUs", "Animated<PERSON>ounter", "jsxDEV", "_jsxDEV", "Home", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "user", "state", "navigate", "featuresRef", "coursesRef", "successStoriesRef", "contactRef", "scrollToSection", "ref", "_ref$current", "current", "scrollIntoView", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "nav", "initial", "y", "opacity", "animate", "src", "alt", "onError", "style", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "to", "div", "x", "transition", "duration", "delay", "h1", "fontFamily", "p", "whileInView", "viewport", "once", "end", "onSubmit", "type", "onChange", "required", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowRight,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbLogin,\r\n  TbMenu2,\r\n  TbX\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\r\n\r\n\r\nconst Home = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Refs for smooth scrolling\r\n  const featuresRef = useRef(null);\r\n  const coursesRef = useRef(null);\r\n  const successStoriesRef = useRef(null);\r\n  const contactRef = useRef(null);\r\n\r\n  const scrollToSection = (ref) => {\r\n    ref.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        message.error(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\">\r\n      {/* Tanzania-Inspired Navbar */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-green-200/50 shadow-lg\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo with Tanzania Flag */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* Tanzania Flag */}\r\n              <div className=\"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\">\r\n                <img\r\n                  src=\"https://flagcdn.com/w40/tz.png\"\r\n                  alt=\"Tanzania Flag\"\r\n                  className=\"w-full h-full object-cover\"\r\n                  onError={(e) => {\r\n                    e.target.style.display = 'none';\r\n                    e.target.nextSibling.style.display = 'flex';\r\n                  }}\r\n                />\r\n                <div className=\"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\">\r\n                  TZ\r\n                </div>\r\n              </div>\r\n              <TbBrain className=\"w-8 h-8 text-green-600\" />\r\n              <span className=\"text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\">\r\n                Brainwave\r\n              </span>\r\n            </div>\r\n\r\n            {/* Desktop Menu */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button\r\n                onClick={() => scrollToSection(featuresRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Features\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(coursesRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Courses\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(successStoriesRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Success Stories\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(contactRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Contact\r\n              </button>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Login\r\n              </Link>\r\n              <Link\r\n                to=\"/register\"\r\n                className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl\"\r\n              >\r\n                Get Started\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n              className=\"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n            >\r\n              {isMobileMenuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Menu */}\r\n          {isMobileMenuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"md:hidden py-4 border-t border-gray-200\"\r\n            >\r\n              <div className=\"flex flex-col space-y-4\">\r\n                <button\r\n                  onClick={() => { scrollToSection(featuresRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Features\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(coursesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Courses\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(successStoriesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Success Stories\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(contactRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Contact\r\n                </button>\r\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-green-200\">\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium text-center\"\r\n                  >\r\n                    Get Started\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n      {/* Hero Section */}\r\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Column - Text Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"text-center lg:text-left\"\r\n            >\r\n              {/* Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6\"\r\n              >\r\n                <TbTrophy className=\"w-4 h-4\" />\r\n                <span>#1 Educational Platform in Tanzania</span>\r\n              </motion.div>\r\n\r\n              {/* Main Heading */}\r\n              <motion.h1\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.3 }}\r\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\"\r\n                style={{ fontFamily: \"'SF Pro Display', 'Inter', sans-serif\" }}\r\n              >\r\n                🎓 Study smarter,{' '}\r\n                <span className=\"text-blue-600\">not harder.</span>\r\n              </motion.h1>\r\n\r\n              {/* Subtitle */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.4 }}\r\n                className=\"text-xl text-gray-600 mb-8 leading-relaxed\"\r\n              >\r\n                Access high-quality study materials, quizzes, and academic guidance—anywhere, anytime.\r\n              </motion.p>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.5 }}\r\n                className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\r\n              >\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"bg-blue-600 text-white px-8 py-4 rounded-2xl hover:bg-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\"\r\n                >\r\n                  Get Started\r\n                </Link>\r\n                <button\r\n                  onClick={() => scrollToSection(coursesRef)}\r\n                  className=\"bg-white text-gray-900 px-8 py-4 rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\"\r\n                >\r\n                  Explore Courses\r\n                </button>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Column - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative\"\r\n            >\r\n              <div className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 shadow-2xl\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"w-32 h-32 mx-auto mb-6 bg-blue-600 rounded-full flex items-center justify-center shadow-xl\">\r\n                    <TbBrain className=\"w-16 h-16 text-white\" />\r\n                  </div>\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Real students. Real success.</h3>\r\n                  <p className=\"text-gray-600\">Join thousands of students achieving their academic goals</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Quick Stats Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\"\r\n          >\r\n            {/* Stat 1 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                <TbUsers className=\"w-6 h-6 text-blue-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={10000} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Students</p>\r\n            </div>\r\n\r\n            {/* Stat 2 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center\">\r\n                <TbBook className=\"w-6 h-6 text-green-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Study Materials</p>\r\n            </div>\r\n\r\n            {/* Stat 3 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center\">\r\n                <TbSchool className=\"w-6 h-6 text-purple-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Courses Available</p>\r\n            </div>\r\n\r\n            {/* Stat 4 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center\">\r\n                <TbTrophy className=\"w-6 h-6 text-yellow-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">99%</div>\r\n              <p className=\"text-gray-600 font-medium\">Success Rate</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n      {/* Features Section */}\r\n      <section ref={featuresRef} className=\"py-20 bg-gray-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Why Choose Brainwave?</h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Experience the future of education with our comprehensive learning platform\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {/* Feature 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-2xl flex items-center justify-center\">\r\n                <TbBook className=\"w-8 h-8 text-blue-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Quality Materials</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Access comprehensive study materials designed by education experts\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-green-100 rounded-2xl flex items-center justify-center\">\r\n                <TbTrophy className=\"w-8 h-8 text-green-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Interactive Quizzes</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Test your knowledge with engaging quizzes and track your progress\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-2xl flex items-center justify-center\">\r\n                <TbStar className=\"w-8 h-8 text-purple-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Expert Guidance</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Get personalized academic guidance from experienced educators\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Success Stories Section */}\r\n      <section ref={successStoriesRef} className=\"py-20 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Success Stories</h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Hear from students who transformed their academic journey with Brainwave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {/* Testimonial 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  A\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">Amina Hassan</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Form 4 Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Brainwave helped me improve my grades significantly. The study materials are excellent!\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  J\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">John Mwalimu</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Form 6 Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"The interactive quizzes made learning fun and helped me understand complex topics easily.\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  M\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">Maria Kimaro</h4>\r\n                  <p className=\"text-gray-600 text-sm\">University Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Thanks to Brainwave, I passed my national exams with flying colors and got into university!\"\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactRef} className=\"py-20 bg-gray-900 text-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold mb-4\">Get in Touch</h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"max-w-2xl mx-auto\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              <div className=\"grid md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"Your name\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"<EMAIL>\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium mb-2\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                  rows={6}\r\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                  placeholder=\"Your message...\"\r\n                />\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\"\r\n                >\r\n                  {loading ? 'Sending...' : 'Send Message'}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-black text-white py-12\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center\">\r\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\r\n              <TbBrain className=\"w-8 h-8 text-blue-500\" />\r\n              <span className=\"text-2xl font-bold\">Brainwave</span>\r\n            </div>\r\n            <p className=\"text-gray-400 mb-6\">\r\n              Empowering students across Tanzania with quality education\r\n            </p>\r\n            <div className=\"flex justify-center space-x-8 text-sm text-gray-400\">\r\n              <Link to=\"/login\" className=\"hover:text-white transition-colors\">Login</Link>\r\n              <Link to=\"/register\" className=\"hover:text-white transition-colors\">Register</Link>\r\n              <button onClick={() => scrollToSection(contactRef)} className=\"hover:text-white transition-colors\">Contact</button>\r\n            </div>\r\n            <div className=\"mt-8 pt-8 border-t border-gray-800 text-center text-gray-500\">\r\n              <p>&copy; 2024 Brainwave. All rights reserved.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEb,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE+B;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+B,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,UAAU,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmC,iBAAiB,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMoC,UAAU,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMqC,eAAe,GAAIC,GAAG,IAAK;IAAA,IAAAC,YAAA;IAC/B,CAAAA,YAAA,GAAAD,GAAG,CAACE,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACrD,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGmB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoB,IAAI,GAAG,MAAMjC,SAAS,CAACQ,QAAQ,CAAC;MACtC,IAAIyB,IAAI,CAACC,OAAO,EAAE;QAChBpC,OAAO,CAACoC,OAAO,CAAC,4BAA4B,CAAC;QAC7CzB,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEb,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLA,OAAO,CAACqC,KAAK,CAACF,IAAI,CAACnC,OAAO,IAAI,uBAAuB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,0CAA0C,CAAC;IAC3D;IACAtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKiC,SAAS,EAAC,uEAAuE;IAAAC,QAAA,gBAEpFlC,OAAA,CAAChB,MAAM,CAACmD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,uFAAuF;MAAAC,QAAA,eAEjGlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDlC,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1ClC,OAAA;cAAKiC,SAAS,EAAC,qEAAqE;cAAAC,QAAA,gBAClFlC,OAAA;gBACEwC,GAAG,EAAC,gCAAgC;gBACpCC,GAAG,EAAC,eAAe;gBACnBR,SAAS,EAAC,4BAA4B;gBACtCS,OAAO,EAAGjB,CAAC,IAAK;kBACdA,CAAC,CAACE,MAAM,CAACgB,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC/BnB,CAAC,CAACE,MAAM,CAACkB,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;gBAC7C;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjD,OAAA;gBAAKiC,SAAS,EAAC,2IAA2I;gBAAAC,QAAA,EAAC;cAE3J;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA,CAACd,OAAO;cAAC+C,SAAS,EAAC;YAAwB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CjD,OAAA;cAAMiC,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EAAC;YAE9G;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjD,OAAA;YAAKiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlC,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACJ,WAAW,CAAE;cAC5CmB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACH,UAAU,CAAE;cAC3CkB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACF,iBAAiB,CAAE;cAClDiB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACD,UAAU,CAAE;cAC3CgB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjD,OAAA;YAAKiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlC,OAAA,CAAClB,IAAI;cACHqE,EAAE,EAAC,QAAQ;cACXlB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjD,OAAA,CAAClB,IAAI;cACHqE,EAAE,EAAC,WAAW;cACdlB,SAAS,EAAC,wLAAwL;cAAAC,QAAA,EACnM;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjD,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAM9C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtD8B,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAEnE/B,gBAAgB,gBAAGH,OAAA,CAACN,GAAG;cAACuC,SAAS,EAAC;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjD,OAAA,CAACP,OAAO;cAACwC,SAAS,EAAC;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL9C,gBAAgB,iBACfH,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAEnDlC,OAAA;YAAKiC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClC,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACJ,WAAW,CAAC;gBAAEV,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC7E6B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACH,UAAU,CAAC;gBAAEX,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E6B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACF,iBAAiB,CAAC;gBAAEZ,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cACnF6B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACD,UAAU,CAAC;gBAAEb,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E6B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjD,OAAA;cAAKiC,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrElC,OAAA,CAAClB,IAAI;gBACHqE,EAAE,EAAC,QAAQ;gBACXlB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAC7E;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjD,OAAA,CAAClB,IAAI;gBACHqE,EAAE,EAAC,WAAW;gBACdlB,SAAS,EAAC,0KAA0K;gBAAAC,QAAA,EACrL;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEbjD,OAAA;MAASiC,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eACnFlC,OAAA;QAAKiC,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpElC,OAAA;UAAKiC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BtB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAGpClC,OAAA,CAAChB,MAAM,CAACoE,GAAG;cACThB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BiB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CvB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCjD,OAAA;gBAAAkC,QAAA,EAAM;cAAmC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACyE,EAAE;cACRrB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BiB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CvB,SAAS,EAAC,6EAA6E;cACvFU,KAAK,EAAE;gBAAEe,UAAU,EAAE;cAAwC,CAAE;cAAAxB,QAAA,GAChE,6BACkB,EAAC,GAAG,eACrBlC,OAAA;gBAAMiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAGZjD,OAAA,CAAChB,MAAM,CAAC2E,CAAC;cACPvB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BiB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CvB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;cACThB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BiB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CvB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3ElC,OAAA,CAAClB,IAAI;gBACHqE,EAAE,EAAC,WAAW;gBACdlB,SAAS,EAAC,6JAA6J;gBAAAC,QAAA,EACxK;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjD,OAAA;gBACEkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACH,UAAU,CAAE;gBAC3CkB,SAAS,EAAC,uLAAuL;gBAAAC,QAAA,EAClM;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAG,CAAE;YAC/Bd,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEe,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CvB,SAAS,EAAC,UAAU;YAAAC,QAAA,eAEpBlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,4FAA4F;kBAAAC,QAAA,eACzGlC,OAAA,CAACd,OAAO;oBAAC+C,SAAS,EAAC;kBAAsB;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNjD,OAAA;kBAAIiC,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvFjD,OAAA;kBAAGiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAyD;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjClC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCiB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAGjDlC,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA,CAACX,OAAO;gBAAC4C,SAAS,EAAC;cAAuB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNjD,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAACiE,GAAG,EAAE,KAAM;gBAACR,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KACjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAGNjD,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGlC,OAAA,CAACb,MAAM;gBAAC8C,SAAS,EAAC;cAAwB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNjD,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAACiE,GAAG,EAAE,EAAG;gBAACR,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAGNjD,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGlC,OAAA,CAACV,QAAQ;gBAAC2C,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNjD,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAACiE,GAAG,EAAE,EAAG;gBAACR,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAiB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAGNjD,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNjD,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAG;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAY;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVjD,OAAA;MAASmB,GAAG,EAAEL,WAAY;MAACmB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACrDlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCiB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAqB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFjD,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbjD,OAAA;UAAKiC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAExClC,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FlC,OAAA,CAACb,MAAM;gBAAC8C,SAAS,EAAC;cAAuB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNjD,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAiB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvFjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAwB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNjD,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAmB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGlC,OAAA,CAACT,MAAM;gBAAC0C,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNjD,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFjD,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASmB,GAAG,EAAEH,iBAAkB;MAACiB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACzDlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCiB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EjD,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbjD,OAAA;UAAKiC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAEvDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,EAAC;cAE1G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjD,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDjD,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,EAAC;cAE3G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjD,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDjD,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCiB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB7B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EAAC;cAE5G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjD,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDjD,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASmB,GAAG,EAAEF,UAAW;MAACgB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAChElC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCiB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDjD,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbjD,OAAA,CAAChB,MAAM,CAACoE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BuB,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCiB,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CK,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB7B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BlC,OAAA;YAAMgE,QAAQ,EAAEpC,YAAa;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDlC,OAAA;cAAKiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9DjD,OAAA;kBACEiE,IAAI,EAAC,MAAM;kBACX1D,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrB2D,QAAQ,EAAE1C,YAAa;kBACvB2C,QAAQ;kBACRlC,SAAS,EAAC,qIAAqI;kBAC/ImC,WAAW,EAAC;gBAAW;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjD,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/DjD,OAAA;kBACEiE,IAAI,EAAC,OAAO;kBACZ1D,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtB0D,QAAQ,EAAE1C,YAAa;kBACvB2C,QAAQ;kBACRlC,SAAS,EAAC,qIAAqI;kBAC/ImC,WAAW,EAAC;gBAAgB;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjEjD,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACdmB,KAAK,EAAErB,QAAQ,CAACV,OAAQ;gBACxBuE,QAAQ,EAAE1C,YAAa;gBACvB2C,QAAQ;gBACRE,IAAI,EAAE,CAAE;gBACRpC,SAAS,EAAC,qIAAqI;gBAC/ImC,WAAW,EAAC;cAAiB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA;cAAKiC,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BlC,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAE7D,OAAQ;gBAClBwB,SAAS,EAAC,sJAAsJ;gBAAAC,QAAA,EAE/JzB,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAAQiC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eAC3ClC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAKiC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DlC,OAAA,CAACd,OAAO;cAAC+C,SAAS,EAAC;YAAuB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CjD,OAAA;cAAMiC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNjD,OAAA;YAAGiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjD,OAAA;YAAKiC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClElC,OAAA,CAAClB,IAAI;cAACqE,EAAE,EAAC,QAAQ;cAAClB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7EjD,OAAA,CAAClB,IAAI;cAACqE,EAAE,EAAC,WAAW;cAAClB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFjD,OAAA;cAAQkD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACD,UAAU,CAAE;cAACgB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChH,CAAC,eACNjD,OAAA;YAAKiC,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3ElC,OAAA;cAAAkC,QAAA,EAAG;YAA2C;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA9jBID,IAAI;EAAA,QAISL,WAAW,EACXb,WAAW;AAAA;AAAAwF,EAAA,GALxBtE,IAAI;AAgkBV,eAAeA,IAAI;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}