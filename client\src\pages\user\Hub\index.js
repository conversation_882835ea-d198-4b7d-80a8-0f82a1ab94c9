import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { useLanguage } from '../../../contexts/LanguageContext';
import './Hub.css';
import NotificationBell from '../../../components/common/NotificationBell';
import ProfilePicture from '../../../components/common/ProfilePicture';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket,
  FaRobot,
  FaSignOutAlt,
  FaVideo
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const { t, isKiswahili } = useLanguage();
  const [currentQuote, setCurrentQuote] = useState(0);




  // Inspiring quotes that rotate
  const inspiringQuotes = isKiswahili ? [
    "Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.",
    "Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.",
    "Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.",
    "Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.",
    "Amini unaweza na umefika nusu ya njia.",
    "Kikomo chako—ni mawazo yako tu.",
    "Mambo makuu hayatoki katika mazingira ya starehe.",
    "Ota. Tamani. Fanya.",
    "Mafanikio hayakutafuti tu. Lazima uende ukayatafute.",
    "Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia."
  ] : [
    "Education is the most powerful weapon which you can use to change the world.",
    "The beautiful thing about learning is that no one can take it away from you.",
    "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    "The only way to do great work is to love what you do.",
    "Believe you can and you're halfway there.",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it.",
    "Success doesn't just find you. You have to go out and get it.",
    "The harder you work for something, the greater you'll feel when you achieve it."
  ];

  // Rotate quotes every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [inspiringQuotes.length]);

  // Logout function
  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Show success message
    message.success('Logged out successfully!');

    // Navigate to home page
    navigate('/');
  };



  const navigationItems = [
    {
      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',
      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',
      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',
      icon: FaBook,
      path: '/user/study-material',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',
      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',
      icon: FaVideo,
      path: '/user/video-lessons',
      color: 'from-red-500 to-red-600',
      hoverColor: 'from-red-600 to-red-700'
    },
    {
      title: isKiswahili ? 'Ripoti' : 'Reports',
      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',
      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: isKiswahili ? 'Ujuzi' : 'Skills',
      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',
      icon: FaStar,
      path: '/user/skills',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    }
  ];

  return (
    <div className="hub-container">
      {/* Header with User Profile and Notification Bell - Same as Home */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20"
      >
        <div className="px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
          <div className="flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20">
            {/* Left section - Empty for hamburger menu alignment */}
            <div className="flex items-center space-x-2">
            </div>

            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}
            <div className="flex-1 flex justify-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative group flex items-center space-x-3"
              >
                {/* Tanzania Flag - Using actual flag image */}
                <div
                  className="rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative"
                  style={{
                    width: '32px',
                    height: '24px'
                  }}
                >
                  <img
                    src="https://flagcdn.com/w40/tz.png"
                    alt="Tanzania Flag"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      // Fallback to another flag source if first fails
                      e.target.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png";
                      e.target.onerror = () => {
                        // Final fallback - hide image and show text
                        e.target.style.display = 'none';
                        e.target.parentElement.innerHTML = '<div class="w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold">TZ</div>';
                      };
                    }}
                  />
                </div>

                {/* Amazing Animated Brainwave Text */}
                <div className="relative brainwave-container">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                      style={{
                        fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                        letterSpacing: '-0.02em'
                      }}>
                    {/* Brain - with amazing effects */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: -30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        textShadow: [
                          "0 0 10px rgba(59, 130, 246, 0.5)",
                          "0 0 20px rgba(59, 130, 246, 0.8)",
                          "0 0 10px rgba(59, 130, 246, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.3,
                        textShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, -2, 2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#1f2937',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                      }}
                    >
                      Brain

                      {/* Electric spark */}
                      <motion.div
                        className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0.5, 1.2, 0.5],
                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: 2
                        }}
                        style={{
                          backgroundColor: '#3b82f6',
                          boxShadow: '0 0 10px #3b82f6'
                        }}
                      />
                    </motion.span>

                    {/* Wave - with flowing effects (no space) */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: 30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        y: [0, -2, 0, 2, 0],
                        textShadow: [
                          "0 0 10px rgba(16, 185, 129, 0.5)",
                          "0 0 20px rgba(16, 185, 129, 0.8)",
                          "0 0 10px rgba(16, 185, 129, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.5,
                        y: {
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        },
                        textShadow: {
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, 2, -2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#059669',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                      }}
                    >
                      wave

                      {/* Wave particle */}
                      <motion.div
                        className="absolute top-0 left-0 w-1.5 h-1.5 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          x: [0, 40, 80],
                          y: [0, -5, 0, 5, 0],
                          backgroundColor: ['#10b981', '#34d399', '#10b981']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: 1
                        }}
                        style={{
                          backgroundColor: '#10b981',
                          boxShadow: '0 0 8px #10b981'
                        }}
                      />
                    </motion.span>
                  </h1>

                  {/* Glowing underline effect */}
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    animate={{
                      width: '100%',
                      opacity: 1,
                      boxShadow: [
                        '0 0 10px rgba(16, 185, 129, 0.5)',
                        '0 0 20px rgba(59, 130, 246, 0.8)',
                        '0 0 10px rgba(16, 185, 129, 0.5)'
                      ]
                    }}
                    transition={{
                      duration: 1.5,
                      delay: 1.2,
                      boxShadow: {
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                    }}
                  />
                </div>

                {/* Official Logo - Small like profile */}
                <div
                  className="rounded-full overflow-hidden border-2 border-white/20 relative"
                  style={{
                    background: '#f0f0f0',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    width: '32px',
                    height: '32px'
                  }}
                >
                  <img
                    src="/favicon.png"
                    alt="Brainwave Logo"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div
                    className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold"
                    style={{
                      display: 'none',
                      fontSize: '12px'
                    }}
                  >
                    🧠
                  </div>
                </div>

                {/* Modern Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
              </motion.div>
            </div>

            {/* Right Section - Notifications + User Profile */}
            <div className="flex items-center justify-end space-x-2 sm:space-x-3">
              {/* Notification Bell */}
              {user && !user?.isAdmin && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <NotificationBell />
                </motion.div>
              )}

              {/* User Profile Section */}
              {user && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 group"
                >
                  {/* Profile Picture with Online Status */}
                  <ProfilePicture
                    user={user}
                    size="sm"
                    showOnlineStatus={true}
                    style={{
                      width: '32px',
                      height: '32px'
                    }}
                  />

                  {/* User Name and Class */}
                  <div className="hidden sm:block text-right">
                    <div className="text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300">
                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </motion.header>

      <div className="hub-content">
        <div className="hub-header">
          <h1 className="hub-welcome">
            {isKiswahili ? 'Karibu' : 'Welcome'}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}
          </h1>
          <p className="hub-subtitle">
            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}
          </p>

          <div className="hub-quote">
            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />
            "{inspiringQuotes[currentQuote]}"
            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />
            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>
              - BrainWave Team
            </div>
          </div>
        </div>





        <div className="hub-grid-container">
          <div className="hub-grid">
            {navigationItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`hub-card hover:${item.hoverColor} ${item.color}`}
                  onClick={() => navigate(item.path)}
                  tabIndex={0}
                  role="button"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      navigate(item.path);
                    }
                  }}
                  style={{
                    cursor: 'pointer',
                    touchAction: 'manipulation', // Improves touch responsiveness
                  }}
                >


                  <div className="hub-card-icon">
                    <IconComponent />
                  </div>

                  <h3 className="hub-card-title">
                    {item.title}
                  </h3>

                  <p className="hub-card-description">
                    {item.description}
                  </p>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="hub-bottom-decoration"
          >
            <div className="decoration-content">
              <FaGraduationCap className="decoration-icon animate-bounce-gentle" />
              <span>Your learning journey starts here!</span>
              <FaRocket className="decoration-icon animate-bounce-gentle" />
            </div>
          </motion.div>
        </div>


      </div>
    </div>
  );
};

export default Hub;