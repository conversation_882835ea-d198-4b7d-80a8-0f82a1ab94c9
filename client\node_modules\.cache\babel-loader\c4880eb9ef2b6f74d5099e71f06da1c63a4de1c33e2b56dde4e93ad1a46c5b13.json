{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { getUserInfo } from '../../../apicalls/users';\nimport { SetUser } from '../../../redux/usersSlice';\n\n// Professional Sound System\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst playSound = type => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n    switch (type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        // Check for instant cached data first\n        const instantCacheKey = `quiz_instant_${id}`;\n        const instantCached = localStorage.getItem(instantCacheKey);\n        if (instantCached) {\n          try {\n            const cachedQuiz = JSON.parse(instantCached);\n            console.log('📚 Using instant cached quiz data');\n            setQuiz(cachedQuiz);\n            setQuestions(cachedQuiz.questions);\n            setAnswers(new Array(cachedQuiz.questions.length).fill(''));\n            setTimeLeft(cachedQuiz.duration || 180);\n            setStartTime(new Date());\n            setLoading(false);\n\n            // Clean up instant cache\n            localStorage.removeItem(instantCacheKey);\n            return;\n          } catch (error) {\n            console.error('Error parsing instant cache:', error);\n          }\n        }\n\n        // Check regular cache\n        const cacheKey = `quiz_data_${id}`;\n        const cachedData = localStorage.getItem(cacheKey);\n        const cacheTime = localStorage.getItem(`${cacheKey}_time`);\n        const now = Date.now();\n        if (cachedData && cacheTime && now - parseInt(cacheTime) < 600000) {\n          try {\n            const cachedQuiz = JSON.parse(cachedData);\n            console.log('📚 Using cached quiz data');\n            setQuiz(cachedQuiz);\n            setQuestions(cachedQuiz.questions);\n            setAnswers(new Array(cachedQuiz.questions.length).fill(''));\n            setTimeLeft(cachedQuiz.duration || 180);\n            setStartTime(new Date());\n            setLoading(false);\n            return;\n          } catch (error) {\n            console.error('Error parsing cached data:', error);\n          }\n        }\n        setLoading(true);\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          // Duration is already in seconds, no need to multiply by 60\n          setTimeLeft(response.data.duration || 180);\n          setStartTime(new Date());\n\n          // Cache the quiz data for future use\n          const cacheKey = `quiz_data_${id}`;\n          localStorage.setItem(cacheKey, JSON.stringify(response.data));\n          localStorage.setItem(`${cacheKey}_time`, Date.now().toString());\n          console.log('Quiz loaded successfully:', response.data);\n          console.log('Quiz duration (seconds):', response.data.duration);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Function to refresh user data after quiz completion\n  const refreshUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\n        console.log('🔄 User data refreshed with updated XP:', response.data.totalXP);\n      }\n    } catch (error) {\n      console.error('Error refreshing user data:', error);\n    }\n  };\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          var _actualCorrectAnswer;\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.toLowerCase().trim()) === ((_actualCorrectAnswer = actualCorrectAnswer) === null || _actualCorrectAnswer === void 0 ? void 0 : _actualCorrectAnswer.toLowerCase().trim());\n        }\n        if (isCorrect) correctAnswers++;\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken,\n          // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n      try {\n        console.log('📤 Submitting quiz report:', reportData);\n        const response = await addReport(reportData);\n        console.log('📥 Server response:', response);\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n          console.log('🎯 XP Data received from server:', response.xpData);\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null,\n            // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage,\n            // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          console.log('🚀 Navigation state with XP data:', navigationState);\n\n          // Refresh user data to get updated XP\n          await refreshUserData();\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Just log the error, don't show notification to user\n          setTimeout(() => {\n            setSubmitting(false);\n            // message.error(response.message || 'Failed to submit quiz'); // Removed notification\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Just log the error, don't show notification to user\n        setTimeout(() => {\n          setSubmitting(false);\n          // message.error('Network error while submitting quiz'); // Removed notification\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Just log the error, don't show notification to user\n      setTimeout(() => {\n        setSubmitting(false);\n        // message.error('Failed to submit quiz'); // Removed notification\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown with auto-submit\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Auto-submit when timer reaches 0\n      console.log('⏰ Time up! Auto-submitting quiz...');\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Duolingo-style time formatting (min:sec format)\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-800 font-medium\",\n            children: \"No options found for this question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"text-sm text-yellow-600 cursor-pointer\",\n              children: \"Show question data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\",\n              children: JSON.stringify(currentQ, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: ['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index);\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: options.map((option, index) => {\n        const optionLetter = String.fromCharCode(65 + index);\n        const isSelected = answers[currentQuestion] === option;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(option),\n          className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n              children: optionLetter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n              children: typeof option === 'string' ? option : JSON.stringify(option)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-800 text-sm font-medium mb-2\",\n          children: \"Fill in the blank:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700\",\n          children: \"Type your answer in the box below\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: answers[currentQuestion] || '',\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Question Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Unable to load the current question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Invalid Question Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"The question data is corrupted or invalid.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes professionalSpin {\n            0% { transform: rotate(0deg) scale(1); }\n            50% { transform: rotate(180deg) scale(1.1); }\n            100% { transform: rotate(360deg) scale(1); }\n          }\n          @keyframes elegantPulse {\n            0%, 100% { opacity: 1; transform: scale(1); }\n            50% { opacity: 0.7; transform: scale(1.05); }\n          }\n          @keyframes smoothBounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n            40% { transform: translateY(-8px) scale(1.2); }\n            60% { transform: translateY(-4px) scale(1.1); }\n          }\n          @keyframes gradientShift {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n          }\n          @keyframes fadeInUp {\n            0% { opacity: 0; transform: translateY(20px); }\n            100% { opacity: 1; transform: translateY(0); }\n          }\n          @keyframes orbitalSpin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          .professional-dot {\n            animation: smoothBounce 1.6s infinite ease-in-out both;\n          }\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\n          .gradient-bg {\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\n            background-size: 400% 400%;\n            animation: gradientShift 4s ease infinite;\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-bg\",\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(255, 255, 255, 0.98)',\n            borderRadius: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '20px' : '24px',\n            padding: window.innerWidth <= 480 ? '24px 16px' : window.innerWidth <= 768 ? '32px 24px' : '48px 40px',\n            textAlign: 'center',\n            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)',\n            backdropFilter: 'blur(20px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            maxWidth: window.innerWidth <= 480 ? '280px' : window.innerWidth <= 768 ? '320px' : '450px',\n            width: window.innerWidth <= 480 ? '95%' : '90%',\n            animation: 'fadeInUp 0.6s ease-out',\n            margin: window.innerWidth <= 480 ? '0 auto' : 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: window.innerWidth <= 480 ? '80px' : window.innerWidth <= 768 ? '100px' : '120px',\n              height: window.innerWidth <= 480 ? '80px' : window.innerWidth <= 768 ? '100px' : '120px',\n              margin: window.innerWidth <= 480 ? '0 auto 24px auto' : '0 auto 32px auto',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              animation: 'professionalSpin 3s ease-in-out infinite',\n              boxShadow: '0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '50px' : '60px',\n                height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '50px' : '60px',\n                background: 'white',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: window.innerWidth <= 480 ? '20px' : window.innerWidth <= 768 ? '24px' : '28px',\n                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                },\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                width: '140%',\n                height: '140%',\n                border: '2px solid rgba(255, 255, 255, 0.3)',\n                borderRadius: '50%',\n                animation: 'orbitalSpin 4s linear infinite reverse'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: window.innerWidth <= 480 ? '20px' : window.innerWidth <= 768 ? '24px' : '32px',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea, #764ba2)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n              margin: window.innerWidth <= 480 ? '0 0 8px 0' : '0 0 12px 0',\n              animation: 'elegantPulse 2.5s infinite',\n              letterSpacing: window.innerWidth <= 480 ? '-0.25px' : '-0.5px'\n            },\n            children: \"Evaluating Your Quiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '14px' : '16px',\n              color: '#64748b',\n              margin: window.innerWidth <= 480 ? '0 0 24px 0' : '0 0 32px 0',\n              lineHeight: '1.6',\n              fontWeight: '500',\n              padding: window.innerWidth <= 480 ? '0 8px' : '0'\n            },\n            children: \"Our advanced system is carefully reviewing your answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: window.innerWidth <= 480 ? '4px' : '6px',\n              marginBottom: window.innerWidth <= 480 ? '12px' : '16px'\n            },\n            children: [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"professional-dot\",\n              style: {\n                width: window.innerWidth <= 480 ? '8px' : '10px',\n                height: window.innerWidth <= 480 ? '8px' : '10px',\n                background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                borderRadius: '50%',\n                boxShadow: '0 2px 8px rgba(102, 126, 234, 0.4)'\n              }\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: window.innerWidth <= 480 ? '10px' : window.innerWidth <= 768 ? '12px' : '14px',\n              color: '#94a3b8',\n              fontWeight: '500',\n              textTransform: 'uppercase',\n              letterSpacing: window.innerWidth <= 480 ? '0.5px' : '1px'\n            },\n            children: \"Processing...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative p-2 sm:p-4 lg:p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200 rounded-lg mb-3 sm:mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 text-center sm:text-left truncate\",\n            children: quiz.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 sm:gap-3 rounded-lg sm:rounded-xl shadow-lg border-2 px-3 sm:px-4 py-2 sm:py-3\",\n              style: {\n                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                background: timeLeft <= 60 ? 'linear-gradient(to right, #ef4444, #dc2626)' : 'linear-gradient(to right, #22c55e, #16a34a)',\n                borderColor: timeLeft <= 60 ? '#fca5a5' : '#86efac',\n                color: 'white',\n                boxShadow: timeLeft <= 60 ? '0 0 20px rgba(239, 68, 68, 0.6), 0 4px 20px rgba(0,0,0,0.3)' : '0 0 15px rgba(34, 197, 94, 0.4), 0 4px 20px rgba(0,0,0,0.3)',\n                animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                style: {\n                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                  animation: timeLeft <= 60 ? 'bounce 1s infinite' : 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm sm:text-base lg:text-lg font-mono font-black\",\n                style: {\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.7)',\n                  animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                },\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm sm:text-base text-gray-600 font-medium text-center sm:text-right\",\n            children: [currentQuestion + 1, \" of \", questions.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3 sm:mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs sm:text-sm text-gray-600 font-medium\",\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs sm:text-sm text-blue-600 font-bold\",\n              children: [Math.round((currentQuestion + 1) / questions.length * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full rounded-full overflow-hidden shadow-inner\",\n            style: {\n              height: window.innerWidth <= 768 ? '8px' : '12px',\n              backgroundColor: '#e5e7eb'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`,\n                height: '100%',\n                background: 'linear-gradient(to right, #2563eb, #1d4ed8)',\n                borderRadius: '9999px',\n                transition: 'width 0.5s ease-out',\n                boxShadow: '0 2px 4px rgba(37, 99, 235, 0.4)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 855,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg sm:rounded-2xl shadow-lg sm:shadow-xl border border-gray-200 transition-all duration-300 p-4 sm:p-6 lg:p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 text-center mb-4 sm:mb-6 leading-tight\",\n            children: typeof currentQ.name === 'string' ? currentQ.name : 'Question'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question diagram\",\n              className: \"max-w-full h-auto rounded-lg shadow-lg mx-auto block max-h-48 sm:max-h-64 lg:max-h-80\",\n              onError: e => {\n                e.target.style.display = 'none';\n                // Show fallback message\n                const fallback = document.createElement('div');\n                fallback.className = 'text-center py-8 text-gray-500';\n                fallback.innerHTML = '<p>Could not load diagram</p>';\n                e.target.parentNode.appendChild(fallback);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          style: {\n            marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n          },\n          children: renderAnswerSection()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          style: {\n            flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n            justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n            gap: window.innerWidth <= 768 ? '12px' : '0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            disabled: submitting,\n            className: `flex items-center gap-2 rounded-lg font-semibold transition-colors ${submitting ? 'bg-gray-400 text-gray-200 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full border-2 border-white border-t-transparent\",\n                style: {\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 21\n              }, this), \"Submitting...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                style: {\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 21\n              }, this), \"Submit Quiz\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 853,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"z4GYzot3QsObI2R2iSUJ9C7WEpw=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "useDispatch", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "getUserInfo", "SetUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "playSound", "type", "audioContext", "window", "AudioContext", "webkitAudioContext", "createTone", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "currentTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "setTimeout", "error", "console", "log", "QuizPlay", "_s", "id", "navigate", "dispatch", "user", "state", "loading", "setLoading", "submitting", "setSubmitting", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "_id", "token", "localStorage", "getItem", "instantCache<PERSON>ey", "instantCached", "cachedQuiz", "JSON", "parse", "Array", "length", "fill", "Date", "removeItem", "cache<PERSON>ey", "cachedData", "cacheTime", "now", "parseInt", "response", "examId", "success", "data", "setItem", "stringify", "toString", "refreshUserData", "totalXP", "handleSubmitQuiz", "currentUser", "storedUser", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "actualCorrectAnswer", "questionType", "answerType", "toLowerCase", "options", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "_actualCorrectAnswer", "trim", "questionId", "questionName", "name", "questionText", "String", "questionImage", "image", "imageUrl", "percentage", "round", "passingPercentage", "passingMarks", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "xpData", "navigationState", "quizName", "quizSubject", "subject", "category", "Promise", "resolve", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "secs", "padStart", "renderAnswerSection", "currentQ", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "isArray", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "position", "top", "left", "width", "height", "display", "alignItems", "justifyContent", "zIndex", "background", "borderRadius", "innerWidth", "padding", "textAlign", "boxShadow", "<PERSON><PERSON>ilter", "border", "max<PERSON><PERSON><PERSON>", "animation", "margin", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "letterSpacing", "color", "lineHeight", "gap", "marginBottom", "i", "textTransform", "textShadow", "borderColor", "backgroundColor", "transition", "src", "alt", "onError", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "flexDirection", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { getUserInfo } from '../../../apicalls/users';\nimport { SetUser } from '../../../redux/usersSlice';\n\n// Professional Sound System\nconst playSound = (type) => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n\n    switch(type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        console.log('Loading quiz with ID:', id);\n\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        // Check for instant cached data first\n        const instantCacheKey = `quiz_instant_${id}`;\n        const instantCached = localStorage.getItem(instantCacheKey);\n\n        if (instantCached) {\n          try {\n            const cachedQuiz = JSON.parse(instantCached);\n            console.log('📚 Using instant cached quiz data');\n\n            setQuiz(cachedQuiz);\n            setQuestions(cachedQuiz.questions);\n            setAnswers(new Array(cachedQuiz.questions.length).fill(''));\n            setTimeLeft(cachedQuiz.duration || 180);\n            setStartTime(new Date());\n            setLoading(false);\n\n            // Clean up instant cache\n            localStorage.removeItem(instantCacheKey);\n            return;\n          } catch (error) {\n            console.error('Error parsing instant cache:', error);\n          }\n        }\n\n        // Check regular cache\n        const cacheKey = `quiz_data_${id}`;\n        const cachedData = localStorage.getItem(cacheKey);\n        const cacheTime = localStorage.getItem(`${cacheKey}_time`);\n        const now = Date.now();\n\n        if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 600000) {\n          try {\n            const cachedQuiz = JSON.parse(cachedData);\n            console.log('📚 Using cached quiz data');\n\n            setQuiz(cachedQuiz);\n            setQuestions(cachedQuiz.questions);\n            setAnswers(new Array(cachedQuiz.questions.length).fill(''));\n            setTimeLeft(cachedQuiz.duration || 180);\n            setStartTime(new Date());\n            setLoading(false);\n            return;\n          } catch (error) {\n            console.error('Error parsing cached data:', error);\n          }\n        }\n\n        setLoading(true);\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          // Duration is already in seconds, no need to multiply by 60\n          setTimeLeft(response.data.duration || 180);\n          setStartTime(new Date());\n\n          // Cache the quiz data for future use\n          const cacheKey = `quiz_data_${id}`;\n          localStorage.setItem(cacheKey, JSON.stringify(response.data));\n          localStorage.setItem(`${cacheKey}_time`, Date.now().toString());\n\n          console.log('Quiz loaded successfully:', response.data);\n          console.log('Quiz duration (seconds):', response.data.duration);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Function to refresh user data after quiz completion\n  const refreshUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\n        console.log('🔄 User data refreshed with updated XP:', response.data.totalXP);\n      }\n    } catch (error) {\n      console.error('Error refreshing user data:', error);\n    }\n  };\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();\n        }\n\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      try {\n        console.log('📤 Submitting quiz report:', reportData);\n        const response = await addReport(reportData);\n        console.log('📥 Server response:', response);\n\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n          console.log('🎯 XP Data received from server:', response.xpData);\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage, // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          console.log('🚀 Navigation state with XP data:', navigationState);\n\n          // Refresh user data to get updated XP\n          await refreshUserData();\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Just log the error, don't show notification to user\n          setTimeout(() => {\n            setSubmitting(false);\n            // message.error(response.message || 'Failed to submit quiz'); // Removed notification\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Just log the error, don't show notification to user\n        setTimeout(() => {\n          setSubmitting(false);\n          // message.error('Network error while submitting quiz'); // Removed notification\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Just log the error, don't show notification to user\n      setTimeout(() => {\n        setSubmitting(false);\n        // message.error('Failed to submit quiz'); // Removed notification\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown with auto-submit\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Auto-submit when timer reaches 0\n      console.log('⏰ Time up! Auto-submitting quiz...');\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft, handleSubmitQuiz]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Duolingo-style time formatting (min:sec format)\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return (\n      <>\n        <style>{`\n          @keyframes professionalSpin {\n            0% { transform: rotate(0deg) scale(1); }\n            50% { transform: rotate(180deg) scale(1.1); }\n            100% { transform: rotate(360deg) scale(1); }\n          }\n          @keyframes elegantPulse {\n            0%, 100% { opacity: 1; transform: scale(1); }\n            50% { opacity: 0.7; transform: scale(1.05); }\n          }\n          @keyframes smoothBounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n            40% { transform: translateY(-8px) scale(1.2); }\n            60% { transform: translateY(-4px) scale(1.1); }\n          }\n          @keyframes gradientShift {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n          }\n          @keyframes fadeInUp {\n            0% { opacity: 0; transform: translateY(20px); }\n            100% { opacity: 1; transform: translateY(0); }\n          }\n          @keyframes orbitalSpin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          .professional-dot {\n            animation: smoothBounce 1.6s infinite ease-in-out both;\n          }\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\n          .gradient-bg {\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\n            background-size: 400% 400%;\n            animation: gradientShift 4s ease infinite;\n          }\n        `}</style>\n        <div className=\"gradient-bg\" style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        }}>\n          <div style={{\n            background: 'rgba(255, 255, 255, 0.98)',\n            borderRadius: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '20px' : '24px',\n            padding: window.innerWidth <= 480 ? '24px 16px' : window.innerWidth <= 768 ? '32px 24px' : '48px 40px',\n            textAlign: 'center',\n            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)',\n            backdropFilter: 'blur(20px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            maxWidth: window.innerWidth <= 480 ? '280px' : window.innerWidth <= 768 ? '320px' : '450px',\n            width: window.innerWidth <= 480 ? '95%' : '90%',\n            animation: 'fadeInUp 0.6s ease-out',\n            margin: window.innerWidth <= 480 ? '0 auto' : 'auto'\n          }}>\n            {/* Professional Animated Icon */}\n            <div style={{\n              width: window.innerWidth <= 480 ? '80px' : window.innerWidth <= 768 ? '100px' : '120px',\n              height: window.innerWidth <= 480 ? '80px' : window.innerWidth <= 768 ? '100px' : '120px',\n              margin: window.innerWidth <= 480 ? '0 auto 24px auto' : '0 auto 32px auto',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              animation: 'professionalSpin 3s ease-in-out infinite',\n              boxShadow: '0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)',\n              position: 'relative'\n            }}>\n              <div style={{\n                width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '50px' : '60px',\n                height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '50px' : '60px',\n                background: 'white',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}>\n                <span style={{\n                  fontSize: window.innerWidth <= 480 ? '20px' : window.innerWidth <= 768 ? '24px' : '28px',\n                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                }}>🎯</span>\n              </div>\n              {/* Orbital rings */}\n              <div style={{\n                position: 'absolute',\n                width: '140%',\n                height: '140%',\n                border: '2px solid rgba(255, 255, 255, 0.3)',\n                borderRadius: '50%',\n                animation: 'orbitalSpin 4s linear infinite reverse'\n              }}></div>\n            </div>\n\n            {/* Enhanced Main Message */}\n            <h2 style={{\n              fontSize: window.innerWidth <= 480 ? '20px' : window.innerWidth <= 768 ? '24px' : '32px',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea, #764ba2)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n              margin: window.innerWidth <= 480 ? '0 0 8px 0' : '0 0 12px 0',\n              animation: 'elegantPulse 2.5s infinite',\n              letterSpacing: window.innerWidth <= 480 ? '-0.25px' : '-0.5px'\n            }}>Evaluating Your Quiz</h2>\n\n            {/* Professional Sub Message */}\n            <p style={{\n              fontSize: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '14px' : '16px',\n              color: '#64748b',\n              margin: window.innerWidth <= 480 ? '0 0 24px 0' : '0 0 32px 0',\n              lineHeight: '1.6',\n              fontWeight: '500',\n              padding: window.innerWidth <= 480 ? '0 8px' : '0'\n            }}>Our advanced system is carefully reviewing your answers</p>\n\n            {/* Enhanced Progress Indicator */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: window.innerWidth <= 480 ? '4px' : '6px',\n              marginBottom: window.innerWidth <= 480 ? '12px' : '16px'\n            }}>\n              {[1, 2, 3, 4].map(i => (\n                <div\n                  key={i}\n                  className=\"professional-dot\"\n                  style={{\n                    width: window.innerWidth <= 480 ? '8px' : '10px',\n                    height: window.innerWidth <= 480 ? '8px' : '10px',\n                    background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                    borderRadius: '50%',\n                    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.4)'\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Progress Text */}\n            <div style={{\n              fontSize: window.innerWidth <= 480 ? '10px' : window.innerWidth <= 768 ? '12px' : '14px',\n              color: '#94a3b8',\n              fontWeight: '500',\n              textTransform: 'uppercase',\n              letterSpacing: window.innerWidth <= 480 ? '0.5px' : '1px'\n            }}>\n              Processing...\n            </div>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative p-2 sm:p-4 lg:p-6\">\n      {/* Header removed - using ProtectedRoute header only */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200 rounded-lg mb-3 sm:mb-6\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          {/* Quiz Content Layout */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4\">\n            {/* Quiz Title */}\n            <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 text-center sm:text-left truncate\">\n              {quiz.name}\n            </h1>\n\n            {/* Timer - Responsive */}\n            <div className=\"flex justify-center\">\n              <div\n                className=\"flex items-center gap-2 sm:gap-3 rounded-lg sm:rounded-xl shadow-lg border-2 px-3 sm:px-4 py-2 sm:py-3\"\n                style={{\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.5)',\n                  background: timeLeft <= 60\n                    ? 'linear-gradient(to right, #ef4444, #dc2626)'\n                    : 'linear-gradient(to right, #22c55e, #16a34a)',\n                  borderColor: timeLeft <= 60 ? '#fca5a5' : '#86efac',\n                  color: 'white',\n                  boxShadow: timeLeft <= 60\n                    ? '0 0 20px rgba(239, 68, 68, 0.6), 0 4px 20px rgba(0,0,0,0.3)'\n                    : '0 0 15px rgba(34, 197, 94, 0.4), 0 4px 20px rgba(0,0,0,0.3)',\n                  animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                }}>\n                <TbClock\n                  className=\"w-4 h-4 sm:w-5 sm:h-5\"\n                  style={{\n                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                    animation: timeLeft <= 60 ? 'bounce 1s infinite' : 'none'\n                  }}\n                />\n\n                <span\n                  className=\"text-sm sm:text-base lg:text-lg font-mono font-black\"\n                  style={{\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.7)',\n                    animation: timeLeft <= 60 ? 'pulse 1s infinite' : 'none'\n                  }}>\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n            </div>\n\n            {/* Progress */}\n            <p className=\"text-sm sm:text-base text-gray-600 font-medium text-center sm:text-right\">\n              {currentQuestion + 1} of {questions.length}\n            </p>\n          </div>\n\n          {/* Responsive Progress Bar */}\n          <div className=\"mb-3 sm:mb-4\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-xs sm:text-sm text-gray-600 font-medium\">\n                Progress\n              </span>\n              <span className=\"text-xs sm:text-sm text-blue-600 font-bold\">\n                {Math.round(((currentQuestion + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div\n              className=\"w-full rounded-full overflow-hidden shadow-inner\"\n              style={{\n                height: window.innerWidth <= 768 ? '8px' : '12px',\n                backgroundColor: '#e5e7eb'\n              }}\n            >\n              <div\n                style={{\n                  width: `${((currentQuestion + 1) / questions.length) * 100}%`,\n                  height: '100%',\n                  background: 'linear-gradient(to right, #2563eb, #1d4ed8)',\n                  borderRadius: '9999px',\n                  transition: 'width 0.5s ease-out',\n                  boxShadow: '0 2px 4px rgba(37, 99, 235, 0.4)'\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Responsive Main Content */}\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        <div className=\"bg-white rounded-lg sm:rounded-2xl shadow-lg sm:shadow-xl border border-gray-200 transition-all duration-300 p-4 sm:p-6 lg:p-8\">\n          {/* Responsive Question */}\n          <div className=\"mb-6 sm:mb-8\">\n            <h2 className=\"text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 text-center mb-4 sm:mb-6 leading-tight\">\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n\n            {currentQ.image && (\n              <div className=\"bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block max-h-48 sm:max-h-64 lg:max-h-80\"\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div\n            className=\"space-y-4\"\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div\n            className=\"flex items-center\"\n            style={{\n              flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n              justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n              gap: window.innerWidth <= 768 ? '12px' : '0'\n            }}\n          >\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                width: window.innerWidth <= 768 ? '100%' : 'auto',\n                justifyContent: 'center'\n              }}\n            >\n              <TbArrowLeft\n                style={{\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }}\n              />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                disabled={submitting}\n                className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                  submitting\n                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                }`}\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                {submitting ? (\n                  <>\n                    <div\n                      className=\"animate-spin rounded-full border-2 border-white border-t-transparent\"\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    ></div>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <TbCheck\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                    Submit Quiz\n                  </>\n                )}\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                Next\n                <TbArrowRight\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAIC,IAAI,IAAK;EAC1B,IAAI;IACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;IAE7E,MAAMC,UAAU,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEP,IAAI,GAAG,MAAM,KAAK;MACzD,MAAMQ,UAAU,GAAGP,YAAY,CAACQ,gBAAgB,CAAC,CAAC;MAClD,MAAMC,QAAQ,GAAGT,YAAY,CAACU,UAAU,CAAC,CAAC;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;MAE1CL,UAAU,CAACF,SAAS,CAACQ,cAAc,CAACR,SAAS,EAAEL,YAAY,CAACc,WAAW,CAAC;MACxEP,UAAU,CAACR,IAAI,GAAGA,IAAI;MAEtBU,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,CAAC;MACzDL,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEhB,YAAY,CAACc,WAAW,GAAG,IAAI,CAAC;MAC3EL,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEjB,YAAY,CAACc,WAAW,GAAGR,QAAQ,CAAC;MAEtFC,UAAU,CAACW,KAAK,CAAClB,YAAY,CAACc,WAAW,CAAC;MAC1CP,UAAU,CAACY,IAAI,CAACnB,YAAY,CAACc,WAAW,GAAGR,QAAQ,CAAC;IACtD,CAAC;IAED,QAAOP,IAAI;MACT,KAAK,QAAQ;QACX;QACAK,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;QAC9B;MACF,KAAK,UAAU;QACb;QACAA,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;QAC7BgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC;QAClD;MACF,KAAK,QAAQ;QACX;QACAA,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAC9BgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACrDgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD;MACF;QACEA,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACd;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;AACF,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAG7C,SAAS,CAAC,CAAC;EAC1B,MAAM8C,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C;EAAK,CAAC,GAAG9C,WAAW,CAAE+C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqE,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFzB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,EAAE,CAAC;QAExC,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACmB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACV3B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDtC,OAAO,CAACoC,KAAK,CAAC,gCAAgC,CAAC;YAC/CzC,eAAe,CAAC,MAAM;cACpB+C,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;;QAEA;QACA,MAAMyB,eAAe,GAAI,gBAAe1B,EAAG,EAAC;QAC5C,MAAM2B,aAAa,GAAGH,YAAY,CAACC,OAAO,CAACC,eAAe,CAAC;QAE3D,IAAIC,aAAa,EAAE;UACjB,IAAI;YACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;YAC5C/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAEhDa,OAAO,CAACkB,UAAU,CAAC;YACnBhB,YAAY,CAACgB,UAAU,CAACjB,SAAS,CAAC;YAClCK,UAAU,CAAC,IAAIe,KAAK,CAACH,UAAU,CAACjB,SAAS,CAACqB,MAAM,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3Df,WAAW,CAACU,UAAU,CAAChD,QAAQ,IAAI,GAAG,CAAC;YACvCwC,YAAY,CAAC,IAAIc,IAAI,CAAC,CAAC,CAAC;YACxB5B,UAAU,CAAC,KAAK,CAAC;;YAEjB;YACAkB,YAAY,CAACW,UAAU,CAACT,eAAe,CAAC;YACxC;UACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACtD;QACF;;QAEA;QACA,MAAMyC,QAAQ,GAAI,aAAYpC,EAAG,EAAC;QAClC,MAAMqC,UAAU,GAAGb,YAAY,CAACC,OAAO,CAACW,QAAQ,CAAC;QACjD,MAAME,SAAS,GAAGd,YAAY,CAACC,OAAO,CAAE,GAAEW,QAAS,OAAM,CAAC;QAC1D,MAAMG,GAAG,GAAGL,IAAI,CAACK,GAAG,CAAC,CAAC;QAEtB,IAAIF,UAAU,IAAIC,SAAS,IAAKC,GAAG,GAAGC,QAAQ,CAACF,SAAS,CAAC,GAAI,MAAM,EAAE;UACnE,IAAI;YACF,MAAMV,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACO,UAAU,CAAC;YACzCzC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;YAExCa,OAAO,CAACkB,UAAU,CAAC;YACnBhB,YAAY,CAACgB,UAAU,CAACjB,SAAS,CAAC;YAClCK,UAAU,CAAC,IAAIe,KAAK,CAACH,UAAU,CAACjB,SAAS,CAACqB,MAAM,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3Df,WAAW,CAACU,UAAU,CAAChD,QAAQ,IAAI,GAAG,CAAC;YACvCwC,YAAY,CAAC,IAAIc,IAAI,CAAC,CAAC,CAAC;YACxB5B,UAAU,CAAC,KAAK,CAAC;YACjB;UACF,CAAC,CAAC,OAAOX,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UACpD;QACF;QAEAW,UAAU,CAAC,IAAI,CAAC;QAEhB,MAAMmC,QAAQ,GAAG,MAAM7E,WAAW,CAAC;UAAE8E,MAAM,EAAE1C;QAAG,CAAC,CAAC;QAClDJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4C,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBrF,OAAO,CAACoC,KAAK,CAAC,qBAAqB,CAAC;YACpCzC,eAAe,CAAC,MAAM;cACpB+C,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAACwC,QAAQ,CAACG,IAAI,CAACjC,SAAS,IAAI8B,QAAQ,CAACG,IAAI,CAACjC,SAAS,CAACqB,MAAM,KAAK,CAAC,EAAE;YACpEzE,OAAO,CAACoC,KAAK,CAAC,sCAAsC,CAAC;YACrDzC,eAAe,CAAC,MAAM;cACpB+C,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAS,OAAO,CAAC+B,QAAQ,CAACG,IAAI,CAAC;UACtBhC,YAAY,CAAC6B,QAAQ,CAACG,IAAI,CAACjC,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIe,KAAK,CAACU,QAAQ,CAACG,IAAI,CAACjC,SAAS,CAACqB,MAAM,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9D;UACAf,WAAW,CAACuB,QAAQ,CAACG,IAAI,CAAChE,QAAQ,IAAI,GAAG,CAAC;UAC1CwC,YAAY,CAAC,IAAIc,IAAI,CAAC,CAAC,CAAC;;UAExB;UACA,MAAME,QAAQ,GAAI,aAAYpC,EAAG,EAAC;UAClCwB,YAAY,CAACqB,OAAO,CAACT,QAAQ,EAAEP,IAAI,CAACiB,SAAS,CAACL,QAAQ,CAACG,IAAI,CAAC,CAAC;UAC7DpB,YAAY,CAACqB,OAAO,CAAE,GAAET,QAAS,OAAM,EAAEF,IAAI,CAACK,GAAG,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC,CAAC;UAE/DnD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4C,QAAQ,CAACG,IAAI,CAAC;UACvDhD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE4C,QAAQ,CAACG,IAAI,CAAChE,QAAQ,CAAC;QACjE,CAAC,MAAM;UACLgB,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAE8C,QAAQ,CAAClF,OAAO,CAAC;UAClDA,OAAO,CAACoC,KAAK,CAAC8C,QAAQ,CAAClF,OAAO,IAAI,qBAAqB,CAAC;UACxDL,eAAe,CAAC,MAAM;YACpB+C,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CpC,OAAO,CAACoC,KAAK,CAAC,wCAAwC,CAAC;QACvDzC,eAAe,CAAC,MAAM;UACpB+C,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRK,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIN,EAAE,IAAIG,IAAI,EAAE;MACdkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACrB,EAAE,EAAEC,QAAQ,EAAEE,IAAI,CAAC,CAAC;;EAExB;EACA,MAAM6C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAM3E,WAAW,CAAC,CAAC;MACpC,IAAI2E,QAAQ,CAACE,OAAO,EAAE;QACpBzC,QAAQ,CAACnC,OAAO,CAAC0E,QAAQ,CAACG,IAAI,CAAC,CAAC;QAChCpB,YAAY,CAACqB,OAAO,CAAC,MAAM,EAAEhB,IAAI,CAACiB,SAAS,CAACL,QAAQ,CAACG,IAAI,CAAC,CAAC;QAC3DhD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE4C,QAAQ,CAACG,IAAI,CAACK,OAAO,CAAC;MAC/E;IACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAGjG,WAAW,CAAC,YAAY;IAC/C2C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjED,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEU,UAAU,CAAC;IAEpD,IAAI;MACF;MACAnC,SAAS,CAAC,QAAQ,CAAC;;MAEnB;MACAoC,aAAa,CAAC,IAAI,CAAC;MACnBZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIsD,WAAW,GAAGhD,IAAI;MACtB,IAAI,CAACgD,WAAW,IAAI,CAACA,WAAW,CAAC7B,GAAG,EAAE;QACpC,MAAM8B,UAAU,GAAG5B,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAI2B,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGtB,IAAI,CAACC,KAAK,CAACsB,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOzD,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDzC,eAAe,CAAC,MAAM;cACpB+C,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACkD,WAAW,IAAI,CAACA,WAAW,CAAC7B,GAAG,EAAE;QACpC/D,OAAO,CAACoC,KAAK,CAAC,2CAA2C,CAAC;QAC1DzC,eAAe,CAAC,MAAM;UACpB+C,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMoD,OAAO,GAAG,IAAInB,IAAI,CAAC,CAAC;MAC1B,MAAMoB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGlC,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAIsC,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAG/C,SAAS,CAACgD,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAG/C,OAAO,CAAC8C,KAAK,CAAC;QACjC,IAAIE,SAAS,GAAG,KAAK;QACrB,IAAIC,mBAAmB,GAAG,EAAE;;QAE5B;QACA,MAAMC,YAAY,GAAGL,QAAQ,CAACvF,IAAI,IAAIuF,QAAQ,CAACM,UAAU,IAAI,KAAK;QAElE,IAAID,YAAY,CAACE,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIF,YAAY,KAAK,SAAS,EAAE;UACtE;UACA,IAAIL,QAAQ,CAACQ,OAAO,IAAI,OAAOR,QAAQ,CAACQ,OAAO,KAAK,QAAQ,EAAE;YAC5D;YACA,IAAIR,QAAQ,CAACS,aAAa,IAAIT,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC,EAAE;cACtEL,mBAAmB,GAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC;cAC9DN,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACU,aAAa,IAAIV,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC,EAAE;cAC3EN,mBAAmB,GAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC;cAC9DP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACS,aAAa,EAAE;cAC/BL,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa;cAC5CN,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;UACF,CAAC,MAAM;YACL;YACAA,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa,IAAIT,QAAQ,CAACU,aAAa,IAAI,EAAE;YAC5EP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;UAChD;QACF,CAAC,MAAM;UAAA,IAAAO,oBAAA;UACL;UACAP,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa,IAAI,EAAE;UAClDN,SAAS,GAAG,CAAAD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,QAAAD,oBAAA,GAAKP,mBAAmB,cAAAO,oBAAA,uBAAnBA,oBAAA,CAAqBJ,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QAC5F;QAEA,IAAIT,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLgB,UAAU,EAAEb,QAAQ,CAACtC,GAAG,IAAK,YAAWuC,KAAM,EAAC;UAC/Ca,YAAY,EAAE,OAAOd,QAAQ,CAACe,IAAI,KAAK,QAAQ,GAAGf,QAAQ,CAACe,IAAI,GAAI,YAAWd,KAAK,GAAG,CAAE,EAAC;UACzFe,YAAY,EAAEhB,QAAQ,CAACe,IAAI,IAAK,YAAWd,KAAK,GAAG,CAAE,EAAC;UACtDC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGe,MAAM,CAACf,UAAU,IAAI,EAAE,CAAC;UAClFO,aAAa,EAAEL,mBAAmB;UAClCD,SAAS;UACTE,YAAY,EAAEA,YAAY;UAC1BG,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI,IAAI;UACjCU,aAAa,EAAElB,QAAQ,CAACmB,KAAK,IAAInB,QAAQ,CAACkB,aAAa,IAAIlB,QAAQ,CAACoB,QAAQ,IAAI,IAAI;UACpFD,KAAK,EAAEnB,QAAQ,CAACmB,KAAK,IAAInB,QAAQ,CAACkB,aAAa,IAAIlB,QAAQ,CAACoB,QAAQ,IAAI;QAC1E,CAAC;MACH,CAAC,CAAC;MAEF,MAAMC,UAAU,GAAG1B,IAAI,CAAC2B,KAAK,CAAEzB,cAAc,GAAG9C,SAAS,CAACqB,MAAM,GAAI,GAAG,CAAC;MACxE;MACA,MAAMmD,iBAAiB,GAAG1E,IAAI,CAAC2E,YAAY,IAAI3E,IAAI,CAAC0E,iBAAiB,IAAI,EAAE;MAC3E,MAAME,OAAO,GAAGJ,UAAU,IAAIE,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEjE,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAEvF,EAAE;QACRG,IAAI,EAAEgD,WAAW,CAAC7B,GAAG;QACrBkE,MAAM,EAAE;UACN/B,cAAc;UACdgC,YAAY,EAAE9E,SAAS,CAACqB,MAAM,GAAGyB,cAAc;UAC/CwB,UAAU;UACVS,KAAK,EAAET,UAAU;UACjBI,OAAO,EAAEA,OAAO;UAChB/B,SAAS;UACTqC,SAAS,EAAErC,SAAS;UAAE;UACtBsC,MAAM,EAAEnC,cAAc,GAAG,EAAE;UAC3BoC,cAAc,EAAElF,SAAS,CAACqB;QAC5B;MACF,CAAC;MAED,IAAI;QACFpC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyF,UAAU,CAAC;QACrD,MAAM7C,QAAQ,GAAG,MAAM5E,SAAS,CAACyH,UAAU,CAAC;QAC5C1F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4C,QAAQ,CAAC;QAE5C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB/C,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClED,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4C,QAAQ,CAACqD,MAAM,CAAC;;UAEhE;UACA,MAAMC,eAAe,GAAG;YACtBd,UAAU;YACVxB,cAAc;YACdoC,cAAc,EAAElF,SAAS,CAACqB,MAAM;YAChCsB,SAAS;YACTI,aAAa;YACboC,MAAM,EAAErD,QAAQ,CAACqD,MAAM,IAAI,IAAI;YAAE;YACjCE,QAAQ,EAAEvF,IAAI,CAACkE,IAAI;YACnBsB,WAAW,EAAExF,IAAI,CAACyF,OAAO,IAAIzF,IAAI,CAAC0F,QAAQ;YAC1ChB,iBAAiB,EAAEA,iBAAiB;YAAE;YACtCE,OAAO,EAAEA,OAAO,CAAC;UACnB,CAAC;;UAEDzF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEkG,eAAe,CAAC;;UAEjE;UACA,MAAM/C,eAAe,CAAC,CAAC;;UAEvB;UACA,MAAM,IAAIoD,OAAO,CAACC,OAAO,IAAI3G,UAAU,CAAC2G,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvDzG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C3C,eAAe,CAAC,MAAM;YACpB+C,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;cAC7BI,KAAK,EAAE2F;YACT,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLnG,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAE8C,QAAQ,CAAClF,OAAO,CAAC;UAC5D;UACAmC,UAAU,CAAC,MAAM;YACfc,aAAa,CAAC,KAAK,CAAC;YACpB;UACF,CAAC,EAAE,IAAI,CAAC;UACR;QACF;MACF,CAAC,CAAC,OAAO8F,QAAQ,EAAE;QACjB1G,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAE2G,QAAQ,CAAC;QACzD;QACA5G,UAAU,CAAC,MAAM;UACfc,aAAa,CAAC,KAAK,CAAC;UACpB;QACF,CAAC,EAAE,IAAI,CAAC;QACR;MACF;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAD,UAAU,CAAC,MAAM;QACfc,aAAa,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,EAAE,IAAI,CAAC;MACR;IACF,CAAC,SAAS;MACRA,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACW,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEf,EAAE,EAAEC,QAAQ,EAAEE,IAAI,CAAC,CAAC;;EAEvD;EACAnD,SAAS,CAAC,MAAM;IACd,IAAIiE,QAAQ,IAAI,CAAC,EAAE;MACjB;MACArB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDqD,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMqD,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BtF,WAAW,CAACuF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACtF,QAAQ,EAAEiC,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAMyD,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAG9F,OAAO,CAAC;IAC/B8F,UAAU,CAAChG,eAAe,CAAC,GAAG+F,MAAM;IACpC5F,UAAU,CAAC6F,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIjG,eAAe,GAAGF,SAAS,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC1C5D,SAAS,CAAC,UAAU,CAAC;MACrB0C,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMkG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIlG,eAAe,GAAG,CAAC,EAAE;MACvBzC,SAAS,CAAC,UAAU,CAAC;MACrB0C,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMmG,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG3D,IAAI,CAACC,KAAK,CAACyD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IAEzB,OAAQ,GAAEC,OAAQ,IAAGC,IAAI,CAACpE,QAAQ,CAAC,CAAC,CAACqE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACzD,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMpD,YAAY,GAAGqD,QAAQ,CAACjJ,IAAI,IAAIiJ,QAAQ,CAACpD,UAAU,IAAI,KAAK;IAIlE,QAAQD,YAAY,CAACE,WAAW,CAAC,CAAC;MAChC,KAAK,KAAK;MACV,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;QACnB,OAAOoD,oBAAoB,CAAC,CAAC;MAE/B,KAAK,MAAM;MACX,KAAK,mBAAmB;MACxB,KAAK,WAAW;MAChB,KAAK,MAAM;QACT,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,OAAOC,mBAAmB,CAAC,CAAC;MAE9B;QACE;QACA,OAAOF,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAInD,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAIrC,KAAK,CAAC2F,OAAO,CAACJ,QAAQ,CAAClD,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAGkD,QAAQ,CAAClD,OAAO;IAC5B,CAAC,MAAM,IAAIkD,QAAQ,CAAClD,OAAO,IAAI,OAAOkD,QAAQ,CAAClD,OAAO,KAAK,QAAQ,EAAE;MACnE;MACAA,OAAO,GAAGuD,MAAM,CAACC,MAAM,CAACN,QAAQ,CAAClD,OAAO,CAAC;IAC3C,CAAC,MAAM,IAAIkD,QAAQ,CAACO,OAAO,IAAIP,QAAQ,CAACQ,OAAO,EAAE;MAC/C;MACA1D,OAAO,GAAG,CAACkD,QAAQ,CAACO,OAAO,EAAEP,QAAQ,CAACQ,OAAO,EAAER,QAAQ,CAACS,OAAO,EAAET,QAAQ,CAACU,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACpG;IAEA,IAAI,CAAC9D,OAAO,IAAIA,OAAO,CAACpC,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,oBACE/D,OAAA;QAAKkK,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnK,OAAA;UAAKkK,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFnK,OAAA;YAAGkK,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjFvK,OAAA;YAASkK,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACvBnK,OAAA;cAASkK,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxFvK,OAAA;cAAKkK,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACrFvG,IAAI,CAACiB,SAAS,CAACwE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNvK,OAAA;UAAKkK,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACzE,GAAG,CAAC,CAAC8E,MAAM,EAAE5E,KAAK,KAAK;YACnG,MAAM6E,YAAY,GAAG7D,MAAM,CAAC8D,YAAY,CAAC,EAAE,GAAG9E,KAAK,CAAC;YACpD,MAAM+E,UAAU,GAAG7H,OAAO,CAACF,eAAe,CAAC,KAAK4H,MAAM;YAEtD,oBACExK,OAAA;cAEE4K,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAAC8B,MAAM,CAAE;cAC1CN,SAAS,EAAG,wFACVS,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;cAAAR,QAAA,eAEHnK,OAAA;gBAAKkK,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCnK,OAAA;kBAAKkK,SAAS,EAAG,2FACfS,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAR,QAAA,EACAM;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNvK,OAAA;kBAAMkK,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACrEK;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAnBD3E,KAAK;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBJ,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACEvK,OAAA;MAAKkK,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBhE,OAAO,CAACT,GAAG,CAAC,CAAC8E,MAAM,EAAE5E,KAAK,KAAK;QAC9B,MAAM6E,YAAY,GAAG7D,MAAM,CAAC8D,YAAY,CAAC,EAAE,GAAG9E,KAAK,CAAC;QACpD,MAAM+E,UAAU,GAAG7H,OAAO,CAACF,eAAe,CAAC,KAAK4H,MAAM;QAEtD,oBACExK,OAAA;UAEE4K,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAAC8B,MAAM,CAAE;UAC1CN,SAAS,EAAG,wFACVS,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;UAAAR,QAAA,eAEHnK,OAAA;YAAKkK,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCnK,OAAA;cAAKkK,SAAS,EAAG,2FACfS,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAAR,QAAA,EACAM;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvK,OAAA;cAAMkK,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EACrE,OAAOK,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG5G,IAAI,CAACiB,SAAS,CAAC2F,MAAM;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC,GAnBD3E,KAAK;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBJ,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMhB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACEvJ,OAAA;MAAKkK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnK,OAAA;QAAKkK,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DnK,OAAA;UAAGkK,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5EvK,OAAA;UAAGkK,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNvK,OAAA;QAAKkK,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBnK,OAAA;UACEI,IAAI,EAAC,MAAM;UACXyK,KAAK,EAAE/H,OAAO,CAACF,eAAe,CAAC,IAAI,EAAG;UACtCkI,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAACqC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,0BAA0B;UACtCf,SAAS,EAAC,mHAAmH;UAC7HgB,SAAS;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMf,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIH,QAAQ,CAAClD,OAAO,IAAIrC,KAAK,CAAC2F,OAAO,CAACJ,QAAQ,CAAClD,OAAO,CAAC,IAAIkD,QAAQ,CAAClD,OAAO,CAACpC,MAAM,GAAG,CAAC,EAAE;MACtF,OAAOuF,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOC,oBAAoB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,IAAInH,OAAO,EAAE;IACX,oBACEpC,OAAA;MAAKkK,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnK,OAAA;QAAKkK,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnK,OAAA;UAAKkK,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGvK,OAAA;UAAGkK,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC/H,IAAI,IAAI,CAACE,SAAS,CAACqB,MAAM,EAAE;IAC9B,oBACE/D,OAAA;MAAKkK,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnK,OAAA;QAAKkK,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnK,OAAA;UAAKkK,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnK,OAAA;YAAIkK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFvK,OAAA;YAAGkK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EvK,OAAA;YACE4K,OAAO,EAAEA,CAAA,KAAM3L,eAAe,CAAC,MAAM+C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDkI,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC7H,SAAS,CAACE,eAAe,CAAC,EAAE;IAC/B,oBACE5C,OAAA;MAAKkK,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnK,OAAA;QAAKkK,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnK,OAAA;UAAKkK,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnK,OAAA;YAAIkK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EvK,OAAA;YAAGkK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1EvK,OAAA;YACE4K,OAAO,EAAEA,CAAA,KAAM3L,eAAe,CAAC,MAAM+C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDkI,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMlB,QAAQ,GAAG3G,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAMuI,cAAc,GAAGvI,eAAe,KAAKF,SAAS,CAACqB,MAAM,GAAG,CAAC;;EAE/D;EACA,IAAI,CAACsF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACErJ,OAAA;MAAKkK,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGnK,OAAA;QAAKkK,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFnK,OAAA;UAAKkK,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnK,OAAA;YAAIkK,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EvK,OAAA;YAAGkK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFvK,OAAA;YACE4K,OAAO,EAAEA,CAAA,KAAM3L,eAAe,CAAC,MAAM+C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDkI,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAIA;EACA,IAAIjI,UAAU,EAAE;IACd,oBACEtC,OAAA,CAAAE,SAAA;MAAAiK,QAAA,gBACEnK,OAAA;QAAAmK,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACVvK,OAAA;QAAKkK,SAAS,EAAC,aAAa;QAACkB,KAAK,EAAE;UAClCC,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE;QACV,CAAE;QAAA1B,QAAA,eACAnK,OAAA;UAAKoL,KAAK,EAAE;YACVU,UAAU,EAAE,2BAA2B;YACvCC,YAAY,EAAEzL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;YAC5FC,OAAO,EAAE3L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;YACtGE,SAAS,EAAE,QAAQ;YACnBC,SAAS,EAAE,gEAAgE;YAC3EC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAE,oCAAoC;YAC5CC,QAAQ,EAAEhM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;YAC3FR,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;YAC/CO,SAAS,EAAE,wBAAwB;YACnCC,MAAM,EAAElM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG;UAChD,CAAE;UAAA7B,QAAA,gBAEAnK,OAAA;YAAKoL,KAAK,EAAE;cACVI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;cACvFP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;cACxFQ,MAAM,EAAElM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,kBAAkB,GAAG,kBAAkB;cAC1EF,UAAU,EAAE,gEAAgE;cAC5EC,YAAY,EAAE,KAAK;cACnBL,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBW,SAAS,EAAE,0CAA0C;cACrDJ,SAAS,EAAE,0EAA0E;cACrFd,QAAQ,EAAE;YACZ,CAAE;YAAAlB,QAAA,gBACAnK,OAAA;cAAKoL,KAAK,EAAE;gBACVI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACrFP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACtFF,UAAU,EAAE,OAAO;gBACnBC,YAAY,EAAE,KAAK;gBACnBL,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBO,SAAS,EAAE;cACb,CAAE;cAAAhC,QAAA,eACAnK,OAAA;gBAAMoL,KAAK,EAAE;kBACXqB,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACxFhC,MAAM,EAAE;gBACV,CAAE;gBAAAG,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENvK,OAAA;cAAKoL,KAAK,EAAE;gBACVC,QAAQ,EAAE,UAAU;gBACpBG,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdY,MAAM,EAAE,oCAAoC;gBAC5CN,YAAY,EAAE,KAAK;gBACnBQ,SAAS,EAAE;cACb;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvK,OAAA;YAAIoL,KAAK,EAAE;cACTqB,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACxFU,UAAU,EAAE,KAAK;cACjBZ,UAAU,EAAE,2CAA2C;cACvDa,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCC,cAAc,EAAE,MAAM;cACtBL,MAAM,EAAElM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,YAAY;cAC7DO,SAAS,EAAE,4BAA4B;cACvCO,aAAa,EAAExM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,SAAS,GAAG;YACxD,CAAE;YAAA7B,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG5BvK,OAAA;YAAGoL,KAAK,EAAE;cACRqB,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACxFe,KAAK,EAAE,SAAS;cAChBP,MAAM,EAAElM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,YAAY,GAAG,YAAY;cAC9DgB,UAAU,EAAE,KAAK;cACjBN,UAAU,EAAE,KAAK;cACjBT,OAAO,EAAE3L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;YAChD,CAAE;YAAA7B,QAAA,EAAC;UAAuD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAG9DvK,OAAA;YAAKoL,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpBsB,GAAG,EAAE3M,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;cAC7CkB,YAAY,EAAE5M,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YACpD,CAAE;YAAA7B,QAAA,EACC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACzE,GAAG,CAACyH,CAAC,iBACjBnN,OAAA;cAEEkK,SAAS,EAAC,kBAAkB;cAC5BkB,KAAK,EAAE;gBACLI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;gBAChDP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;gBACjDF,UAAU,EAAE,2CAA2C;gBACvDC,YAAY,EAAE,KAAK;gBACnBI,SAAS,EAAE;cACb;YAAE,GARGgB,CAAC;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASP,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNvK,OAAA;YAAKoL,KAAK,EAAE;cACVqB,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG1L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACxFe,KAAK,EAAE,SAAS;cAChBL,UAAU,EAAE,KAAK;cACjBU,aAAa,EAAE,WAAW;cAC1BN,aAAa,EAAExM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;YACtD,CAAE;YAAA7B,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACEvK,OAAA;IAAKkK,SAAS,EAAC,sFAAsF;IAAAC,QAAA,gBAEnGnK,OAAA;MAAKkK,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFnK,OAAA;QAAKkK,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElEnK,OAAA;UAAKkK,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAE1FnK,OAAA;YAAIkK,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EACrG3H,IAAI,CAACkE;UAAI;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGLvK,OAAA;YAAKkK,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClCnK,OAAA;cACEkK,SAAS,EAAC,wGAAwG;cAClHkB,KAAK,EAAE;gBACLiC,UAAU,EAAE,6BAA6B;gBACzCvB,UAAU,EAAE9I,QAAQ,IAAI,EAAE,GACtB,6CAA6C,GAC7C,6CAA6C;gBACjDsK,WAAW,EAAEtK,QAAQ,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;gBACnD+J,KAAK,EAAE,OAAO;gBACdZ,SAAS,EAAEnJ,QAAQ,IAAI,EAAE,GACrB,6DAA6D,GAC7D,6DAA6D;gBACjEuJ,SAAS,EAAEvJ,QAAQ,IAAI,EAAE,GAAG,mBAAmB,GAAG;cACpD,CAAE;cAAAmH,QAAA,gBACFnK,OAAA,CAACT,OAAO;gBACN2K,SAAS,EAAC,uBAAuB;gBACjCkB,KAAK,EAAE;kBACLpB,MAAM,EAAE,wCAAwC;kBAChDuC,SAAS,EAAEvJ,QAAQ,IAAI,EAAE,GAAG,oBAAoB,GAAG;gBACrD;cAAE;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEFvK,OAAA;gBACEkK,SAAS,EAAC,sDAAsD;gBAChEkB,KAAK,EAAE;kBACLiC,UAAU,EAAE,6BAA6B;kBACzCd,SAAS,EAAEvJ,QAAQ,IAAI,EAAE,GAAG,mBAAmB,GAAG;gBACpD,CAAE;gBAAAmH,QAAA,EACDpB,UAAU,CAAC/F,QAAQ;cAAC;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvK,OAAA;YAAGkK,SAAS,EAAC,0EAA0E;YAAAC,QAAA,GACpFvH,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACqB,MAAM;UAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNvK,OAAA;UAAKkK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnK,OAAA;YAAKkK,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDnK,OAAA;cAAMkK,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPvK,OAAA;cAAMkK,SAAS,EAAC,4CAA4C;cAAAC,QAAA,GACzD7E,IAAI,CAAC2B,KAAK,CAAE,CAACrE,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACqB,MAAM,GAAI,GAAG,CAAC,EAAC,GAChE;YAAA;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvK,OAAA;YACEkK,SAAS,EAAC,kDAAkD;YAC5DkB,KAAK,EAAE;cACLK,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;cACjDuB,eAAe,EAAE;YACnB,CAAE;YAAApD,QAAA,eAEFnK,OAAA;cACEoL,KAAK,EAAE;gBACLI,KAAK,EAAG,GAAG,CAAC5I,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACqB,MAAM,GAAI,GAAI,GAAE;gBAC7D0H,MAAM,EAAE,MAAM;gBACdK,UAAU,EAAE,6CAA6C;gBACzDC,YAAY,EAAE,QAAQ;gBACtByB,UAAU,EAAE,qBAAqB;gBACjCrB,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvK,OAAA;MAAKkK,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDnK,OAAA;QAAKkK,SAAS,EAAC,gIAAgI;QAAAC,QAAA,gBAE7InK,OAAA;UAAKkK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnK,OAAA;YAAIkK,SAAS,EAAC,2GAA2G;YAAAC,QAAA,EACtH,OAAOd,QAAQ,CAAC3C,IAAI,KAAK,QAAQ,GAAG2C,QAAQ,CAAC3C,IAAI,GAAG;UAAU;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAEJlB,QAAQ,CAACvC,KAAK,iBACb9G,OAAA;YAAKkK,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5DnK,OAAA;cACEyN,GAAG,EAAEpE,QAAQ,CAACvC,KAAM;cACpB4G,GAAG,EAAC,kBAAkB;cACtBxD,SAAS,EAAC,uFAAuF;cACjGyD,OAAO,EAAG5C,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACM,OAAO,GAAG,MAAM;gBAC/B;gBACA,MAAMkC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC9CF,QAAQ,CAAC1D,SAAS,GAAG,gCAAgC;gBACrD0D,QAAQ,CAACG,SAAS,GAAG,+BAA+B;gBACpDhD,CAAC,CAACC,MAAM,CAACgD,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;cAC3C;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNvK,OAAA;UACEkK,SAAS,EAAC,WAAW;UACrBkB,KAAK,EAAE;YACL8B,YAAY,EAAE5M,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UACpD,CAAE;UAAA7B,QAAA,EAEDf,mBAAmB,CAAC;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNvK,OAAA;UACEkK,SAAS,EAAC,mBAAmB;UAC7BkB,KAAK,EAAE;YACL8C,aAAa,EAAE5N,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAK;YAC1DJ,cAAc,EAAEtL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,eAAe;YACrEiB,GAAG,EAAE3M,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UAC3C,CAAE;UAAA7B,QAAA,gBAEFnK,OAAA;YACE4K,OAAO,EAAE9B,YAAa;YACtBqF,QAAQ,EAAEvL,eAAe,KAAK,CAAE;YAChCsH,SAAS,EAAG,sEACVtH,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YACHwI,KAAK,EAAE;cACLa,OAAO,EAAE3L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DS,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDR,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDJ,cAAc,EAAE;YAClB,CAAE;YAAAzB,QAAA,gBAEFnK,OAAA,CAACR,WAAW;cACV4L,KAAK,EAAE;gBACLI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,YAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERY,cAAc,gBACbnL,OAAA;YACE4K,OAAO,EAAE3F,gBAAiB;YAC1BkJ,QAAQ,EAAE7L,UAAW;YACrB4H,SAAS,EAAG,sEACV5H,UAAU,GACN,8CAA8C,GAC9C,4CACL,EAAE;YACH8I,KAAK,EAAE;cACLa,OAAO,EAAE3L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DS,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDR,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDJ,cAAc,EAAE;YAClB,CAAE;YAAAzB,QAAA,EAED7H,UAAU,gBACTtC,OAAA,CAAAE,SAAA;cAAAiK,QAAA,gBACEnK,OAAA;gBACEkK,SAAS,EAAC,sEAAsE;gBAChFkB,KAAK,EAAE;kBACLI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,iBAET;YAAA,eAAE,CAAC,gBAEHvK,OAAA,CAAAE,SAAA;cAAAiK,QAAA,gBACEnK,OAAA,CAACN,OAAO;gBACN0L,KAAK,EAAE;kBACLI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEJ;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,gBAETvK,OAAA;YACE4K,OAAO,EAAE/B,QAAS;YAClBqB,SAAS,EAAC,6GAA6G;YACvHkB,KAAK,EAAE;cACLa,OAAO,EAAE3L,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DS,QAAQ,EAAEnM,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDR,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDJ,cAAc,EAAE;YAClB,CAAE;YAAAzB,QAAA,GACH,MAEC,eAAAnK,OAAA,CAACP,YAAY;cACX2L,KAAK,EAAE;gBACLI,KAAK,EAAElL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDP,MAAM,EAAEnL,MAAM,CAAC0L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzI,EAAA,CAh/BID,QAAQ;EAAA,QACG3C,SAAS,EACPC,WAAW,EACXE,WAAW,EACXD,WAAW;AAAA;AAAAgP,EAAA,GAJxBvM,QAAQ;AAk/Bd,eAAeA,QAAQ;AAAC,IAAAuM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}