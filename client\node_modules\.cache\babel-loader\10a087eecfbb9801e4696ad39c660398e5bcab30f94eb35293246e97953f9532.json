{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbMenu2, TbX } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Refs for smooth scrolling\n  const featuresRef = useRef(null);\n  const coursesRef = useRef(null);\n  const successStoriesRef = useRef(null);\n  const contactRef = useRef(null);\n  const scrollToSection = ref => {\n    var _ref$current;\n    (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        message.error(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      message.error(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-green-200/50 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://flagcdn.com/w40/tz.png\",\n                alt: \"Tanzania Flag\",\n                className: \"w-full h-full object-cover\",\n                onError: e => {\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'flex';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\",\n                children: \"TZ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(featuresRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(coursesRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(successStoriesRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl\",\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\",\n            children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"md:hidden py-4 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(featuresRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(coursesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(successStoriesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(contactRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 pt-4 border-t border-green-200\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-gray-700 hover:text-green-600 transition-colors font-medium\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium text-center\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative overflow-hidden bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          style: {\n            background: `linear-gradient(135deg,\n              #22c55e 0%, #22c55e 25%,\n              #fbbf24 25%, #fbbf24 50%,\n              #1e40af 50%, #1e40af 75%,\n              #22c55e 75%, #22c55e 100%)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32 relative\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"text-center lg:text-left\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-green-50 to-blue-50 text-green-700 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-green-200 shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-4 rounded-sm overflow-hidden border border-gray-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-4 h-4 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent font-bold\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.3\n              },\n              className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\",\n              style: {\n                fontFamily: \"'SF Pro Display', 'Inter', sans-serif\"\n              },\n              children: [\"\\uD83C\\uDF93 Study smarter,\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\",\n                children: \"not harder.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.4\n              },\n              className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n              children: \"Access high-quality study materials, quizzes, and academic guidance\\u2014anywhere, anytime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.5\n              },\n              className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-2xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(coursesRef),\n                className: \"bg-white text-gray-900 px-8 py-4 rounded-2xl border-2 border-green-200 hover:border-green-400 hover:bg-green-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\",\n                children: \"Explore Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative bg-gradient-to-br from-green-50 via-yellow-50 to-blue-100 rounded-3xl p-8 shadow-2xl border border-green-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-12 mx-auto mb-4 rounded-lg overflow-hidden border-2 border-gray-300 shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://flagcdn.com/w40/tz.png\",\n                    alt: \"Tanzania Flag\",\n                    className: \"w-full h-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center shadow-xl\",\n                  children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-16 h-16 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\",\n                  children: \"Real students. Real success.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-700\",\n                  children: \"Join thousands of Tanzanian students achieving their academic goals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-green-700 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 10000,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 font-medium\",\n              children: \"Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-yellow-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-yellow-700 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-600 font-medium\",\n              children: \"Study Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-blue-700 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 font-medium\",\n              children: \"Courses Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-2\",\n              children: \"99%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 font-medium\",\n              children: \"Success Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: featuresRef,\n      className: \"py-20 bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\",\n            children: \"Why Choose Brainwave?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-700 max-w-3xl mx-auto\",\n            children: \"Experience the future of education with our comprehensive learning platform designed for Tanzania\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-green-500 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-green-700 mb-4 text-center\",\n              children: \"Quality Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Access comprehensive study materials designed by Tanzanian education experts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-yellow-500 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-yellow-700 mb-4 text-center\",\n              children: \"Interactive Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Test your knowledge with engaging quizzes and track your progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-blue-600 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-700 mb-4 text-center\",\n              children: \"Expert Guidance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Get personalized academic guidance from experienced Tanzanian educators\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: successStoriesRef,\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\",\n            children: \"Success Stories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-700 max-w-3xl mx-auto\",\n            children: \"Hear from Tanzanian students who transformed their academic journey with Brainwave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-green-700\",\n                  children: \"Amina Hassan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-green-600 text-sm\",\n                  children: \"Form 4 Student - Dar es Salaam\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Brainwave helped me improve my grades significantly. The study materials are excellent and designed for Tanzanian curriculum!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg border border-yellow-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"J\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-yellow-700\",\n                  children: \"John Mwalimu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-yellow-600 text-sm\",\n                  children: \"Form 6 Student - Arusha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"The interactive quizzes made learning fun and helped me understand complex topics easily. Perfect for Tanzanian students!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-blue-700\",\n                  children: \"Maria Kimaro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-600 text-sm\",\n                  children: \"University Student - Dodoma\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Thanks to Brainwave, I passed my national exams with flying colors and got into University of Dar es Salaam!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactRef,\n      className: \"py-20 bg-gradient-to-br from-green-800 via-gray-900 to-blue-900 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-14 rounded-lg overflow-hidden border-2 border-white shadow-xl\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://flagcdn.com/w40/tz.png\",\n              alt: \"Tanzania Flag\",\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold mb-4 bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"max-w-2xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                required: true,\n                rows: 6,\n                className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                placeholder: \"Your message...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\",\n                children: loading ? 'Sending...' : 'Send Message'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gradient-to-r from-green-900 via-gray-900 to-blue-900 text-white py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-6 rounded-sm overflow-hidden border border-gray-300\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://flagcdn.com/w40/tz.png\",\n                alt: \"Tanzania Flag\",\n                className: \"w-full h-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-6\",\n            children: \"Empowering students across Tanzania with quality education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-8 text-sm text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"hover:text-green-400 transition-colors\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"hover:text-blue-400 transition-colors\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"hover:text-yellow-400 transition-colors\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Brainwave Tanzania. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"g7KKyuBBBvcIXU/tEQbEtuqSJCU=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "motion", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbMenu2", "TbX", "message", "useSelector", "contactUs", "Animated<PERSON>ounter", "jsxDEV", "_jsxDEV", "Home", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "user", "state", "navigate", "featuresRef", "useRef", "coursesRef", "successStoriesRef", "contactRef", "scrollToSection", "ref", "_ref$current", "current", "scrollIntoView", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "nav", "initial", "y", "opacity", "animate", "src", "alt", "onError", "style", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "to", "div", "background", "x", "transition", "duration", "delay", "h1", "fontFamily", "p", "whileInView", "viewport", "once", "end", "TbStar", "onSubmit", "type", "onChange", "required", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\r\n\r\n\r\nconst Home = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Refs for smooth scrolling\r\n  const featuresRef = useRef(null);\r\n  const coursesRef = useRef(null);\r\n  const successStoriesRef = useRef(null);\r\n  const contactRef = useRef(null);\r\n\r\n  const scrollToSection = (ref) => {\r\n    ref.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        message.error(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\">\r\n      {/* Tanzania-Inspired Navbar */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-green-200/50 shadow-lg\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo with Tanzania Flag */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* Tanzania Flag */}\r\n              <div className=\"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\">\r\n                <img\r\n                  src=\"https://flagcdn.com/w40/tz.png\"\r\n                  alt=\"Tanzania Flag\"\r\n                  className=\"w-full h-full object-cover\"\r\n                  onError={(e) => {\r\n                    e.target.style.display = 'none';\r\n                    e.target.nextSibling.style.display = 'flex';\r\n                  }}\r\n                />\r\n                <div className=\"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\">\r\n                  TZ\r\n                </div>\r\n              </div>\r\n              <TbBrain className=\"w-8 h-8 text-green-600\" />\r\n              <span className=\"text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\">\r\n                Brainwave\r\n              </span>\r\n            </div>\r\n\r\n            {/* Desktop Menu */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button\r\n                onClick={() => scrollToSection(featuresRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Features\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(coursesRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Courses\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(successStoriesRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Success Stories\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(contactRef)}\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Contact\r\n              </button>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n              >\r\n                Login\r\n              </Link>\r\n              <Link\r\n                to=\"/register\"\r\n                className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl\"\r\n              >\r\n                Get Started\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n              className=\"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n            >\r\n              {isMobileMenuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Menu */}\r\n          {isMobileMenuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"md:hidden py-4 border-t border-gray-200\"\r\n            >\r\n              <div className=\"flex flex-col space-y-4\">\r\n                <button\r\n                  onClick={() => { scrollToSection(featuresRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Features\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(coursesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Courses\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(successStoriesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Success Stories\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(contactRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                >\r\n                  Contact\r\n                </button>\r\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-green-200\">\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"text-gray-700 hover:text-green-600 transition-colors font-medium\"\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium text-center\"\r\n                  >\r\n                    Get Started\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n      {/* Hero Section */}\r\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\">\r\n        {/* Tanzania Flag Pattern Background */}\r\n        <div className=\"absolute inset-0 opacity-5\">\r\n          <div className=\"absolute inset-0\" style={{\r\n            background: `linear-gradient(135deg,\r\n              #22c55e 0%, #22c55e 25%,\r\n              #fbbf24 25%, #fbbf24 50%,\r\n              #1e40af 50%, #1e40af 75%,\r\n              #22c55e 75%, #22c55e 100%)`\r\n          }}></div>\r\n        </div>\r\n\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32 relative\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Column - Text Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"text-center lg:text-left\"\r\n            >\r\n              {/* Badge with Tanzania Flag */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"inline-flex items-center space-x-3 bg-gradient-to-r from-green-50 to-blue-50 text-green-700 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-green-200 shadow-lg\"\r\n              >\r\n                <div className=\"w-6 h-4 rounded-sm overflow-hidden border border-gray-300\">\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                  />\r\n                </div>\r\n                <TbTrophy className=\"w-4 h-4 text-yellow-600\" />\r\n                <span className=\"bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent font-bold\">\r\n                  #1 Educational Platform in Tanzania\r\n                </span>\r\n              </motion.div>\r\n\r\n              {/* Main Heading */}\r\n              <motion.h1\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.3 }}\r\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\"\r\n                style={{ fontFamily: \"'SF Pro Display', 'Inter', sans-serif\" }}\r\n              >\r\n                🎓 Study smarter,{' '}\r\n                <span className=\"bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent\">\r\n                  not harder.\r\n                </span>\r\n              </motion.h1>\r\n\r\n              {/* Subtitle */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.4 }}\r\n                className=\"text-xl text-gray-600 mb-8 leading-relaxed\"\r\n              >\r\n                Access high-quality study materials, quizzes, and academic guidance—anywhere, anytime.\r\n              </motion.p>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.5 }}\r\n                className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\r\n              >\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-2xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\"\r\n                >\r\n                  Get Started\r\n                </Link>\r\n                <button\r\n                  onClick={() => scrollToSection(coursesRef)}\r\n                  className=\"bg-white text-gray-900 px-8 py-4 rounded-2xl border-2 border-green-200 hover:border-green-400 hover:bg-green-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\"\r\n                >\r\n                  Explore Courses\r\n                </button>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Column - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative\"\r\n            >\r\n              <div className=\"relative bg-gradient-to-br from-green-50 via-yellow-50 to-blue-100 rounded-3xl p-8 shadow-2xl border border-green-200\">\r\n                <div className=\"text-center\">\r\n                  {/* Tanzania Flag in Hero */}\r\n                  <div className=\"w-16 h-12 mx-auto mb-4 rounded-lg overflow-hidden border-2 border-gray-300 shadow-lg\">\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center shadow-xl\">\r\n                    <TbBrain className=\"w-16 h-16 text-white\" />\r\n                  </div>\r\n                  <h3 className=\"text-2xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\">\r\n                    Real students. Real success.\r\n                  </h3>\r\n                  <p className=\"text-gray-700\">Join thousands of Tanzanian students achieving their academic goals</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Quick Stats Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\"\r\n          >\r\n            {/* Stat 1 */}\r\n            <div className=\"text-center bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center\">\r\n                <TbUsers className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-green-700 mb-2\">\r\n                <AnimatedCounter end={10000} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-green-600 font-medium\">Students</p>\r\n            </div>\r\n\r\n            {/* Stat 2 */}\r\n            <div className=\"text-center bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-yellow-500 rounded-full flex items-center justify-center\">\r\n                <TbBook className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-yellow-700 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-yellow-600 font-medium\">Study Materials</p>\r\n            </div>\r\n\r\n            {/* Stat 3 */}\r\n            <div className=\"text-center bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-blue-200\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center\">\r\n                <TbSchool className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-blue-700 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-blue-600 font-medium\">Courses Available</p>\r\n            </div>\r\n\r\n            {/* Stat 4 */}\r\n            <div className=\"text-center bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center\">\r\n                <TbTrophy className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-2\">99%</div>\r\n              <p className=\"text-gray-700 font-medium\">Success Rate</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n      {/* Features Section */}\r\n      <section ref={featuresRef} className=\"py-20 bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\">\r\n              Why Choose Brainwave?\r\n            </h2>\r\n            <p className=\"text-xl text-gray-700 max-w-3xl mx-auto\">\r\n              Experience the future of education with our comprehensive learning platform designed for Tanzania\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {/* Feature 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-green-200\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-green-500 rounded-2xl flex items-center justify-center\">\r\n                <TbBook className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-green-700 mb-4 text-center\">Quality Materials</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Access comprehensive study materials designed by Tanzanian education experts\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-yellow-500 rounded-2xl flex items-center justify-center\">\r\n                <TbTrophy className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-yellow-700 mb-4 text-center\">Interactive Quizzes</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Test your knowledge with engaging quizzes and track your progress\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-blue-200\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-blue-600 rounded-2xl flex items-center justify-center\">\r\n                <TbStar className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-blue-700 mb-4 text-center\">Expert Guidance</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Get personalized academic guidance from experienced Tanzanian educators\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Success Stories Section */}\r\n      <section ref={successStoriesRef} className=\"py-20 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4\">\r\n              Success Stories\r\n            </h2>\r\n            <p className=\"text-xl text-gray-700 max-w-3xl mx-auto\">\r\n              Hear from Tanzanian students who transformed their academic journey with Brainwave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {/* Testimonial 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg border border-green-200\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  A\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-green-700\">Amina Hassan</h4>\r\n                  <p className=\"text-green-600 text-sm\">Form 4 Student - Dar es Salaam</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Brainwave helped me improve my grades significantly. The study materials are excellent and designed for Tanzanian curriculum!\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg border border-yellow-200\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  J\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-yellow-700\">John Mwalimu</h4>\r\n                  <p className=\"text-yellow-600 text-sm\">Form 6 Student - Arusha</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"The interactive quizzes made learning fun and helped me understand complex topics easily. Perfect for Tanzanian students!\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg border border-blue-200\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  M\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-blue-700\">Maria Kimaro</h4>\r\n                  <p className=\"text-blue-600 text-sm\">University Student - Dodoma</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Thanks to Brainwave, I passed my national exams with flying colors and got into University of Dar es Salaam!\"\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactRef} className=\"py-20 bg-gradient-to-br from-green-800 via-gray-900 to-blue-900 text-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          {/* Tanzania Flag in Contact Section */}\r\n          <div className=\"flex justify-center mb-8\">\r\n            <div className=\"w-20 h-14 rounded-lg overflow-hidden border-2 border-white shadow-xl\">\r\n              <img\r\n                src=\"https://flagcdn.com/w40/tz.png\"\r\n                alt=\"Tanzania Flag\"\r\n                className=\"w-full h-full object-cover\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold mb-4 bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\">\r\n              Get in Touch\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"max-w-2xl mx-auto\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              <div className=\"grid md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"Your name\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"<EMAIL>\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium mb-2\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                  rows={6}\r\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                  placeholder=\"Your message...\"\r\n                />\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\"\r\n                >\r\n                  {loading ? 'Sending...' : 'Send Message'}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-gradient-to-r from-green-900 via-gray-900 to-blue-900 text-white py-12\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center\">\r\n            <div className=\"flex items-center justify-center space-x-3 mb-4\">\r\n              {/* Tanzania Flag in Footer */}\r\n              <div className=\"w-8 h-6 rounded-sm overflow-hidden border border-gray-300\">\r\n                <img\r\n                  src=\"https://flagcdn.com/w40/tz.png\"\r\n                  alt=\"Tanzania Flag\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n              <TbBrain className=\"w-8 h-8 text-green-400\" />\r\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\">\r\n                Brainwave\r\n              </span>\r\n            </div>\r\n            <p className=\"text-gray-300 mb-6\">\r\n              Empowering students across Tanzania with quality education\r\n            </p>\r\n            <div className=\"flex justify-center space-x-8 text-sm text-gray-300\">\r\n              <Link to=\"/login\" className=\"hover:text-green-400 transition-colors\">Login</Link>\r\n              <Link to=\"/register\" className=\"hover:text-blue-400 transition-colors\">Register</Link>\r\n              <button onClick={() => scrollToSection(contactRef)} className=\"hover:text-yellow-400 transition-colors\">Contact</button>\r\n            </div>\r\n            <div className=\"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400\">\r\n              <p>&copy; 2024 Brainwave Tanzania. All rights reserved.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IAAEuB,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEb,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE2B;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAG3B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4B,WAAW,GAAGC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMC,UAAU,GAAGD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAME,iBAAiB,GAAGF,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMG,UAAU,GAAGH,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMI,eAAe,GAAIC,GAAG,IAAK;IAAA,IAAAC,YAAA;IAC/B,CAAAA,YAAA,GAAAD,GAAG,CAACE,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACrD,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEnB,IAAI;MAAEoB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCtB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGoB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBpB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMqB,IAAI,GAAG,MAAMlC,SAAS,CAACQ,QAAQ,CAAC;MACtC,IAAI0B,IAAI,CAACC,OAAO,EAAE;QAChBrC,OAAO,CAACqC,OAAO,CAAC,4BAA4B,CAAC;QAC7C1B,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEb,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLA,OAAO,CAACsC,KAAK,CAACF,IAAI,CAACpC,OAAO,IAAI,uBAAuB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,0CAA0C,CAAC;IAC3D;IACAvB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKkC,SAAS,EAAC,uEAAuE;IAAAC,QAAA,gBAEpFnC,OAAA,CAACb,MAAM,CAACiD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,uFAAuF;MAAAC,QAAA,eAEjGnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnC,OAAA;UAAKkC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDnC,OAAA;YAAKkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1CnC,OAAA;cAAKkC,SAAS,EAAC,qEAAqE;cAAAC,QAAA,gBAClFnC,OAAA;gBACEyC,GAAG,EAAC,gCAAgC;gBACpCC,GAAG,EAAC,eAAe;gBACnBR,SAAS,EAAC,4BAA4B;gBACtCS,OAAO,EAAGjB,CAAC,IAAK;kBACdA,CAAC,CAACE,MAAM,CAACgB,KAAK,CAACC,OAAO,GAAG,MAAM;kBAC/BnB,CAAC,CAACE,MAAM,CAACkB,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;gBAC7C;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFlD,OAAA;gBAAKkC,SAAS,EAAC,2IAA2I;gBAAAC,QAAA,EAAC;cAE3J;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA,CAACZ,OAAO;cAAC8C,SAAS,EAAC;YAAwB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ClD,OAAA;cAAMkC,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EAAC;YAE9G;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlD,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnC,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACL,WAAW,CAAE;cAC5CoB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACH,UAAU,CAAE;cAC3CkB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACF,iBAAiB,CAAE;cAClDiB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACD,UAAU,CAAE;cAC3CgB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlD,OAAA;YAAKkC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnC,OAAA,CAACf,IAAI;cACHmE,EAAE,EAAC,QAAQ;cACXlB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlD,OAAA,CAACf,IAAI;cACHmE,EAAE,EAAC,WAAW;cACdlB,SAAS,EAAC,wLAAwL;cAAAC,QAAA,EACnM;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlD,OAAA;YACEmD,OAAO,EAAEA,CAAA,KAAM/C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtD+B,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAEnEhC,gBAAgB,gBAAGH,OAAA,CAACN,GAAG;cAACwC,SAAS,EAAC;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACP,OAAO;cAACyC,SAAS,EAAC;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL/C,gBAAgB,iBACfH,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAEnDnC,OAAA;YAAKkC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnC,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACL,WAAW,CAAC;gBAAEV,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC7E8B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACH,UAAU,CAAC;gBAAEZ,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E8B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACF,iBAAiB,CAAC;gBAAEb,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cACnF8B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM;gBAAEhC,eAAe,CAACD,UAAU,CAAC;gBAAEd,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E8B,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACvF;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA;cAAKkC,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrEnC,OAAA,CAACf,IAAI;gBACHmE,EAAE,EAAC,QAAQ;gBACXlB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAC7E;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlD,OAAA,CAACf,IAAI;gBACHmE,EAAE,EAAC,WAAW;gBACdlB,SAAS,EAAC,0KAA0K;gBAAAC,QAAA,EACrL;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEblD,OAAA;MAASkC,SAAS,EAAC,mFAAmF;MAAAC,QAAA,gBAEpGnC,OAAA;QAAKkC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnC,OAAA;UAAKkC,SAAS,EAAC,kBAAkB;UAACU,KAAK,EAAE;YACvCU,UAAU,EAAG;AACzB;AACA;AACA;AACA;UACU;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlD,OAAA;QAAKkC,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EnC,OAAA;UAAKkC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtDnC,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCf,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BvB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAGpCnC,OAAA,CAACb,MAAM,CAACkE,GAAG;cACThB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BkB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CxB,SAAS,EAAC,+KAA+K;cAAAC,QAAA,gBAEzLnC,OAAA;gBAAKkC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,eACxEnC,OAAA;kBACEyC,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBR,SAAS,EAAC;gBAA4B;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlD,OAAA,CAACV,QAAQ;gBAAC4C,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDlD,OAAA;gBAAMkC,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,EAAC;cAEtG;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACwE,EAAE;cACRtB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BkB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CxB,SAAS,EAAC,6EAA6E;cACvFU,KAAK,EAAE;gBAAEgB,UAAU,EAAE;cAAwC,CAAE;cAAAzB,QAAA,GAChE,6BACkB,EAAC,GAAG,eACrBnC,OAAA;gBAAMkC,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,EAAC;cAE5F;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGZlD,OAAA,CAACb,MAAM,CAAC0E,CAAC;cACPxB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BkB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CxB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXlD,OAAA,CAACb,MAAM,CAACkE,GAAG;cACThB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BkB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CxB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3EnC,OAAA,CAACf,IAAI;gBACHmE,EAAE,EAAC,WAAW;gBACdlB,SAAS,EAAC,kNAAkN;gBAAAC,QAAA,EAC7N;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlD,OAAA;gBACEmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACH,UAAU,CAAE;gBAC3CkB,SAAS,EAAC,6MAA6M;gBAAAC,QAAA,EACxN;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CxB,SAAS,EAAC,UAAU;YAAAC,QAAA,eAEpBnC,OAAA;cAAKkC,SAAS,EAAC,uHAAuH;cAAAC,QAAA,eACpInC,OAAA;gBAAKkC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAE1BnC,OAAA;kBAAKkC,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,eACnGnC,OAAA;oBACEyC,GAAG,EAAC,gCAAgC;oBACpCC,GAAG,EAAC,eAAe;oBACnBR,SAAS,EAAC;kBAA4B;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlD,OAAA;kBAAKkC,SAAS,EAAC,4HAA4H;kBAAAC,QAAA,eACzInC,OAAA,CAACZ,OAAO;oBAAC8C,SAAS,EAAC;kBAAsB;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNlD,OAAA;kBAAIkC,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlD,OAAA;kBAAGkC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAmE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlD,OAAA;MAASkC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDnC,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCkB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAGjDnC,OAAA;YAAKkC,SAAS,EAAC,8IAA8I;YAAAC,QAAA,gBAC3JnC,OAAA;cAAKkC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGnC,OAAA,CAACT,OAAO;gBAAC2C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNlD,OAAA;cAAKkC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnC,OAAA,CAACF,eAAe;gBAACmE,GAAG,EAAE,KAAM;gBAACR,QAAQ,EAAE;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KACjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGNlD,OAAA;YAAKkC,SAAS,EAAC,iJAAiJ;YAAAC,QAAA,gBAC9JnC,OAAA;cAAKkC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGnC,OAAA,CAACX,MAAM;gBAAC6C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNlD,OAAA;cAAKkC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtDnC,OAAA,CAACF,eAAe;gBAACmE,GAAG,EAAE,EAAG;gBAACR,QAAQ,EAAE;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAGNlD,OAAA;YAAKkC,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJnC,OAAA;cAAKkC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FnC,OAAA,CAACR,QAAQ;gBAAC0C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNlD,OAAA;cAAKkC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDnC,OAAA,CAACF,eAAe;gBAACmE,GAAG,EAAE,EAAG;gBAACR,QAAQ,EAAE;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAiB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAGNlD,OAAA;YAAKkC,SAAS,EAAC,0JAA0J;YAAAC,QAAA,gBACvKnC,OAAA;cAAKkC,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAC/HnC,OAAA,CAACV,QAAQ;gBAAC4C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNlD,OAAA;cAAKkC,SAAS,EAAC,mGAAmG;cAAAC,QAAA,EAAC;YAAG;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5HlD,OAAA;cAAGkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAY;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVlD,OAAA;MAASoB,GAAG,EAAEN,WAAY;MAACoB,SAAS,EAAC,gEAAgE;MAAAC,QAAA,eACnGnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnC,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCkB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BnC,OAAA;YAAIkC,SAAS,EAAC,mGAAmG;YAAAC,QAAA,EAAC;UAElH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlD,OAAA;YAAGkC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEblD,OAAA;UAAKkC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAExCnC,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAExGnC,OAAA;cAAKkC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FnC,OAAA,CAACX,MAAM;gBAAC6C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNlD,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAAiB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFlD,OAAA;cAAGkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,+FAA+F;YAAAC,QAAA,gBAEzGnC,OAAA;cAAKkC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGnC,OAAA,CAACV,QAAQ;gBAAC4C,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNlD,OAAA;cAAIkC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAAmB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3FlD,OAAA;cAAGkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvGnC,OAAA;cAAKkC,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FnC,OAAA,CAACkE,MAAM;gBAAChC,SAAS,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNlD,OAAA;cAAIkC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrFlD,OAAA;cAAGkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlD,OAAA;MAASoB,GAAG,EAAEH,iBAAkB;MAACiB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACzDnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnC,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCkB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BnC,OAAA;YAAIkC,SAAS,EAAC,mGAAmG;YAAAC,QAAA,EAAC;UAElH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlD,OAAA;YAAGkC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEblD,OAAA;UAAKkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAEvDnC,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,gGAAgG;YAAAC,QAAA,gBAE1GnC,OAAA;cAAKkC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCnC,OAAA;gBAAKkC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,EAAC;cAE3G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAKkC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnC,OAAA;kBAAIkC,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DlD,OAAA;kBAAGkC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,mGAAmG;YAAAC,QAAA,gBAE7GnC,OAAA;cAAKkC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCnC,OAAA;gBAAKkC,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EAAC;cAE5G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAKkC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnC,OAAA;kBAAIkC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3DlD,OAAA;kBAAGkC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGblD,OAAA,CAACb,MAAM,CAACkE,GAAG;YACThB,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BwB,WAAW,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCkB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CK,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB9B,SAAS,EAAC,6FAA6F;YAAAC,QAAA,gBAEvGnC,OAAA;cAAKkC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCnC,OAAA;gBAAKkC,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,EAAC;cAE1G;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAKkC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnC,OAAA;kBAAIkC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzDlD,OAAA;kBAAGkC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAGkC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlD,OAAA;MAASoB,GAAG,EAAEF,UAAW;MAACgB,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eAC9GnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDnC,OAAA;UAAKkC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCnC,OAAA;YAAKkC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFnC,OAAA;cACEyC,GAAG,EAAC,gCAAgC;cACpCC,GAAG,EAAC,eAAe;cACnBR,SAAS,EAAC;YAA4B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCkB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BnC,OAAA;YAAIkC,SAAS,EAAC,mGAAmG;YAAAC,QAAA,EAAC;UAElH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlD,OAAA;YAAGkC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEblD,OAAA,CAACb,MAAM,CAACkE,GAAG;UACThB,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BwB,WAAW,EAAE;YAAEvB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCkB,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CK,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB9B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BnC,OAAA;YAAMmE,QAAQ,EAAEtC,YAAa;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDnC,OAAA;cAAKkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCnC,OAAA;gBAAAmC,QAAA,gBACEnC,OAAA;kBAAOkC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9DlD,OAAA;kBACEoE,IAAI,EAAC,MAAM;kBACX7D,IAAI,EAAC,MAAM;kBACXoB,KAAK,EAAEtB,QAAQ,CAACE,IAAK;kBACrB8D,QAAQ,EAAE5C,YAAa;kBACvB6C,QAAQ;kBACRpC,SAAS,EAAC,qIAAqI;kBAC/IqC,WAAW,EAAC;gBAAW;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlD,OAAA;gBAAAmC,QAAA,gBACEnC,OAAA;kBAAOkC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/DlD,OAAA;kBACEoE,IAAI,EAAC,OAAO;kBACZ7D,IAAI,EAAC,OAAO;kBACZoB,KAAK,EAAEtB,QAAQ,CAACG,KAAM;kBACtB6D,QAAQ,EAAE5C,YAAa;kBACvB6C,QAAQ;kBACRpC,SAAS,EAAC,qIAAqI;kBAC/IqC,WAAW,EAAC;gBAAgB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAOkC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjElD,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACdoB,KAAK,EAAEtB,QAAQ,CAACV,OAAQ;gBACxB0E,QAAQ,EAAE5C,YAAa;gBACvB6C,QAAQ;gBACRE,IAAI,EAAE,CAAE;gBACRtC,SAAS,EAAC,qIAAqI;gBAC/IqC,WAAW,EAAC;cAAiB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlD,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BnC,OAAA;gBACEoE,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAEhE,OAAQ;gBAClByB,SAAS,EAAC,qNAAqN;gBAAAC,QAAA,EAE9N1B,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlD,OAAA;MAAQkC,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eAC3FnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDnC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnC,OAAA;YAAKkC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAE9DnC,OAAA;cAAKkC,SAAS,EAAC,2DAA2D;cAAAC,QAAA,eACxEnC,OAAA;gBACEyC,GAAG,EAAC,gCAAgC;gBACpCC,GAAG,EAAC,eAAe;gBACnBR,SAAS,EAAC;cAA4B;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlD,OAAA,CAACZ,OAAO;cAAC8C,SAAS,EAAC;YAAwB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ClD,OAAA;cAAMkC,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAAC;YAE/G;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNlD,OAAA;YAAGkC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlD,OAAA;YAAKkC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEnC,OAAA,CAACf,IAAI;cAACmE,EAAE,EAAC,QAAQ;cAAClB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjFlD,OAAA,CAACf,IAAI;cAACmE,EAAE,EAAC,WAAW;cAAClB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFlD,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACD,UAAU,CAAE;cAACgB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH,CAAC,eACNlD,OAAA;YAAKkC,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3EnC,OAAA;cAAAmC,QAAA,EAAG;YAAoD;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChD,EAAA,CAznBID,IAAI;EAAA,QAISL,WAAW,EACXV,WAAW;AAAA;AAAAwF,EAAA,GALxBzE,IAAI;AA2nBV,eAAeA,IAAI;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}