/* ===== RESPONSIVE HUB PAGE ===== */

/* ===== BASE STYLES ===== */
.hub-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #e2e8f0 100%);
  padding: 0.75rem;
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
  position: relative;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Mobile First Responsive Padding */
@media (min-width: 640px) {
  .hub-container {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .hub-container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hub-container {
    padding: 2rem;
  }
}

.hub-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(0, 123, 255, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(0, 86, 210, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 60%);
  z-index: 0;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(10px) translateY(-10px); }
  50% { transform: translateX(-5px) translateY(10px); }
  75% { transform: translateX(-10px) translateY(-5px); }
}

.hub-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  overflow-y: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  padding: 0 0.5rem;
}

/* Responsive Content Padding */
@media (min-width: 640px) {
  .hub-content {
    padding: 0 1rem;
  }
}

@media (min-width: 768px) {
  .hub-content {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hub-content {
    padding: 0 2rem;
  }
}

/* ===== HEADER SECTION ===== */
.hub-header {
  text-align: center;
  margin-bottom: 1rem;
  padding: 1rem;
  position: relative;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Header */
@media (min-width: 640px) {
  .hub-header {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
  }
}

@media (min-width: 768px) {
  .hub-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hub-header {
    margin-bottom: 2.5rem;
    padding: 2rem;
  }
}

/* ===== LOGOUT BUTTON STYLES ===== */
.hub-logout-container {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 10;
}

.hub-logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  backdrop-filter: blur(10px);
}

.hub-logout-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  transform: translateY(-2px);
}

.hub-logout-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.logout-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.hub-logout-btn:hover .logout-icon {
  transform: rotate(-10deg);
}

.logout-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Responsive logout button */
@media (max-width: 768px) {
  .hub-logout-container {
    top: 1rem;
    right: 1rem;
  }

  .hub-logout-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .logout-text {
    display: none;
  }

  .logout-icon {
    font-size: 1.1rem;
  }
}

.hub-brainwave-title {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
  text-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
}

.hub-welcome {
  font-size: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  letter-spacing: -0.025em;
  text-align: center;
}

/* Responsive Welcome Text */
@media (min-width: 480px) {
  .hub-welcome {
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }
}

@media (min-width: 640px) {
  .hub-welcome {
    font-size: 2rem;
  }
}

@media (min-width: 768px) {
  .hub-welcome {
    font-size: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .hub-welcome {
    font-size: 2.5rem;
  }
}

.hub-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  text-align: center;
  font-weight: 500;
}

.hub-quote {
  font-size: 0.875rem;
  color: #374151;
  font-style: italic;
  padding: 0.75rem 1.5rem;
  background: rgba(0, 123, 255, 0.05);
  border-left: 3px solid #007BFF;
  border-radius: 0.5rem;
  margin: 0 auto;
  max-width: 500px;
}

/* ===== ENHANCED RESPONSIVE GRID LAYOUT ===== */
.hub-grid-container {
  margin-bottom: 3rem;
}

.hub-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Progressive Enhancement for Larger Screens */
@media (min-width: 480px) {
  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 640px) {
  .hub-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
}

@media (min-width: 1024px) {
  .hub-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
  }
}

/* Mobile: Single column */
@media (max-width: 640px) {
  .hub-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .hub-container {
    padding: 1.5rem 0.75rem;
  }

  .hub-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .hub-welcome {
    font-size: 1.5rem;
  }

  .hub-subtitle {
    font-size: 1rem;
  }

  .hub-quote {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }

  .hub-card {
    padding: 2rem 1.5rem;
  }

  .hub-card-icon {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }

  .hub-card-title {
    font-size: 1.25rem;
  }

  .hub-card-description {
    font-size: 0.9rem;
  }
}

/* Tablet: Two columns */
@media (min-width: 641px) and (max-width: 1024px) {
  .hub-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.75rem;
  }

  .hub-container {
    padding: 1.75rem 1rem;
  }

  .hub-header {
    padding: 2rem 1.5rem;
    margin-bottom: 2.5rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
  }

  .hub-card {
    padding: 2.25rem 2rem;
  }

  .hub-card-icon {
    width: 80px;
    height: 80px;
    font-size: 2.125rem;
  }
}

/* Desktop: Optimized layout */
@media (min-width: 1025px) {
  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
  }

  .hub-container {
    padding: 2rem 1.5rem;
  }

  .hub-header {
    padding: 2.5rem 2rem;
    margin-bottom: 3rem;
  }
}

/* ===== CARD STYLES ===== */
.hub-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 2rem;
  padding: 2.5rem;
  text-align: center;
  box-shadow:
    0 20px 25px -5px rgba(0, 123, 255, 0.1),
    0 10px 10px -5px rgba(0, 123, 255, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 123, 255, 0.08);
  backdrop-filter: blur(20px);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.hub-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);
  transform: scaleX(0);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2rem 2rem 0 0;
}

.hub-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(0, 86, 210, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 2rem;
  z-index: 0;
}

.hub-card:hover::before {
  transform: scaleX(1);
}

.hub-card:hover::after {
  opacity: 1;
}

.hub-card:hover {
  transform: translateY(-12px) scale(1.03) rotateX(2deg);
  box-shadow:
    0 32px 64px -12px rgba(0, 123, 255, 0.25),
    0 20px 25px -5px rgba(0, 123, 255, 0.1),
    0 0 0 1px rgba(0, 123, 255, 0.1);
  border-color: rgba(0, 123, 255, 0.2);
}

.hub-card > * {
  position: relative;
  z-index: 1;
}

.hub-card-icon {
  width: 90px;
  height: 90px;
  margin: 0 auto 2rem;
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.25rem;
  color: white;
  box-shadow:
    0 15px 25px -5px rgba(0, 123, 255, 0.4),
    0 8px 10px -6px rgba(0, 123, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.hub-card-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hub-card:hover .hub-card-icon {
  transform: scale(1.15) rotate(8deg) translateY(-5px);
  box-shadow:
    0 25px 50px -12px rgba(0, 123, 255, 0.5),
    0 15px 25px -5px rgba(0, 123, 255, 0.4);
}

.hub-card:hover .hub-card-icon::before {
  opacity: 1;
}

.hub-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.hub-card-description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* ===== ANIMATIONS ===== */

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Bounce animation */
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

/* Stagger animation delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }

/* Float animation for icons */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Bounce animation for icons */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25%);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* ===== BOTTOM DECORATION ===== */
.hub-bottom-decoration {
  text-align: center;
  margin-top: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 1.5rem;
  backdrop-filter: blur(10px);
}

.hub-bottom-decoration .decoration-content {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  color: #6b7280;
  font-size: 1.125rem;
  font-weight: 600;
}

.hub-bottom-decoration .decoration-icon {
  font-size: 1.5rem;
  color: #007BFF;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  .hub-container {
    padding: 0.75rem 0.5rem;
  }

  .hub-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
    margin-bottom: 0.75rem;
  }

  .hub-subtitle {
    font-size: 1rem;
    margin-bottom: 0;
  }

  .hub-quote {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    margin: 0;
    max-width: 100%;
  }

  .hub-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .hub-card {
    padding: 1.5rem;
    border-radius: 1rem;
    min-height: 120px; /* Touch-friendly minimum */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .hub-card-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .hub-card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .hub-card-description {
    font-size: 0.875rem;
  }

  .hub-bottom-decoration {
    margin-top: 2rem;
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .hub-bottom-decoration .decoration-content {
    flex-direction: column;
    gap: 0.5rem;
    font-size: 1rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 1.25rem;
  }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .hub-container {
    padding: 1rem 0.75rem;
  }

  .hub-header {
    padding: 1.25rem;
    margin-bottom: 1.75rem;
  }

  .hub-welcome {
    font-size: 2rem;
  }

  .hub-subtitle {
    font-size: 1.0625rem;
  }

  .hub-quote {
    font-size: 0.8125rem;
    padding: 0.625rem 1rem;
  }

  .hub-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .hub-card {
    padding: 1.75rem;
  }

  .hub-card-icon {
    width: 70px;
    height: 70px;
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .hub-card-title {
    font-size: 1.375rem;
  }

  .hub-card-description {
    font-size: 0.9375rem;
  }

  .hub-bottom-decoration {
    margin-top: 3rem;
    padding: 1.75rem;
  }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .hub-container {
    padding: 2rem 1.5rem;
  }

  .hub-header {
    padding: 2rem;
    margin-bottom: 3rem;
  }

  .hub-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.75rem;
  }

  .hub-card {
    padding: 2rem;
  }

  .hub-card-icon {
    width: 75px;
    height: 75px;
    font-size: 1.875rem;
  }
}

/* Large Desktop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .hub-container {
    padding: 2.5rem 2rem;
  }

  .hub-header {
    padding: 2.5rem;
    margin-bottom: 3.5rem;
  }

  .hub-welcome {
    font-size: 3.5rem;
  }

  .hub-subtitle {
    font-size: 1.375rem;
  }

  .hub-quote {
    font-size: 1.1875rem;
    padding: 1.25rem 2.5rem;
  }

  .hub-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .hub-card {
    padding: 2.5rem;
  }

  .hub-card-icon {
    width: 85px;
    height: 85px;
    font-size: 2.125rem;
    margin-bottom: 1.75rem;
  }

  .hub-card-title {
    font-size: 1.625rem;
    margin-bottom: 1rem;
  }

  .hub-card-description {
    font-size: 1.0625rem;
  }

  .hub-bottom-decoration {
    margin-top: 4.5rem;
    padding: 2.5rem;
  }

  .hub-bottom-decoration .decoration-content {
    font-size: 1.25rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 1.75rem;
  }
}

/* Ultra-Wide Screens (1441px+) */
@media (min-width: 1441px) {
  .hub-container {
    padding: 3rem 2.5rem;
  }

  .hub-content {
    max-width: 1600px;
  }

  .hub-header {
    padding: 3rem;
    margin-bottom: 4rem;
  }

  .hub-welcome {
    font-size: 4rem;
  }

  .hub-subtitle {
    font-size: 1.5rem;
  }

  .hub-quote {
    font-size: 1.25rem;
    padding: 1.5rem 3rem;
    max-width: 800px;
  }

  .hub-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }

  .hub-card {
    padding: 3rem;
  }

  .hub-card-icon {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .hub-card-title {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }

  .hub-card-description {
    font-size: 1.125rem;
  }

  .hub-bottom-decoration {
    margin-top: 5rem;
    padding: 3rem;
  }

  .hub-bottom-decoration .decoration-content {
    font-size: 1.375rem;
    gap: 1.5rem;
  }

  .hub-bottom-decoration .decoration-icon {
    font-size: 2rem;
  }
}

/* Bounce animation */
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

/* Rotate animation */
@keyframes rotate-gentle {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.animate-rotate-gentle {
  animation: rotate-gentle 4s ease-in-out infinite;
}

/* Scale pulse */
@keyframes scale-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-scale-pulse {
  animation: scale-pulse 3s ease-in-out infinite;
}

/* ===== ADDITIONAL RESPONSIVE STYLES ===== */

/* Landscape Mobile (max-height: 500px and orientation: landscape) */
@media (max-height: 500px) and (orientation: landscape) {
  .hub-container {
    padding: 1rem 0.75rem;
  }

  .hub-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .hub-subtitle {
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
  }

  .hub-quote {
    font-size: 0.8rem;
    padding: 0.75rem 1rem;
  }

  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .hub-card {
    padding: 1rem;
  }

  .hub-card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .hub-card-title {
    font-size: 1rem;
    margin-bottom: 0.375rem;
  }

  .hub-card-description {
    font-size: 0.8rem;
  }

  .hub-bottom-decoration {
    margin-top: 1.5rem;
    padding: 1rem;
  }
}

/* Very Small Screens (max-width: 320px) */
@media (max-width: 320px) {
  .hub-container {
    padding: 0.75rem 0.5rem;
  }

  .hub-header {
    padding: 1rem;
  }

  .hub-welcome {
    font-size: 1.75rem;
  }

  .hub-subtitle {
    font-size: 0.875rem;
  }

  .hub-quote {
    font-size: 0.8125rem;
    padding: 0.75rem;
  }

  .hub-card {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }

  .hub-card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .hub-card-title {
    font-size: 1.125rem;
  }

  .hub-card-description {
    font-size: 0.8125rem;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */

/* Focus states for accessibility */
.hub-card:focus {
  outline: 3px solid #007BFF;
  outline-offset: 2px;
}

.hub-card:focus:not(:focus-visible) {
  outline: none;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .hub-card,
  .hub-card-icon,
  .animate-fadeInUp,
  .animate-bounce-gentle {
    animation: none !important;
    transition: none !important;
  }

  .hub-card:hover {
    transform: none !important;
  }
}

/* Gradient background animation */
@keyframes gradient-bg {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-bg {
  background-size: 400% 400%;
  animation: gradient-bg 8s ease infinite;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Stagger animation delays */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-600 {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .animate-blob {
    animation-duration: 10s;
  }
  
  .hub-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
}
