import React, { useState } from "react";
import "./index.css";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Tb<PERSON>rain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbSchool
} from "react-icons/tb";
import { message } from "antd";
import { useSelector } from "react-redux";
import { contactUs } from "../../../apicalls/users";
import AnimatedCounter from "../../../components/AnimatedCounter";
import Logo from '../../../assets/logo.png';


const Home = () => {
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  return (
    <div className="bg-gradient-to-br from-white to-blue-50 text-gray-900 min-h-screen">
      {/* Navbar */}
      <nav className="flex justify-center items-center px-6 py-4 shadow-md bg-white sticky top-0 z-50">
        <div className="flex items-center space-x-3">
          {/* Tanzania Flag */}
          <div className="w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm">
            <img
              src="https://flagcdn.com/w40/tz.png"
              alt="Tanzania Flag"
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
            <div className="w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold">
              TZ
            </div>
          </div>

          {/* BRAINWAVE Logo */}
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-black">BRAIN</span>
            <span className="text-2xl font-bold text-blue-600">WAVE</span>
          </div>

          {/* Logo Image - Smaller and cropped to show only picture */}
          <div className="w-5 h-5 overflow-hidden rounded-sm">
            <img
              src={Logo}
              alt="Brainwave Logo"
              className="w-full h-auto object-cover"
              style={{
                objectPosition: 'center top',
                transform: 'scale(1.2)',
                marginTop: '-2px'
              }}
            />
          </div>
        </div>
      </nav>
      {/* Hero Section */}
      <section className="text-center px-6 py-20 max-w-4xl mx-auto">
        <div className="text-sm font-medium inline-block px-4 py-1 mb-4 bg-yellow-300 text-black rounded-full">
          🎓 <span className="bg-yellow-400 px-2 py-1 rounded font-bold">Study Smarter</span>
        </div>
        <h1 className="text-5xl font-extrabold text-blue-800 leading-tight mb-4">
          Learn Anywhere, Anytime
        </h1>
        <p className="text-lg text-gray-700 mb-6">
          Explore courses, study materials, quizzes, and boost your academic success with Tanzania's leading learning platform.
        </p>
        <div className="space-x-4">
          <Link
            to="/register"
            className="px-6 py-3 bg-green-500 text-white rounded-full shadow hover:bg-green-600 inline-block"
          >
            Get Started
          </Link>
          <button className="px-6 py-3 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50">
            Explore
          </button>
        </div>
      </section>

      {/* Stats Section */}
      <section className="grid grid-cols-2 sm:grid-cols-4 gap-6 px-6 py-12 text-center max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow p-6">
          <p className="text-2xl font-bold text-green-600">
            <AnimatedCounter end={10000} duration={2000} />+
          </p>
          <p className="text-sm text-gray-600">Students</p>
        </div>
        <div className="bg-white rounded-xl shadow p-6">
          <p className="text-2xl font-bold text-blue-600">
            <AnimatedCounter end={50} duration={2000} />+
          </p>
          <p className="text-sm text-gray-600">Courses</p>
        </div>
        <div className="bg-white rounded-xl shadow p-6">
          <p className="text-2xl font-bold text-yellow-500">99%</p>
          <p className="text-sm text-gray-600">Success Rate</p>
        </div>
        <div className="bg-white rounded-xl shadow p-6">
          <p className="text-2xl font-bold text-black">
            <AnimatedCounter end={52} duration={2000} />+
          </p>
          <p className="text-sm text-gray-600">Study Materials</p>
        </div>
      </section>

      {/* Banner Image Section */}
      <section className="px-6 py-12">
        <div className="overflow-hidden rounded-2xl shadow-lg">
          <img
            src="https://images.unsplash.com/photo-1588072432836-e10032774350?auto=format&fit=crop&w=1500&q=80"
            alt="Students studying"
            className="w-full h-80 object-cover"
          />
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white text-center py-6 mt-10">
        <div className="flex items-center justify-center space-x-3 mb-4">
          {/* Tanzania Flag in Footer */}
          <div className="w-8 h-6 rounded-sm overflow-hidden border border-gray-300">
            <img
              src="https://flagcdn.com/w40/tz.png"
              alt="Tanzania Flag"
              className="w-full h-full object-cover"
            />
          </div>
          <span className="text-lg font-bold">Brainwave Tanzania</span>
        </div>
        <p>&copy; 2025 Brainwave Tanzania. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default Home;
