import React, { useState, useRef } from "react";
import "./index.css";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowRight,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbSchool,
  TbStar,
  TbLogin,
  TbMenu2,
  TbX
} from "react-icons/tb";
import { message } from "antd";
import { useSelector } from "react-redux";
import { contactUs } from "../../../apicalls/users";
import AnimatedCounter from "../../../components/AnimatedCounter";


const Home = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  // Refs for smooth scrolling
  const featuresRef = useRef(null);
  const coursesRef = useRef(null);
  const successStoriesRef = useRef(null);
  const contactRef = useRef(null);

  const scrollToSection = (ref) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        message.error(data.message || "Something went wrong.");
      }
    } catch (error) {
      message.error("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50">
      {/* Tanzania-Inspired Navbar */}
      <motion.nav
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-green-200/50 shadow-lg"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo with Tanzania Flag */}
            <div className="flex items-center space-x-3">
              {/* Tanzania Flag */}
              <div className="w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm">
                <img
                  src="https://flagcdn.com/w40/tz.png"
                  alt="Tanzania Flag"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
                <div className="w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold">
                  TZ
                </div>
              </div>
              <TbBrain className="w-8 h-8 text-green-600" />
              <span className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Brainwave
              </span>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection(featuresRef)}
                className="text-gray-700 hover:text-green-600 transition-colors font-medium"
              >
                Features
              </button>
              <button
                onClick={() => scrollToSection(coursesRef)}
                className="text-gray-700 hover:text-green-600 transition-colors font-medium"
              >
                Courses
              </button>
              <button
                onClick={() => scrollToSection(successStoriesRef)}
                className="text-gray-700 hover:text-green-600 transition-colors font-medium"
              >
                Success Stories
              </button>
              <button
                onClick={() => scrollToSection(contactRef)}
                className="text-gray-700 hover:text-green-600 transition-colors font-medium"
              >
                Contact
              </button>
            </div>

            {/* Action Buttons */}
            <div className="hidden md:flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-700 hover:text-green-600 transition-colors font-medium"
              >
                Login
              </Link>
              <Link
                to="/register"
                className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
              >
                Get Started
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100"
            >
              {isMobileMenuOpen ? <TbX className="w-6 h-6" /> : <TbMenu2 className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="md:hidden py-4 border-t border-gray-200"
            >
              <div className="flex flex-col space-y-4">
                <button
                  onClick={() => { scrollToSection(featuresRef); setIsMobileMenuOpen(false); }}
                  className="text-left text-gray-700 hover:text-green-600 transition-colors font-medium"
                >
                  Features
                </button>
                <button
                  onClick={() => { scrollToSection(coursesRef); setIsMobileMenuOpen(false); }}
                  className="text-left text-gray-700 hover:text-green-600 transition-colors font-medium"
                >
                  Courses
                </button>
                <button
                  onClick={() => { scrollToSection(successStoriesRef); setIsMobileMenuOpen(false); }}
                  className="text-left text-gray-700 hover:text-green-600 transition-colors font-medium"
                >
                  Success Stories
                </button>
                <button
                  onClick={() => { scrollToSection(contactRef); setIsMobileMenuOpen(false); }}
                  className="text-left text-gray-700 hover:text-green-600 transition-colors font-medium"
                >
                  Contact
                </button>
                <div className="flex flex-col space-y-2 pt-4 border-t border-green-200">
                  <Link
                    to="/login"
                    className="text-gray-700 hover:text-green-600 transition-colors font-medium"
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-2 rounded-full hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-medium text-center"
                  >
                    Get Started
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50">
        {/* Tanzania Flag Pattern Background */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            background: `linear-gradient(135deg,
              #22c55e 0%, #22c55e 25%,
              #fbbf24 25%, #fbbf24 50%,
              #1e40af 50%, #1e40af 75%,
              #22c55e 75%, #22c55e 100%)`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center lg:text-left"
            >
              {/* Badge with Tanzania Flag */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center space-x-3 bg-gradient-to-r from-green-50 to-blue-50 text-green-700 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-green-200 shadow-lg"
              >
                <div className="w-6 h-4 rounded-sm overflow-hidden border border-gray-300">
                  <img
                    src="https://flagcdn.com/w40/tz.png"
                    alt="Tanzania Flag"
                    className="w-full h-full object-cover"
                  />
                </div>
                <TbTrophy className="w-4 h-4 text-yellow-600" />
                <span className="bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent font-bold">
                  #1 Educational Platform in Tanzania
                </span>
              </motion.div>

              {/* Main Heading */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6"
                style={{ fontFamily: "'SF Pro Display', 'Inter', sans-serif" }}
              >
                🎓 Study smarter,{' '}
                <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  not harder.
                </span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-xl text-gray-600 mb-8 leading-relaxed"
              >
                Access high-quality study materials, quizzes, and academic guidance—anywhere, anytime.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              >
                <Link
                  to="/register"
                  className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-2xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105"
                >
                  Get Started
                </Link>
                <button
                  onClick={() => scrollToSection(coursesRef)}
                  className="bg-white text-gray-900 px-8 py-4 rounded-2xl border-2 border-green-200 hover:border-green-400 hover:bg-green-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105"
                >
                  Explore Courses
                </button>
              </motion.div>
            </motion.div>

            {/* Right Column - Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-green-50 via-yellow-50 to-blue-100 rounded-3xl p-8 shadow-2xl border border-green-200">
                <div className="text-center">
                  {/* Tanzania Flag in Hero */}
                  <div className="w-16 h-12 mx-auto mb-4 rounded-lg overflow-hidden border-2 border-gray-300 shadow-lg">
                    <img
                      src="https://flagcdn.com/w40/tz.png"
                      alt="Tanzania Flag"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center shadow-xl">
                    <TbBrain className="w-16 h-16 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4">
                    Real students. Real success.
                  </h3>
                  <p className="text-gray-700">Join thousands of Tanzanian students achieving their academic goals</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Quick Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {/* Stat 1 */}
            <div className="text-center bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center">
                <TbUsers className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-green-700 mb-2">
                <AnimatedCounter end={10000} duration={2000} />+
              </div>
              <p className="text-green-600 font-medium">Students</p>
            </div>

            {/* Stat 2 */}
            <div className="text-center bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-yellow-500 rounded-full flex items-center justify-center">
                <TbBook className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-yellow-700 mb-2">
                <AnimatedCounter end={50} duration={2000} />+
              </div>
              <p className="text-yellow-600 font-medium">Study Materials</p>
            </div>

            {/* Stat 3 */}
            <div className="text-center bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-blue-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center">
                <TbSchool className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-blue-700 mb-2">
                <AnimatedCounter end={50} duration={2000} />+
              </div>
              <p className="text-blue-600 font-medium">Courses Available</p>
            </div>

            {/* Stat 4 */}
            <div className="text-center bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-green-200">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                <TbTrophy className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-2">99%</div>
              <p className="text-gray-700 font-medium">Success Rate</p>
            </div>
          </motion.div>
        </div>
      </section>
      {/* Features Section */}
      <section ref={featuresRef} className="py-20 bg-gradient-to-br from-green-50 via-yellow-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4">
              Why Choose Brainwave?
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Experience the future of education with our comprehensive learning platform designed for Tanzania
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-green-200"
            >
              <div className="w-16 h-16 mx-auto mb-6 bg-green-500 rounded-2xl flex items-center justify-center">
                <TbBook className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-green-700 mb-4 text-center">Quality Materials</h3>
              <p className="text-gray-600 text-center">
                Access comprehensive study materials designed by Tanzanian education experts
              </p>
            </motion.div>

            {/* Feature 2 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-yellow-200"
            >
              <div className="w-16 h-16 mx-auto mb-6 bg-yellow-500 rounded-2xl flex items-center justify-center">
                <TbTrophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-yellow-700 mb-4 text-center">Interactive Quizzes</h3>
              <p className="text-gray-600 text-center">
                Test your knowledge with engaging quizzes and track your progress
              </p>
            </motion.div>

            {/* Feature 3 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-blue-200"
            >
              <div className="w-16 h-16 mx-auto mb-6 bg-blue-600 rounded-2xl flex items-center justify-center">
                <TbStar className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-blue-700 mb-4 text-center">Expert Guidance</h3>
              <p className="text-gray-600 text-center">
                Get personalized academic guidance from experienced Tanzanian educators
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section ref={successStoriesRef} className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent mb-4">
              Success Stories
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Hear from Tanzanian students who transformed their academic journey with Brainwave
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 shadow-lg border border-green-200"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                  A
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-green-700">Amina Hassan</h4>
                  <p className="text-green-600 text-sm">Form 4 Student - Dar es Salaam</p>
                </div>
              </div>
              <p className="text-gray-700 italic">
                "Brainwave helped me improve my grades significantly. The study materials are excellent and designed for Tanzanian curriculum!"
              </p>
            </motion.div>

            {/* Testimonial 2 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-6 shadow-lg border border-yellow-200"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold">
                  J
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-yellow-700">John Mwalimu</h4>
                  <p className="text-yellow-600 text-sm">Form 6 Student - Arusha</p>
                </div>
              </div>
              <p className="text-gray-700 italic">
                "The interactive quizzes made learning fun and helped me understand complex topics easily. Perfect for Tanzanian students!"
              </p>
            </motion.div>

            {/* Testimonial 3 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 shadow-lg border border-blue-200"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                  M
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-blue-700">Maria Kimaro</h4>
                  <p className="text-blue-600 text-sm">University Student - Dodoma</p>
                </div>
              </div>
              <p className="text-gray-700 italic">
                "Thanks to Brainwave, I passed my national exams with flying colors and got into University of Dar es Salaam!"
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section ref={contactRef} className="py-20 bg-gradient-to-br from-green-800 via-gray-900 to-blue-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tanzania Flag in Contact Section */}
          <div className="flex justify-center mb-8">
            <div className="w-20 h-14 rounded-lg overflow-hidden border-2 border-white shadow-xl">
              <img
                src="https://flagcdn.com/w40/tz.png"
                alt="Tanzania Flag"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              Get in Touch
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Message</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white"
                  placeholder="Your message..."
                />
              </div>
              <div className="text-center">
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50"
                >
                  {loading ? 'Sending...' : 'Send Message'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-green-900 via-gray-900 to-blue-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              {/* Tanzania Flag in Footer */}
              <div className="w-8 h-6 rounded-sm overflow-hidden border border-gray-300">
                <img
                  src="https://flagcdn.com/w40/tz.png"
                  alt="Tanzania Flag"
                  className="w-full h-full object-cover"
                />
              </div>
              <TbBrain className="w-8 h-8 text-green-400" />
              <span className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                Brainwave
              </span>
            </div>
            <p className="text-gray-300 mb-6">
              Empowering students across Tanzania with quality education
            </p>
            <div className="flex justify-center space-x-8 text-sm text-gray-300">
              <Link to="/login" className="hover:text-green-400 transition-colors">Login</Link>
              <Link to="/register" className="hover:text-blue-400 transition-colors">Register</Link>
              <button onClick={() => scrollToSection(contactRef)} className="hover:text-yellow-400 transition-colors">Contact</button>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
              <p>&copy; 2024 Brainwave Tanzania. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
