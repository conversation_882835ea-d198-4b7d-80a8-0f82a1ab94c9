{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Skills\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { message } from \"antd\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { getAllSkills, getFeaturedSkills, markSkillCompleted } from \"../../../apicalls/skills\";\nimport { FaSearch, FaFilter, FaPlay, FaStar, FaClock, FaUser, FaEye, FaTimes, FaExpand, FaCompress, FaCheck } from \"react-icons/fa\";\nimport \"./Skills.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Skills = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n\n  // State management\n  const [skills, setSkills] = useState([]);\n  const [featuredSkills, setFeaturedSkills] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedLevel, setSelectedLevel] = useState(\"all\");\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [selectedSkill, setSelectedSkill] = useState(null);\n  const [showVideoModal, setShowVideoModal] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n\n  // Skill levels with translations\n  const skillLevels = [{\n    value: \"all\",\n    label: isKiswahili ? \"Viwango Vyote\" : \"All Levels\"\n  }, {\n    value: \"beginner\",\n    label: isKiswahili ? \"Mwanzo\" : \"Beginner\",\n    color: \"green\"\n  }, {\n    value: \"amateur\",\n    label: isKiswahili ? \"Wastani\" : \"Amateur\",\n    color: \"blue\"\n  }, {\n    value: \"professional\",\n    label: isKiswahili ? \"Kitaalamu\" : \"Professional\",\n    color: \"orange\"\n  }, {\n    value: \"expert\",\n    label: isKiswahili ? \"Mtaalamu\" : \"Expert\",\n    color: \"red\"\n  }];\n\n  // Fetch skills data\n  const fetchSkills = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const params = {\n        targetAudience: (user === null || user === void 0 ? void 0 : user.level) || \"all\",\n        limit: 50,\n        sortBy: sortBy === \"newest\" ? \"createdAt\" : sortBy,\n        sortOrder: \"desc\"\n      };\n      if (selectedLevel !== \"all\") {\n        params.level = selectedLevel;\n      }\n      if (selectedCategory !== \"all\") {\n        params.category = selectedCategory;\n      }\n      if (searchTerm) {\n        params.search = searchTerm;\n      }\n      const response = await getAllSkills(params);\n      if (response.success) {\n        setSkills(response.data);\n      } else {\n        setError(response.message || \"Failed to fetch skills\");\n      }\n    } catch (error) {\n      setError(\"Error fetching skills\");\n      console.error(\"Error:\", error);\n    } finally {\n      setLoading(false);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.level, selectedLevel, selectedCategory, searchTerm, sortBy]);\n\n  // Fetch featured skills\n  const fetchFeaturedSkills = useCallback(async () => {\n    try {\n      const response = await getFeaturedSkills(6);\n      if (response.success) {\n        setFeaturedSkills(response.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching featured skills:\", error);\n    }\n  }, []);\n  useEffect(() => {\n    fetchSkills();\n    fetchFeaturedSkills();\n  }, [fetchSkills, fetchFeaturedSkills]);\n\n  // Get unique categories from skills\n  const categories = useMemo(() => {\n    const uniqueCategories = [...new Set(skills.map(skill => skill.category))];\n    return [{\n      value: \"all\",\n      label: isKiswahili ? \"Makundi Yote\" : \"All Categories\"\n    }, ...uniqueCategories.map(cat => ({\n      value: cat,\n      label: cat\n    }))];\n  }, [skills, isKiswahili]);\n\n  // Handle skill video play\n  const handlePlaySkill = skill => {\n    setSelectedSkill(skill);\n    setShowVideoModal(true);\n    setVideoError(null);\n  };\n\n  // Handle skill completion\n  const handleMarkCompleted = async skillId => {\n    try {\n      const response = await markSkillCompleted(skillId);\n      if (response.success) {\n        message.success(isKiswahili ? \"Ujuzi umekamilika!\" : \"Skill completed!\");\n      }\n    } catch (error) {\n      message.error(isKiswahili ? \"Hitilafu ya kukamilisha ujuzi\" : \"Error marking skill as completed\");\n    }\n  };\n\n  // Close video modal\n  const handleCloseVideo = () => {\n    setShowVideoModal(false);\n    setSelectedSkill(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n  };\n\n  // Toggle video expand\n  const toggleVideoExpand = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get skill level badge\n  const getSkillLevelBadge = level => {\n    const levelConfig = skillLevels.find(l => l.value === level);\n    return levelConfig || {\n      label: level,\n      color: \"default\"\n    };\n  };\n\n  // Format duration\n  const formatDuration = duration => {\n    if (!duration) return isKiswahili ? \"Muda haujaainishwa\" : \"Duration not specified\";\n    return duration;\n  };\n\n  // Filter and sort skills\n  const filteredSkills = useMemo(() => {\n    let filtered = skills.filter(skill => {\n      var _skill$description;\n      const matchesSearch = !searchTerm || skill.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_skill$description = skill.description) === null || _skill$description === void 0 ? void 0 : _skill$description.toLowerCase().includes(searchTerm.toLowerCase())) || skill.category.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesLevel = selectedLevel === \"all\" || skill.level === selectedLevel;\n      const matchesCategory = selectedCategory === \"all\" || skill.category === selectedCategory;\n      return matchesSearch && matchesLevel && matchesCategory;\n    });\n\n    // Sort skills\n    if (sortBy === \"popular\") {\n      filtered.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));\n    } else if (sortBy === \"rating\") {\n      filtered.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0));\n    } else {\n      filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    }\n    return filtered;\n  }, [skills, searchTerm, selectedLevel, selectedCategory, sortBy]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"skills-container\",\n    children: [featuredSkills.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"featured-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: [/*#__PURE__*/_jsxDEV(FaStar, {\n          className: \"section-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), isKiswahili ? \"Ujuzi Maalum\" : \"Featured Skills\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"featured-grid\",\n        children: featuredSkills.map(skill => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"featured-skill-card\",\n          onClick: () => handlePlaySkill(skill),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: skill.thumbnailUrl || \"https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Skill+Video\",\n              alt: skill.title,\n              onError: e => {\n                e.target.src = \"https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Skill+Video\";\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlay, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-level-badge\",\n              style: {\n                backgroundColor: getSkillLevelBadge(skill.level).color\n              },\n              children: getSkillLevelBadge(skill.level).label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"skill-title\",\n              children: skill.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"skill-category\",\n              children: skill.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-duration\",\n                children: [/*#__PURE__*/_jsxDEV(FaClock, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this), \" \", formatDuration(skill.duration)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-views\",\n                children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), \" \", skill.viewCount || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this)]\n        }, skill._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-container\",\n          children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: isKiswahili ? \"Tafuta ujuzi...\" : \"Search skills...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: [/*#__PURE__*/_jsxDEV(FaFilter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), isKiswahili ? \"Kiwango\" : \"Level\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedLevel,\n            onChange: e => setSelectedLevel(e.target.value),\n            className: \"filter-select\",\n            children: skillLevels.map(level => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: level.value,\n              children: level.label\n            }, level.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: isKiswahili ? \"Kundi\" : \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            className: \"filter-select\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.value,\n              children: category.label\n            }, category.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: isKiswahili ? \"Panga kwa\" : \"Sort by\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"filter-select\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"newest\",\n              children: isKiswahili ? \"Mpya zaidi\" : \"Newest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"popular\",\n              children: isKiswahili ? \"Maarufu\" : \"Most Popular\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rating\",\n              children: isKiswahili ? \"Kiwango cha Juu\" : \"Highest Rated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? \"Inapakia ujuzi...\" : \"Loading skills...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? \"Hitilafu ya Kupakia\" : \"Error Loading Skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchSkills,\n          className: \"retry-btn\",\n          children: isKiswahili ? \"Jaribu Tena\" : \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this) : filteredSkills.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"skills-grid\",\n        children: filteredSkills.map(skill => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skill-card\",\n          onClick: () => handlePlaySkill(skill),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: skill.thumbnailUrl || \"/api/placeholder/300/200\",\n              alt: skill.title,\n              onError: e => {\n                e.target.src = \"/api/placeholder/300/200\";\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlay, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-level-badge\",\n              style: {\n                backgroundColor: getSkillLevelBadge(skill.level).color\n              },\n              children: getSkillLevelBadge(skill.level).label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this), skill.difficulty && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"difficulty-badge\",\n              children: \"⭐\".repeat(skill.difficulty)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"skill-title\",\n              children: skill.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"skill-description\",\n              children: skill.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-tags\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-category\",\n                children: skill.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), skill.estimatedTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"estimated-time\",\n                children: [/*#__PURE__*/_jsxDEV(FaClock, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 25\n                }, this), \" \", skill.estimatedTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skill-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-views\",\n                  children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 25\n                  }, this), \" \", skill.viewCount || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 23\n                }, this), skill.averageRating > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-rating\",\n                  children: [/*#__PURE__*/_jsxDEV(FaStar, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 27\n                  }, this), \" \", skill.averageRating.toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"complete-btn\",\n                onClick: e => {\n                  e.stopPropagation();\n                  handleMarkCompleted(skill._id);\n                },\n                children: /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this)]\n        }, skill._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaUser, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? \"Hakuna Ujuzi Uliopatikana\" : \"No Skills Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? \"Hakuna ujuzi unaolingana na utafutaji wako. Jaribu kubadilisha vigezo vya utafutaji.\" : \"No skills match your search criteria. Try adjusting your search filters.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), showVideoModal && selectedSkill && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-modal-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: handleCloseVideo,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"video-title\",\n            children: selectedSkill.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: toggleVideoExpand,\n              className: \"expand-btn\",\n              children: isVideoExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 38\n              }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 55\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseVideo,\n              className: \"close-btn\",\n              children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-container\",\n          children: selectedSkill.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n            controls: true,\n            autoPlay: true,\n            width: \"100%\",\n            height: \"400\",\n            onError: () => setVideoError(\"Failed to load video\"),\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: selectedSkill.videoUrl,\n              type: \"video/mp4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 19\n            }, this), isKiswahili ? \"Kivinjari chako hakitumii video.\" : \"Your browser does not support the video tag.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-error\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: isKiswahili ? \"Video Haipatikani\" : \"Video Unavailable\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: isKiswahili ? \"Video hii haiwezi kuchezwa kwa sasa.\" : \"This video cannot be played at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-meta-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-level\",\n                style: {\n                  backgroundColor: getSkillLevelBadge(selectedSkill.level).color\n                },\n                children: getSkillLevelBadge(selectedSkill.level).label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-category\",\n                children: selectedSkill.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), selectedSkill.difficulty && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"difficulty\",\n                children: \"⭐\".repeat(selectedSkill.difficulty)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"skill-description\",\n              children: selectedSkill.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), selectedSkill.learningOutcomes && selectedSkill.learningOutcomes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"learning-outcomes\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: isKiswahili ? \"Utakachojifunza:\" : \"What you'll learn:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: selectedSkill.learningOutcomes.map((outcome, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: outcome\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleMarkCompleted(selectedSkill._id),\n              className: \"complete-skill-btn\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), isKiswahili ? \"Kamilisha Ujuzi\" : \"Mark as Completed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s(Skills, \"lyFg7QEEpTXUf6q/RKPVtkNohAM=\", false, function () {\n  return [useSelector, useLanguage];\n});\n_c = Skills;\nexport default Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useSelector", "message", "useLanguage", "getAllSkills", "getFeaturedSkills", "mark<PERSON><PERSON>Completed", "FaSearch", "FaFilter", "FaPlay", "FaStar", "FaClock", "FaUser", "FaEye", "FaTimes", "FaExpand", "FaCompress", "FaCheck", "jsxDEV", "_jsxDEV", "Skills", "_s", "user", "state", "t", "isKiswahili", "skills", "setSkills", "featuredS<PERSON>s", "setFeaturedSkills", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "sortBy", "setSortBy", "selectedSkill", "setSelectedSkill", "showVideoModal", "setShowVideoModal", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "skillLevels", "value", "label", "color", "fetchSkills", "params", "targetAudience", "level", "limit", "sortOrder", "category", "search", "response", "success", "data", "console", "fetchFeaturedSkills", "categories", "uniqueCategories", "Set", "map", "skill", "cat", "handlePlaySkill", "handleMarkCompleted", "skillId", "handleCloseVideo", "toggleVideoExpand", "getSkillLevelBadge", "levelConfig", "find", "l", "formatDuration", "duration", "filteredSkills", "filtered", "filter", "_skill$description", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesLevel", "matchesCategory", "sort", "a", "b", "viewCount", "averageRating", "Date", "createdAt", "className", "children", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "thumbnailUrl", "alt", "onError", "e", "target", "style", "backgroundColor", "_id", "type", "placeholder", "onChange", "difficulty", "repeat", "estimatedTime", "toFixed", "stopPropagation", "videoUrl", "controls", "autoPlay", "width", "height", "learningOutcomes", "outcome", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Skills/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { message } from \"antd\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { getAllSkills, getFeaturedSkills, markSkillCompleted } from \"../../../apicalls/skills\";\nimport {\n  FaSearch,\n  FaFilter,\n  FaPlay,\n  FaStar,\n  FaClock,\n  FaUser,\n  FaEye,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  FaCheck\n} from \"react-icons/fa\";\nimport \"./Skills.css\";\n\nconst Skills = () => {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  \n  // State management\n  const [skills, setSkills] = useState([]);\n  const [featuredSkills, setFeaturedSkills] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedLevel, setSelectedLevel] = useState(\"all\");\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n  \n  // Video player state\n  const [selectedSkill, setSelectedSkill] = useState(null);\n  const [showVideoModal, setShowVideoModal] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n\n  // Skill levels with translations\n  const skillLevels = [\n    { value: \"all\", label: isKiswahili ? \"Viwango Vyote\" : \"All Levels\" },\n    { value: \"beginner\", label: isKiswahili ? \"Mwanzo\" : \"Beginner\", color: \"green\" },\n    { value: \"amateur\", label: isKiswahili ? \"Wastani\" : \"Amateur\", color: \"blue\" },\n    { value: \"professional\", label: isKiswahili ? \"Kitaalamu\" : \"Professional\", color: \"orange\" },\n    { value: \"expert\", label: isKiswahili ? \"Mtaalamu\" : \"Expert\", color: \"red\" }\n  ];\n\n  // Fetch skills data\n  const fetchSkills = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const params = {\n        targetAudience: user?.level || \"all\",\n        limit: 50,\n        sortBy: sortBy === \"newest\" ? \"createdAt\" : sortBy,\n        sortOrder: \"desc\"\n      };\n\n      if (selectedLevel !== \"all\") {\n        params.level = selectedLevel;\n      }\n\n      if (selectedCategory !== \"all\") {\n        params.category = selectedCategory;\n      }\n\n      if (searchTerm) {\n        params.search = searchTerm;\n      }\n\n      const response = await getAllSkills(params);\n      \n      if (response.success) {\n        setSkills(response.data);\n      } else {\n        setError(response.message || \"Failed to fetch skills\");\n      }\n    } catch (error) {\n      setError(\"Error fetching skills\");\n      console.error(\"Error:\", error);\n    } finally {\n      setLoading(false);\n    }\n  }, [user?.level, selectedLevel, selectedCategory, searchTerm, sortBy]);\n\n  // Fetch featured skills\n  const fetchFeaturedSkills = useCallback(async () => {\n    try {\n      const response = await getFeaturedSkills(6);\n      if (response.success) {\n        setFeaturedSkills(response.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching featured skills:\", error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchSkills();\n    fetchFeaturedSkills();\n  }, [fetchSkills, fetchFeaturedSkills]);\n\n  // Get unique categories from skills\n  const categories = useMemo(() => {\n    const uniqueCategories = [...new Set(skills.map(skill => skill.category))];\n    return [\n      { value: \"all\", label: isKiswahili ? \"Makundi Yote\" : \"All Categories\" },\n      ...uniqueCategories.map(cat => ({ value: cat, label: cat }))\n    ];\n  }, [skills, isKiswahili]);\n\n  // Handle skill video play\n  const handlePlaySkill = (skill) => {\n    setSelectedSkill(skill);\n    setShowVideoModal(true);\n    setVideoError(null);\n  };\n\n  // Handle skill completion\n  const handleMarkCompleted = async (skillId) => {\n    try {\n      const response = await markSkillCompleted(skillId);\n      if (response.success) {\n        message.success(isKiswahili ? \"Ujuzi umekamilika!\" : \"Skill completed!\");\n      }\n    } catch (error) {\n      message.error(isKiswahili ? \"Hitilafu ya kukamilisha ujuzi\" : \"Error marking skill as completed\");\n    }\n  };\n\n  // Close video modal\n  const handleCloseVideo = () => {\n    setShowVideoModal(false);\n    setSelectedSkill(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n  };\n\n  // Toggle video expand\n  const toggleVideoExpand = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get skill level badge\n  const getSkillLevelBadge = (level) => {\n    const levelConfig = skillLevels.find(l => l.value === level);\n    return levelConfig || { label: level, color: \"default\" };\n  };\n\n  // Format duration\n  const formatDuration = (duration) => {\n    if (!duration) return isKiswahili ? \"Muda haujaainishwa\" : \"Duration not specified\";\n    return duration;\n  };\n\n  // Filter and sort skills\n  const filteredSkills = useMemo(() => {\n    let filtered = skills.filter(skill => {\n      const matchesSearch = !searchTerm || \n        skill.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        skill.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        skill.category.toLowerCase().includes(searchTerm.toLowerCase());\n      \n      const matchesLevel = selectedLevel === \"all\" || skill.level === selectedLevel;\n      const matchesCategory = selectedCategory === \"all\" || skill.category === selectedCategory;\n      \n      return matchesSearch && matchesLevel && matchesCategory;\n    });\n\n    // Sort skills\n    if (sortBy === \"popular\") {\n      filtered.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));\n    } else if (sortBy === \"rating\") {\n      filtered.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0));\n    } else {\n      filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    }\n\n    return filtered;\n  }, [skills, searchTerm, selectedLevel, selectedCategory, sortBy]);\n\n  return (\n    <div className=\"skills-container\">\n      {/* Header removed - using ProtectedRoute header only */}\n\n      {/* Featured Skills */}\n      {featuredSkills.length > 0 && (\n        <div className=\"featured-section\">\n          <h2 className=\"section-title\">\n            <FaStar className=\"section-icon\" />\n            {isKiswahili ? \"Ujuzi Maalum\" : \"Featured Skills\"}\n          </h2>\n          <div className=\"featured-grid\">\n            {featuredSkills.map((skill) => (\n              <div key={skill._id} className=\"featured-skill-card\" onClick={() => handlePlaySkill(skill)}>\n                <div className=\"skill-thumbnail\">\n                  <img\n                    src={skill.thumbnailUrl || \"https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Skill+Video\"}\n                    alt={skill.title}\n                    onError={(e) => {\n                      e.target.src = \"https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Skill+Video\";\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlay className=\"play-icon\" />\n                  </div>\n                  <div className=\"skill-level-badge\" style={{backgroundColor: getSkillLevelBadge(skill.level).color}}>\n                    {getSkillLevelBadge(skill.level).label}\n                  </div>\n                </div>\n                <div className=\"skill-info\">\n                  <h3 className=\"skill-title\">{skill.title}</h3>\n                  <p className=\"skill-category\">{skill.category}</p>\n                  <div className=\"skill-meta\">\n                    <span className=\"skill-duration\">\n                      <FaClock /> {formatDuration(skill.duration)}\n                    </span>\n                    <span className=\"skill-views\">\n                      <FaEye /> {skill.viewCount || 0}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Filters and Search */}\n      <div className=\"skills-controls\">\n        <div className=\"search-section\">\n          <div className=\"search-input-container\">\n            <FaSearch className=\"search-icon\" />\n            <input\n              type=\"text\"\n              placeholder={isKiswahili ? \"Tafuta ujuzi...\" : \"Search skills...\"}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"search-input\"\n            />\n          </div>\n        </div>\n\n        <div className=\"filters-section\">\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              <FaFilter />\n              {isKiswahili ? \"Kiwango\" : \"Level\"}\n            </label>\n            <select\n              value={selectedLevel}\n              onChange={(e) => setSelectedLevel(e.target.value)}\n              className=\"filter-select\"\n            >\n              {skillLevels.map((level) => (\n                <option key={level.value} value={level.value}>\n                  {level.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              {isKiswahili ? \"Kundi\" : \"Category\"}\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"filter-select\"\n            >\n              {categories.map((category) => (\n                <option key={category.value} value={category.value}>\n                  {category.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label className=\"filter-label\">\n              {isKiswahili ? \"Panga kwa\" : \"Sort by\"}\n            </label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"filter-select\"\n            >\n              <option value=\"newest\">{isKiswahili ? \"Mpya zaidi\" : \"Newest\"}</option>\n              <option value=\"popular\">{isKiswahili ? \"Maarufu\" : \"Most Popular\"}</option>\n              <option value=\"rating\">{isKiswahili ? \"Kiwango cha Juu\" : \"Highest Rated\"}</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Skills Grid */}\n      <div className=\"skills-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? \"Inapakia ujuzi...\" : \"Loading skills...\"}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <h3>{isKiswahili ? \"Hitilafu ya Kupakia\" : \"Error Loading Skills\"}</h3>\n            <p>{error}</p>\n            <button onClick={fetchSkills} className=\"retry-btn\">\n              {isKiswahili ? \"Jaribu Tena\" : \"Try Again\"}\n            </button>\n          </div>\n        ) : filteredSkills.length > 0 ? (\n          <div className=\"skills-grid\">\n            {filteredSkills.map((skill) => (\n              <div key={skill._id} className=\"skill-card\" onClick={() => handlePlaySkill(skill)}>\n                <div className=\"skill-thumbnail\">\n                  <img\n                    src={skill.thumbnailUrl || \"/api/placeholder/300/200\"}\n                    alt={skill.title}\n                    onError={(e) => {\n                      e.target.src = \"/api/placeholder/300/200\";\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlay className=\"play-icon\" />\n                  </div>\n                  <div className=\"skill-level-badge\" style={{backgroundColor: getSkillLevelBadge(skill.level).color}}>\n                    {getSkillLevelBadge(skill.level).label}\n                  </div>\n                  {skill.difficulty && (\n                    <div className=\"difficulty-badge\">\n                      {\"⭐\".repeat(skill.difficulty)}\n                    </div>\n                  )}\n                </div>\n                <div className=\"skill-content\">\n                  <h3 className=\"skill-title\">{skill.title}</h3>\n                  <p className=\"skill-description\">{skill.description}</p>\n                  <div className=\"skill-tags\">\n                    <span className=\"skill-category\">{skill.category}</span>\n                    {skill.estimatedTime && (\n                      <span className=\"estimated-time\">\n                        <FaClock /> {skill.estimatedTime}\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"skill-footer\">\n                    <div className=\"skill-stats\">\n                      <span className=\"skill-views\">\n                        <FaEye /> {skill.viewCount || 0}\n                      </span>\n                      {skill.averageRating > 0 && (\n                        <span className=\"skill-rating\">\n                          <FaStar /> {skill.averageRating.toFixed(1)}\n                        </span>\n                      )}\n                    </div>\n                    <button\n                      className=\"complete-btn\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleMarkCompleted(skill._id);\n                      }}\n                    >\n                      <FaCheck />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaUser className=\"empty-icon\" />\n            <h3>{isKiswahili ? \"Hakuna Ujuzi Uliopatikana\" : \"No Skills Found\"}</h3>\n            <p>\n              {isKiswahili \n                ? \"Hakuna ujuzi unaolingana na utafutaji wako. Jaribu kubadilisha vigezo vya utafutaji.\"\n                : \"No skills match your search criteria. Try adjusting your search filters.\"}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Video Modal */}\n      {showVideoModal && selectedSkill && (\n        <div className={`video-modal-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={handleCloseVideo}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => e.stopPropagation()}>\n            <div className=\"video-header\">\n              <h3 className=\"video-title\">{selectedSkill.title}</h3>\n              <div className=\"video-controls\">\n                <button onClick={toggleVideoExpand} className=\"expand-btn\">\n                  {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                </button>\n                <button onClick={handleCloseVideo} className=\"close-btn\">\n                  <FaTimes />\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"video-container\">\n              {selectedSkill.videoUrl ? (\n                <video\n                  controls\n                  autoPlay\n                  width=\"100%\"\n                  height=\"400\"\n                  onError={() => setVideoError(\"Failed to load video\")}\n                >\n                  <source src={selectedSkill.videoUrl} type=\"video/mp4\" />\n                  {isKiswahili ? \"Kivinjari chako hakitumii video.\" : \"Your browser does not support the video tag.\"}\n                </video>\n              ) : (\n                <div className=\"video-error\">\n                  <h4>{isKiswahili ? \"Video Haipatikani\" : \"Video Unavailable\"}</h4>\n                  <p>{isKiswahili ? \"Video hii haiwezi kuchezwa kwa sasa.\" : \"This video cannot be played at the moment.\"}</p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"video-info\">\n              <div className=\"skill-details\">\n                <div className=\"skill-meta-info\">\n                  <span className=\"skill-level\" style={{backgroundColor: getSkillLevelBadge(selectedSkill.level).color}}>\n                    {getSkillLevelBadge(selectedSkill.level).label}\n                  </span>\n                  <span className=\"skill-category\">{selectedSkill.category}</span>\n                  {selectedSkill.difficulty && (\n                    <span className=\"difficulty\">\n                      {\"⭐\".repeat(selectedSkill.difficulty)}\n                    </span>\n                  )}\n                </div>\n                <p className=\"skill-description\">{selectedSkill.description}</p>\n                {selectedSkill.learningOutcomes && selectedSkill.learningOutcomes.length > 0 && (\n                  <div className=\"learning-outcomes\">\n                    <h4>{isKiswahili ? \"Utakachojifunza:\" : \"What you'll learn:\"}</h4>\n                    <ul>\n                      {selectedSkill.learningOutcomes.map((outcome, index) => (\n                        <li key={index}>{outcome}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n              \n              <div className=\"video-actions\">\n                <button\n                  onClick={() => handleMarkCompleted(selectedSkill._id)}\n                  className=\"complete-skill-btn\"\n                >\n                  <FaCheck />\n                  {isKiswahili ? \"Kamilisha Ujuzi\" : \"Mark as Completed\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Skills;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAQ,0BAA0B;AAC9F,SACEC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGtB,WAAW,CAAC,CAAC;;EAExC;EACA,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMqD,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE3B,WAAW,GAAG,eAAe,GAAG;EAAa,CAAC,EACrE;IAAE0B,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE3B,WAAW,GAAG,QAAQ,GAAG,UAAU;IAAE4B,KAAK,EAAE;EAAQ,CAAC,EACjF;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE3B,WAAW,GAAG,SAAS,GAAG,SAAS;IAAE4B,KAAK,EAAE;EAAO,CAAC,EAC/E;IAAEF,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE3B,WAAW,GAAG,WAAW,GAAG,cAAc;IAAE4B,KAAK,EAAE;EAAS,CAAC,EAC7F;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE3B,WAAW,GAAG,UAAU,GAAG,QAAQ;IAAE4B,KAAK,EAAE;EAAM,CAAC,CAC9E;;EAED;EACA,MAAMC,WAAW,GAAGvD,WAAW,CAAC,YAAY;IAC1CgC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMsB,MAAM,GAAG;QACbC,cAAc,EAAE,CAAAlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,KAAK,KAAI,KAAK;QACpCC,KAAK,EAAE,EAAE;QACTlB,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAGA,MAAM;QAClDmB,SAAS,EAAE;MACb,CAAC;MAED,IAAIvB,aAAa,KAAK,KAAK,EAAE;QAC3BmB,MAAM,CAACE,KAAK,GAAGrB,aAAa;MAC9B;MAEA,IAAIE,gBAAgB,KAAK,KAAK,EAAE;QAC9BiB,MAAM,CAACK,QAAQ,GAAGtB,gBAAgB;MACpC;MAEA,IAAIJ,UAAU,EAAE;QACdqB,MAAM,CAACM,MAAM,GAAG3B,UAAU;MAC5B;MAEA,MAAM4B,QAAQ,GAAG,MAAM1D,YAAY,CAACmD,MAAM,CAAC;MAE3C,IAAIO,QAAQ,CAACC,OAAO,EAAE;QACpBpC,SAAS,CAACmC,QAAQ,CAACE,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL/B,QAAQ,CAAC6B,QAAQ,CAAC5D,OAAO,IAAI,wBAAwB,CAAC;MACxD;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,QAAQ,CAAC,uBAAuB,CAAC;MACjCgC,OAAO,CAACjC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,KAAK,EAAErB,aAAa,EAAEE,gBAAgB,EAAEJ,UAAU,EAAEM,MAAM,CAAC,CAAC;;EAEtE;EACA,MAAM0B,mBAAmB,GAAGnE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAM+D,QAAQ,GAAG,MAAMzD,iBAAiB,CAAC,CAAC,CAAC;MAC3C,IAAIyD,QAAQ,CAACC,OAAO,EAAE;QACpBlC,iBAAiB,CAACiC,QAAQ,CAACE,IAAI,CAAC;MAClC;IACF,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdiC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC,EAAE,EAAE,CAAC;EAENlC,SAAS,CAAC,MAAM;IACdwD,WAAW,CAAC,CAAC;IACbY,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACZ,WAAW,EAAEY,mBAAmB,CAAC,CAAC;;EAEtC;EACA,MAAMC,UAAU,GAAGnE,OAAO,CAAC,MAAM;IAC/B,MAAMoE,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3C,MAAM,CAAC4C,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACX,QAAQ,CAAC,CAAC,CAAC;IAC1E,OAAO,CACL;MAAET,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE3B,WAAW,GAAG,cAAc,GAAG;IAAiB,CAAC,EACxE,GAAG2C,gBAAgB,CAACE,GAAG,CAACE,GAAG,KAAK;MAAErB,KAAK,EAAEqB,GAAG;MAAEpB,KAAK,EAAEoB;IAAI,CAAC,CAAC,CAAC,CAC7D;EACH,CAAC,EAAE,CAAC9C,MAAM,EAAED,WAAW,CAAC,CAAC;;EAEzB;EACA,MAAMgD,eAAe,GAAIF,KAAK,IAAK;IACjC5B,gBAAgB,CAAC4B,KAAK,CAAC;IACvB1B,iBAAiB,CAAC,IAAI,CAAC;IACvBI,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyB,mBAAmB,GAAG,MAAOC,OAAO,IAAK;IAC7C,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMxD,kBAAkB,CAACqE,OAAO,CAAC;MAClD,IAAIb,QAAQ,CAACC,OAAO,EAAE;QACpB7D,OAAO,CAAC6D,OAAO,CAACtC,WAAW,GAAG,oBAAoB,GAAG,kBAAkB,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAACP,WAAW,GAAG,+BAA+B,GAAG,kCAAkC,CAAC;IACnG;EACF,CAAC;;EAED;EACA,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,iBAAiB,CAAC,KAAK,CAAC;IACxBF,gBAAgB,CAAC,IAAI,CAAC;IACtBI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9B,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAMgC,kBAAkB,GAAIrB,KAAK,IAAK;IACpC,MAAMsB,WAAW,GAAG7B,WAAW,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,KAAKM,KAAK,CAAC;IAC5D,OAAOsB,WAAW,IAAI;MAAE3B,KAAK,EAAEK,KAAK;MAAEJ,KAAK,EAAE;IAAU,CAAC;EAC1D,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAIC,QAAQ,IAAK;IACnC,IAAI,CAACA,QAAQ,EAAE,OAAO1D,WAAW,GAAG,oBAAoB,GAAG,wBAAwB;IACnF,OAAO0D,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGpF,OAAO,CAAC,MAAM;IACnC,IAAIqF,QAAQ,GAAG3D,MAAM,CAAC4D,MAAM,CAACf,KAAK,IAAI;MAAA,IAAAgB,kBAAA;MACpC,MAAMC,aAAa,GAAG,CAACtD,UAAU,IAC/BqC,KAAK,CAACkB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,MAAAH,kBAAA,GAC5DhB,KAAK,CAACqB,WAAW,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,KACnEnB,KAAK,CAACX,QAAQ,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC;MAEjE,MAAMG,YAAY,GAAGzD,aAAa,KAAK,KAAK,IAAImC,KAAK,CAACd,KAAK,KAAKrB,aAAa;MAC7E,MAAM0D,eAAe,GAAGxD,gBAAgB,KAAK,KAAK,IAAIiC,KAAK,CAACX,QAAQ,KAAKtB,gBAAgB;MAEzF,OAAOkD,aAAa,IAAIK,YAAY,IAAIC,eAAe;IACzD,CAAC,CAAC;;IAEF;IACA,IAAItD,MAAM,KAAK,SAAS,EAAE;MACxB6C,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,SAAS,IAAI,CAAC,KAAKF,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,MAAM,IAAI1D,MAAM,KAAK,QAAQ,EAAE;MAC9B6C,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACE,aAAa,IAAI,CAAC,KAAKH,CAAC,CAACG,aAAa,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM;MACLd,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIG,IAAI,CAACH,CAAC,CAACI,SAAS,CAAC,GAAG,IAAID,IAAI,CAACJ,CAAC,CAACK,SAAS,CAAC,CAAC;IACxE;IAEA,OAAOhB,QAAQ;EACjB,CAAC,EAAE,CAAC3D,MAAM,EAAEQ,UAAU,EAAEE,aAAa,EAAEE,gBAAgB,EAAEE,MAAM,CAAC,CAAC;EAEjE,oBACErB,OAAA;IAAKmF,SAAS,EAAC,kBAAkB;IAAAC,QAAA,GAI9B3E,cAAc,CAAC4E,MAAM,GAAG,CAAC,iBACxBrF,OAAA;MAAKmF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpF,OAAA;QAAImF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC3BpF,OAAA,CAACT,MAAM;UAAC4F,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAClCnF,WAAW,GAAG,cAAc,GAAG,iBAAiB;MAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACLzF,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B3E,cAAc,CAAC0C,GAAG,CAAEC,KAAK,iBACxBpD,OAAA;UAAqBmF,SAAS,EAAC,qBAAqB;UAACO,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAACF,KAAK,CAAE;UAAAgC,QAAA,gBACzFpF,OAAA;YAAKmF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpF,OAAA;cACE2F,GAAG,EAAEvC,KAAK,CAACwC,YAAY,IAAI,oEAAqE;cAChGC,GAAG,EAAEzC,KAAK,CAACkB,KAAM;cACjBwB,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,oEAAoE;cACrF;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzF,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpF,OAAA,CAACV,MAAM;gBAAC6F,SAAS,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNzF,OAAA;cAAKmF,SAAS,EAAC,mBAAmB;cAACc,KAAK,EAAE;gBAACC,eAAe,EAAEvC,kBAAkB,CAACP,KAAK,CAACd,KAAK,CAAC,CAACJ;cAAK,CAAE;cAAAkD,QAAA,EAChGzB,kBAAkB,CAACP,KAAK,CAACd,KAAK,CAAC,CAACL;YAAK;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzF,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpF,OAAA;cAAImF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEhC,KAAK,CAACkB;YAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CzF,OAAA;cAAGmF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEhC,KAAK,CAACX;YAAQ;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClDzF,OAAA;cAAKmF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpF,OAAA;gBAAMmF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC9BpF,OAAA,CAACR,OAAO;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC1B,cAAc,CAACX,KAAK,CAACY,QAAQ,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACPzF,OAAA;gBAAMmF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC3BpF,OAAA,CAACN,KAAK;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACrC,KAAK,CAAC2B,SAAS,IAAI,CAAC;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA3BErC,KAAK,CAAC+C,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Bd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDzF,OAAA;MAAKmF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BpF,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BpF,OAAA;UAAKmF,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCpF,OAAA,CAACZ,QAAQ;YAAC+F,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpCzF,OAAA;YACEoG,IAAI,EAAC,MAAM;YACXC,WAAW,EAAE/F,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;YAClE0B,KAAK,EAAEjB,UAAW;YAClBuF,QAAQ,EAAGP,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;YAC/CmD,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BpF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YAAOmF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC7BpF,OAAA,CAACX,QAAQ;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACXnF,WAAW,GAAG,SAAS,GAAG,OAAO;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACRzF,OAAA;YACEgC,KAAK,EAAEf,aAAc;YACrBqF,QAAQ,EAAGP,CAAC,IAAK7E,gBAAgB,CAAC6E,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;YAClDmD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAExBrD,WAAW,CAACoB,GAAG,CAAEb,KAAK,iBACrBtC,OAAA;cAA0BgC,KAAK,EAAEM,KAAK,CAACN,KAAM;cAAAoD,QAAA,EAC1C9C,KAAK,CAACL;YAAK,GADDK,KAAK,CAACN,KAAK;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YAAOmF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC5B9E,WAAW,GAAG,OAAO,GAAG;UAAU;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACRzF,OAAA;YACEgC,KAAK,EAAEb,gBAAiB;YACxBmF,QAAQ,EAAGP,CAAC,IAAK3E,mBAAmB,CAAC2E,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;YACrDmD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAExBpC,UAAU,CAACG,GAAG,CAAEV,QAAQ,iBACvBzC,OAAA;cAA6BgC,KAAK,EAAES,QAAQ,CAACT,KAAM;cAAAoD,QAAA,EAChD3C,QAAQ,CAACR;YAAK,GADJQ,QAAQ,CAACT,KAAK;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YAAOmF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC5B9E,WAAW,GAAG,WAAW,GAAG;UAAS;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACRzF,OAAA;YACEgC,KAAK,EAAEX,MAAO;YACdiF,QAAQ,EAAGP,CAAC,IAAKzE,SAAS,CAACyE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;YAC3CmD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAEzBpF,OAAA;cAAQgC,KAAK,EAAC,QAAQ;cAAAoD,QAAA,EAAE9E,WAAW,GAAG,YAAY,GAAG;YAAQ;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACvEzF,OAAA;cAAQgC,KAAK,EAAC,SAAS;cAAAoD,QAAA,EAAE9E,WAAW,GAAG,SAAS,GAAG;YAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC3EzF,OAAA;cAAQgC,KAAK,EAAC,QAAQ;cAAAoD,QAAA,EAAE9E,WAAW,GAAG,iBAAiB,GAAG;YAAe;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzF,OAAA;MAAKmF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BzE,OAAO,gBACNX,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpF,OAAA;UAAKmF,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCzF,OAAA;UAAAoF,QAAA,EAAI9E,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ5E,KAAK,gBACPb,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA;UAAAoF,QAAA,EAAK9E,WAAW,GAAG,qBAAqB,GAAG;QAAsB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvEzF,OAAA;UAAAoF,QAAA,EAAIvE;QAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdzF,OAAA;UAAQ0F,OAAO,EAAEvD,WAAY;UAACgD,SAAS,EAAC,WAAW;UAAAC,QAAA,EAChD9E,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJxB,cAAc,CAACoB,MAAM,GAAG,CAAC,gBAC3BrF,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBnB,cAAc,CAACd,GAAG,CAAEC,KAAK,iBACxBpD,OAAA;UAAqBmF,SAAS,EAAC,YAAY;UAACO,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAACF,KAAK,CAAE;UAAAgC,QAAA,gBAChFpF,OAAA;YAAKmF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpF,OAAA;cACE2F,GAAG,EAAEvC,KAAK,CAACwC,YAAY,IAAI,0BAA2B;cACtDC,GAAG,EAAEzC,KAAK,CAACkB,KAAM;cACjBwB,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,0BAA0B;cAC3C;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzF,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpF,OAAA,CAACV,MAAM;gBAAC6F,SAAS,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNzF,OAAA;cAAKmF,SAAS,EAAC,mBAAmB;cAACc,KAAK,EAAE;gBAACC,eAAe,EAAEvC,kBAAkB,CAACP,KAAK,CAACd,KAAK,CAAC,CAACJ;cAAK,CAAE;cAAAkD,QAAA,EAChGzB,kBAAkB,CAACP,KAAK,CAACd,KAAK,CAAC,CAACL;YAAK;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACLrC,KAAK,CAACmD,UAAU,iBACfvG,OAAA;cAAKmF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC9B,GAAG,CAACoB,MAAM,CAACpD,KAAK,CAACmD,UAAU;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNzF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpF,OAAA;cAAImF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEhC,KAAK,CAACkB;YAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CzF,OAAA;cAAGmF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEhC,KAAK,CAACqB;YAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzF,OAAA;cAAKmF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpF,OAAA;gBAAMmF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEhC,KAAK,CAACX;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACvDrC,KAAK,CAACqD,aAAa,iBAClBzG,OAAA;gBAAMmF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC9BpF,OAAA,CAACR,OAAO;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACrC,KAAK,CAACqD,aAAa;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzF,OAAA;cAAKmF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpF,OAAA;gBAAKmF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpF,OAAA;kBAAMmF,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC3BpF,OAAA,CAACN,KAAK;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,KAAC,EAACrC,KAAK,CAAC2B,SAAS,IAAI,CAAC;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,EACNrC,KAAK,CAAC4B,aAAa,GAAG,CAAC,iBACtBhF,OAAA;kBAAMmF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC5BpF,OAAA,CAACT,MAAM;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,KAAC,EAACrC,KAAK,CAAC4B,aAAa,CAAC0B,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNzF,OAAA;gBACEmF,SAAS,EAAC,cAAc;gBACxBO,OAAO,EAAGK,CAAC,IAAK;kBACdA,CAAC,CAACY,eAAe,CAAC,CAAC;kBACnBpD,mBAAmB,CAACH,KAAK,CAAC+C,GAAG,CAAC;gBAChC,CAAE;gBAAAf,QAAA,eAEFpF,OAAA,CAACF,OAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GArDErC,KAAK,CAAC+C,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENzF,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpF,OAAA,CAACP,MAAM;UAAC0F,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjCzF,OAAA;UAAAoF,QAAA,EAAK9E,WAAW,GAAG,2BAA2B,GAAG;QAAiB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxEzF,OAAA;UAAAoF,QAAA,EACG9E,WAAW,GACR,sFAAsF,GACtF;QAA0E;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhE,cAAc,IAAIF,aAAa,iBAC9BvB,OAAA;MAAKmF,SAAS,EAAG,uBAAsBxD,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAAC+D,OAAO,EAAEjC,gBAAiB;MAAA2B,QAAA,eACpGpF,OAAA;QAAKmF,SAAS,EAAG,eAAcxD,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAC+D,OAAO,EAAGK,CAAC,IAAKA,CAAC,CAACY,eAAe,CAAC,CAAE;QAAAvB,QAAA,gBACtGpF,OAAA;UAAKmF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpF,OAAA;YAAImF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE7D,aAAa,CAAC+C;UAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtDzF,OAAA;YAAKmF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpF,OAAA;cAAQ0F,OAAO,EAAEhC,iBAAkB;cAACyB,SAAS,EAAC,YAAY;cAAAC,QAAA,EACvDzD,eAAe,gBAAG3B,OAAA,CAACH,UAAU;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGzF,OAAA,CAACJ,QAAQ;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACTzF,OAAA;cAAQ0F,OAAO,EAAEjC,gBAAiB;cAAC0B,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtDpF,OAAA,CAACL,OAAO;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzF,OAAA;UAAKmF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B7D,aAAa,CAACqF,QAAQ,gBACrB5G,OAAA;YACE6G,QAAQ;YACRC,QAAQ;YACRC,KAAK,EAAC,MAAM;YACZC,MAAM,EAAC,KAAK;YACZlB,OAAO,EAAEA,CAAA,KAAMhE,aAAa,CAAC,sBAAsB,CAAE;YAAAsD,QAAA,gBAErDpF,OAAA;cAAQ2F,GAAG,EAAEpE,aAAa,CAACqF,QAAS;cAACR,IAAI,EAAC;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACvDnF,WAAW,GAAG,kCAAkC,GAAG,8CAA8C;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,gBAERzF,OAAA;YAAKmF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpF,OAAA;cAAAoF,QAAA,EAAK9E,WAAW,GAAG,mBAAmB,GAAG;YAAmB;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClEzF,OAAA;cAAAoF,QAAA,EAAI9E,WAAW,GAAG,sCAAsC,GAAG;YAA4C;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENzF,OAAA;UAAKmF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpF,OAAA;cAAKmF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BpF,OAAA;gBAAMmF,SAAS,EAAC,aAAa;gBAACc,KAAK,EAAE;kBAACC,eAAe,EAAEvC,kBAAkB,CAACpC,aAAa,CAACe,KAAK,CAAC,CAACJ;gBAAK,CAAE;gBAAAkD,QAAA,EACnGzB,kBAAkB,CAACpC,aAAa,CAACe,KAAK,CAAC,CAACL;cAAK;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACPzF,OAAA;gBAAMmF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAE7D,aAAa,CAACkB;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/DlE,aAAa,CAACgF,UAAU,iBACvBvG,OAAA;gBAAMmF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACzB,GAAG,CAACoB,MAAM,CAACjF,aAAa,CAACgF,UAAU;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzF,OAAA;cAAGmF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAE7D,aAAa,CAACkD;YAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/DlE,aAAa,CAAC0F,gBAAgB,IAAI1F,aAAa,CAAC0F,gBAAgB,CAAC5B,MAAM,GAAG,CAAC,iBAC1ErF,OAAA;cAAKmF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCpF,OAAA;gBAAAoF,QAAA,EAAK9E,WAAW,GAAG,kBAAkB,GAAG;cAAoB;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEzF,OAAA;gBAAAoF,QAAA,EACG7D,aAAa,CAAC0F,gBAAgB,CAAC9D,GAAG,CAAC,CAAC+D,OAAO,EAAEC,KAAK,kBACjDnH,OAAA;kBAAAoF,QAAA,EAAiB8B;gBAAO,GAAfC,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENzF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpF,OAAA;cACE0F,OAAO,EAAEA,CAAA,KAAMnC,mBAAmB,CAAChC,aAAa,CAAC4E,GAAG,CAAE;cACtDhB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBAE9BpF,OAAA,CAACF,OAAO;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACVnF,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;YAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvF,EAAA,CA7bID,MAAM;EAAA,QACOnB,WAAW,EACDE,WAAW;AAAA;AAAAoI,EAAA,GAFlCnH,MAAM;AA+bZ,eAAeA,MAAM;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}