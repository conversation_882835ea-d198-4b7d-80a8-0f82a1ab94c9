{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { TbMenu2, TbX, TbHome, TbBrain, TbBook, TbVideo, TbRobot, TbChartLine, TbTrophy, TbUser, TbMessageCircle, TbCreditCard, TbLogout, TbChevronRight, TbStar } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = ({\n  isOpen = false,\n  setIsOpen = () => {}\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n\n  // Mock notification counts - in real app, these would come from API\n  const notificationCounts = {\n    hub: 0,\n    // No notifications for hub\n    ranking: 0,\n    // No notifications for ranking\n    videoLessons: 3,\n    // 3 new video lessons\n    forum: 5,\n    // 5 new forum posts\n    takeQuiz: 2,\n    // 2 new quizzes available\n    profile: 0,\n    // No notifications for profile\n    subscription: 0,\n    // No notifications for subscription\n    logout: 0 // No notifications for logout\n  };\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n  const navigationItems = [{\n    title: isKiswahili ? 'Kituo' : 'Hub',\n    description: isKiswahili ? 'Dashibodi kuu' : 'Main dashboard',\n    icon: TbHome,\n    path: '/user/hub',\n    color: 'from-blue-500 to-blue-600',\n    notificationCount: notificationCounts.hub\n  }, {\n    title: isKiswahili ? 'Uongozi' : 'Ranking',\n    description: isKiswahili ? 'Jedwali la uongozi' : 'Leaderboard rankings',\n    icon: TbTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    notificationCount: notificationCounts.ranking\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Masomo ya video' : 'Educational videos',\n    icon: TbVideo,\n    path: '/user/video-lessons',\n    color: 'from-green-500 to-green-600',\n    notificationCount: notificationCounts.videoLessons\n  }, {\n    title: isKiswahili ? 'Jukwaa' : 'Forum',\n    description: isKiswahili ? 'Mazungumzo na wanafunzi' : 'Student discussions',\n    icon: TbMessageCircle,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    notificationCount: notificationCounts.forum\n  }, {\n    title: isKiswahili ? 'Fanya Jaribio' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu ujuzi wako' : 'Test your knowledge',\n    icon: TbBrain,\n    path: '/user/quiz',\n    color: 'from-emerald-500 to-emerald-600',\n    notificationCount: notificationCounts.takeQuiz\n  }, {\n    title: isKiswahili ? 'Wasifu' : 'Profile',\n    description: isKiswahili ? 'Simamia akaunti' : 'Manage account',\n    icon: TbUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600',\n    notificationCount: notificationCounts.profile\n  }, {\n    title: isKiswahili ? 'Uanachama' : 'Subscription',\n    description: isKiswahili ? 'Simamia mpango wako' : 'Manage your plan',\n    icon: TbCreditCard,\n    path: '/subscription',\n    color: 'from-purple-500 to-purple-600',\n    notificationCount: notificationCounts.subscription\n  }, {\n    title: isKiswahili ? 'Ondoka' : 'Logout',\n    description: isKiswahili ? 'Toka kwenye akaunti' : 'Sign out of account',\n    icon: TbLogout,\n    path: 'logout',\n    color: 'from-red-500 to-red-600',\n    notificationCount: notificationCounts.logout\n  }];\n  const handleNavigation = path => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n  const isActivePath = path => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(false),\n      className: \"sidebar-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-panel fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\",\n      style: {\n        width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n        maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\",\n        style: {\n          padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          className: \"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\",\n          style: {\n            top: window.innerWidth <= 768 ? '8px' : '12px',\n            right: window.innerWidth <= 768 ? '8px' : '12px',\n            padding: window.innerWidth <= 768 ? '6px' : '8px'\n          },\n          title: \"Close Menu\",\n          children: /*#__PURE__*/_jsxDEV(TbX, {\n            className: \"text-white\",\n            style: {\n              width: window.innerWidth <= 768 ? '16px' : '20px',\n              height: window.innerWidth <= 768 ? '16px' : '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          style: {\n            paddingRight: window.innerWidth <= 768 ? '32px' : '48px',\n            marginTop: window.innerWidth <= 768 ? '20px' : '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-bold mb-2\",\n            style: {\n              fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n            },\n            children: \"Navigation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200\",\n            style: {\n              fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n            },\n            children: \"Choose your destination\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        style: {\n          padding: window.innerWidth <= 768 ? '12px' : '16px',\n          gap: window.innerWidth <= 768 ? '8px' : '12px',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: navigationItems.map((item, index) => {\n          const IconComponent = item.icon;\n          const isActive = item.path !== 'logout' && isActivePath(item.path);\n          const isLogout = item.path === 'logout';\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"sidebar-nav-item\",\n            onClick: () => handleNavigation(item.path),\n            className: `w-full flex items-center justify-between rounded-xl transition-all duration-200 ${isActive ? 'bg-blue-50 border-2 border-blue-200 shadow-md' : isLogout ? 'hover:bg-red-50 border-2 border-transparent' : 'hover:bg-gray-50 border-2 border-transparent'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n              marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              style: {\n                gap: window.innerWidth <= 768 ? '8px' : '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`,\n                style: {\n                  width: window.innerWidth <= 768 ? '32px' : '40px',\n                  height: window.innerWidth <= 768 ? '32px' : '40px'\n                },\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                  className: \"text-white\",\n                  style: {\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `font-medium ${isActive ? 'text-blue-200' : 'text-white'}`,\n                  style: {\n                    fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                  },\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `${isActive ? 'text-blue-100' : 'text-gray-200'}`,\n                  style: {\n                    fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n              className: `${isActive ? 'text-blue-200' : isLogout ? 'text-red-200' : 'text-gray-300'}`,\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 21\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernSidebar, \"pPyyEOtIZdHwFs/FYR2zUMtxEq8=\", false, function () {\n  return [useNavigate, useLocation, useSelector, useLanguage];\n});\n_c = ModernSidebar;\nexport default ModernSidebar;\n\n// Add CSS animations to replace Framer Motion\nconst sidebarStyles = `\n.sidebar-backdrop {\n  animation: backdropFadeIn 0.3s ease-out;\n}\n\n.sidebar-panel {\n  animation: sidebarSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 280px;\n}\n\n.sidebar-nav-item {\n  animation: navItemSlideIn 0.3s ease-out;\n  animation-fill-mode: both;\n}\n\n.sidebar-nav-item:nth-child(1) { animation-delay: 0.05s; }\n.sidebar-nav-item:nth-child(2) { animation-delay: 0.1s; }\n.sidebar-nav-item:nth-child(3) { animation-delay: 0.15s; }\n.sidebar-nav-item:nth-child(4) { animation-delay: 0.2s; }\n.sidebar-nav-item:nth-child(5) { animation-delay: 0.25s; }\n.sidebar-nav-item:nth-child(6) { animation-delay: 0.3s; }\n.sidebar-nav-item:nth-child(7) { animation-delay: 0.35s; }\n.sidebar-nav-item:nth-child(8) { animation-delay: 0.4s; }\n\n@keyframes backdropFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes sidebarSlideIn {\n  from {\n    transform: translateX(-400px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes navItemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .sidebar-panel {\n    width: 260px;\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('sidebar-styles');\n  if (!existingStyle) {\n    const styleSheet = document.createElement('style');\n    styleSheet.id = 'sidebar-styles';\n    styleSheet.textContent = sidebarStyles;\n    document.head.appendChild(styleSheet);\n  }\n}\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "useSelector", "useLanguage", "TbMenu2", "TbX", "TbHome", "TbBrain", "TbBook", "TbVideo", "TbRobot", "TbChartLine", "TbTrophy", "TbUser", "TbMessageCircle", "TbCreditCard", "TbLogout", "TbChevronRight", "TbStar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "isOpen", "setIsOpen", "_s", "navigate", "location", "user", "state", "t", "isKiswahili", "notificationCounts", "hub", "ranking", "videoLessons", "forum", "takeQuiz", "profile", "subscription", "logout", "handleKeyDown", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "navigationItems", "title", "description", "icon", "path", "color", "notificationCount", "handleNavigation", "handleLogout", "localStorage", "removeItem", "window", "href", "isActivePath", "pathname", "startsWith", "children", "onClick", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "innerWidth", "max<PERSON><PERSON><PERSON>", "padding", "top", "right", "height", "paddingRight", "marginTop", "fontSize", "gap", "display", "flexDirection", "map", "item", "index", "IconComponent", "isActive", "isLogout", "marginBottom", "_c", "sidebarStyles", "existingStyle", "getElementById", "styleSheet", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbVideo,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight,\n  TbStar\n} from 'react-icons/tb';\n\nconst ModernSidebar = ({ isOpen = false, setIsOpen = () => {} }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n\n  // Mock notification counts - in real app, these would come from API\n  const notificationCounts = {\n    hub: 0, // No notifications for hub\n    ranking: 0, // No notifications for ranking\n    videoLessons: 3, // 3 new video lessons\n    forum: 5, // 5 new forum posts\n    takeQuiz: 2, // 2 new quizzes available\n    profile: 0, // No notifications for profile\n    subscription: 0, // No notifications for subscription\n    logout: 0 // No notifications for logout\n  };\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Kituo' : 'Hub',\n      description: isKiswahili ? 'Dashibodi kuu' : 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600',\n      notificationCount: notificationCounts.hub\n    },\n    {\n      title: isKiswahili ? 'Uongozi' : 'Ranking',\n      description: isKiswahili ? 'Jedwali la uongozi' : 'Leaderboard rankings',\n      icon: TbTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      notificationCount: notificationCounts.ranking\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Masomo ya video' : 'Educational videos',\n      icon: TbVideo,\n      path: '/user/video-lessons',\n      color: 'from-green-500 to-green-600',\n      notificationCount: notificationCounts.videoLessons\n    },\n    {\n      title: isKiswahili ? 'Jukwaa' : 'Forum',\n      description: isKiswahili ? 'Mazungumzo na wanafunzi' : 'Student discussions',\n      icon: TbMessageCircle,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      notificationCount: notificationCounts.forum\n    },\n    {\n      title: isKiswahili ? 'Fanya Jaribio' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu ujuzi wako' : 'Test your knowledge',\n      icon: TbBrain,\n      path: '/user/quiz',\n      color: 'from-emerald-500 to-emerald-600',\n      notificationCount: notificationCounts.takeQuiz\n    },\n    {\n      title: isKiswahili ? 'Wasifu' : 'Profile',\n      description: isKiswahili ? 'Simamia akaunti' : 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      notificationCount: notificationCounts.profile\n    },\n    {\n      title: isKiswahili ? 'Uanachama' : 'Subscription',\n      description: isKiswahili ? 'Simamia mpango wako' : 'Manage your plan',\n      icon: TbCreditCard,\n      path: '/subscription',\n      color: 'from-purple-500 to-purple-600',\n      notificationCount: notificationCounts.subscription\n    },\n    {\n      title: isKiswahili ? 'Ondoka' : 'Logout',\n      description: isKiswahili ? 'Toka kwenye akaunti' : 'Sign out of account',\n      icon: TbLogout,\n      path: 'logout',\n      color: 'from-red-500 to-red-600',\n      notificationCount: notificationCounts.logout\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n\n\n      {/* Backdrop */}\n      {isOpen && (\n        <div\n          onClick={() => setIsOpen(false)}\n          className=\"sidebar-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n        />\n      )}\n\n      {/* Sidebar - Responsive */}\n      {isOpen && (\n        <div\n          className=\"sidebar-panel fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\"\n            style={{\n              width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n              maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n            }}\n          >\n            {/* Header - Responsive */}\n            <div\n              className=\"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n              }}\n            >\n              {/* Close Button */}\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\"\n                style={{\n                  top: window.innerWidth <= 768 ? '8px' : '12px',\n                  right: window.innerWidth <= 768 ? '8px' : '12px',\n                  padding: window.innerWidth <= 768 ? '6px' : '8px'\n                }}\n                title=\"Close Menu\"\n              >\n                <TbX\n                  className=\"text-white\"\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n\n              <div\n                className=\"text-center\"\n                style={{\n                  paddingRight: window.innerWidth <= 768 ? '32px' : '48px',\n                  marginTop: window.innerWidth <= 768 ? '20px' : '24px'\n                }}\n              >\n                <h1\n                  className=\"font-bold mb-2\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n                  }}\n                >\n                  Navigation\n                </h1>\n                <p\n                  className=\"text-blue-200\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                  }}\n                >\n                  Choose your destination\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation - Responsive */}\n            <div\n              className=\"flex-1 overflow-y-auto\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px' : '16px',\n                gap: window.innerWidth <= 768 ? '8px' : '12px',\n                display: 'flex',\n                flexDirection: 'column'\n              }}\n            >\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = item.path !== 'logout' && isActivePath(item.path);\n                const isLogout = item.path === 'logout';\n\n                return (\n                  <button\n                    key={item.path}\n                    className=\"sidebar-nav-item\"\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : isLogout\n                        ? 'hover:bg-red-50 border-2 border-transparent'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                    style={{\n                      padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n                      marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n                    }}\n                  >\n                    <div\n                      className=\"flex items-center\"\n                      style={{\n                        gap: window.innerWidth <= 768 ? '8px' : '12px'\n                      }}\n                    >\n                      <div\n                        className={`rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}\n                        style={{\n                          width: window.innerWidth <= 768 ? '32px' : '40px',\n                          height: window.innerWidth <= 768 ? '32px' : '40px'\n                        }}\n                      >\n                        <IconComponent\n                          className=\"text-white\"\n                          style={{\n                            width: window.innerWidth <= 768 ? '16px' : '20px',\n                            height: window.innerWidth <= 768 ? '16px' : '20px'\n                          }}\n                        />\n                      </div>\n                      <div className=\"text-left flex-1\">\n                        <p\n                          className={`font-medium ${\n                            isActive\n                              ? 'text-blue-200'\n                              : 'text-white'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                          }}\n                        >\n                          {item.title}\n                        </p>\n                        <p\n                          className={`${\n                            isActive\n                              ? 'text-blue-100'\n                              : 'text-gray-200'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                          }}\n                        >\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight\n                      className={`${\n                        isActive\n                          ? 'text-blue-200'\n                          : isLogout\n                          ? 'text-red-200'\n                          : 'text-gray-300'\n                      }`}\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n        )}\n    </>\n  );\n};\n\nexport default ModernSidebar;\n\n// Add CSS animations to replace Framer Motion\nconst sidebarStyles = `\n.sidebar-backdrop {\n  animation: backdropFadeIn 0.3s ease-out;\n}\n\n.sidebar-panel {\n  animation: sidebarSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 280px;\n}\n\n.sidebar-nav-item {\n  animation: navItemSlideIn 0.3s ease-out;\n  animation-fill-mode: both;\n}\n\n.sidebar-nav-item:nth-child(1) { animation-delay: 0.05s; }\n.sidebar-nav-item:nth-child(2) { animation-delay: 0.1s; }\n.sidebar-nav-item:nth-child(3) { animation-delay: 0.15s; }\n.sidebar-nav-item:nth-child(4) { animation-delay: 0.2s; }\n.sidebar-nav-item:nth-child(5) { animation-delay: 0.25s; }\n.sidebar-nav-item:nth-child(6) { animation-delay: 0.3s; }\n.sidebar-nav-item:nth-child(7) { animation-delay: 0.35s; }\n.sidebar-nav-item:nth-child(8) { animation-delay: 0.4s; }\n\n@keyframes backdropFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes sidebarSlideIn {\n  from {\n    transform: translateX(-400px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes navItemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .sidebar-panel {\n    width: 260px;\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('sidebar-styles');\n  if (!existingStyle) {\n    const styleSheet = document.createElement('style');\n    styleSheet.id = 'sidebar-styles';\n    styleSheet.textContent = sidebarStyles;\n    document.head.appendChild(styleSheet);\n  }\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SACEC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM,GAAG,KAAK;EAAEC,SAAS,GAAGA,CAAA,KAAM,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAG7B,WAAW,CAAC,CAAC;;EAExC;EACA,MAAM8B,kBAAkB,GAAG;IACzBC,GAAG,EAAE,CAAC;IAAE;IACRC,OAAO,EAAE,CAAC;IAAE;IACZC,YAAY,EAAE,CAAC;IAAE;IACjBC,KAAK,EAAE,CAAC;IAAE;IACVC,QAAQ,EAAE,CAAC;IAAE;IACbC,OAAO,EAAE,CAAC;IAAE;IACZC,YAAY,EAAE,CAAC;IAAE;IACjBC,MAAM,EAAE,CAAC,CAAC;EACZ,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACd,MAAM2C,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIpB,MAAM,EAAE;QACpCC,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVqB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;MACnD;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;MACtDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACzB,MAAM,CAAC,CAAC;EAEZ,MAAM2B,eAAe,GAAG,CACtB;IACEC,KAAK,EAAEpB,WAAW,GAAG,OAAO,GAAG,KAAK;IACpCqB,WAAW,EAAErB,WAAW,GAAG,eAAe,GAAG,gBAAgB;IAC7DsB,IAAI,EAAEhD,MAAM;IACZiD,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,2BAA2B;IAClCC,iBAAiB,EAAExB,kBAAkB,CAACC;EACxC,CAAC,EACD;IACEkB,KAAK,EAAEpB,WAAW,GAAG,SAAS,GAAG,SAAS;IAC1CqB,WAAW,EAAErB,WAAW,GAAG,oBAAoB,GAAG,sBAAsB;IACxEsB,IAAI,EAAE1C,QAAQ;IACd2C,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,iBAAiB,EAAExB,kBAAkB,CAACE;EACxC,CAAC,EACD;IACEiB,KAAK,EAAEpB,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDqB,WAAW,EAAErB,WAAW,GAAG,iBAAiB,GAAG,oBAAoB;IACnEsB,IAAI,EAAE7C,OAAO;IACb8C,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,6BAA6B;IACpCC,iBAAiB,EAAExB,kBAAkB,CAACG;EACxC,CAAC,EACD;IACEgB,KAAK,EAAEpB,WAAW,GAAG,QAAQ,GAAG,OAAO;IACvCqB,WAAW,EAAErB,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EsB,IAAI,EAAExC,eAAe;IACrByC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,iBAAiB,EAAExB,kBAAkB,CAACI;EACxC,CAAC,EACD;IACEe,KAAK,EAAEpB,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDqB,WAAW,EAAErB,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEsB,IAAI,EAAE/C,OAAO;IACbgD,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,iCAAiC;IACxCC,iBAAiB,EAAExB,kBAAkB,CAACK;EACxC,CAAC,EACD;IACEc,KAAK,EAAEpB,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCqB,WAAW,EAAErB,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;IAC/DsB,IAAI,EAAEzC,MAAM;IACZ0C,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,+BAA+B;IACtCC,iBAAiB,EAAExB,kBAAkB,CAACM;EACxC,CAAC,EACD;IACEa,KAAK,EAAEpB,WAAW,GAAG,WAAW,GAAG,cAAc;IACjDqB,WAAW,EAAErB,WAAW,GAAG,qBAAqB,GAAG,kBAAkB;IACrEsB,IAAI,EAAEvC,YAAY;IAClBwC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,iBAAiB,EAAExB,kBAAkB,CAACO;EACxC,CAAC,EACD;IACEY,KAAK,EAAEpB,WAAW,GAAG,QAAQ,GAAG,QAAQ;IACxCqB,WAAW,EAAErB,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEsB,IAAI,EAAEtC,QAAQ;IACduC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,yBAAyB;IAChCC,iBAAiB,EAAExB,kBAAkB,CAACQ;EACxC,CAAC,CACF;EAED,MAAMiB,gBAAgB,GAAIH,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBI,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLhC,QAAQ,CAAC4B,IAAI,CAAC;IAChB;IACA9B,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMkC,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/B;IACAC,MAAM,CAAClC,QAAQ,CAACmC,IAAI,GAAG,GAAG;EAC5B,CAAC;EAED,MAAMC,YAAY,GAAIT,IAAI,IAAK;IAC7B,OAAO3B,QAAQ,CAACqC,QAAQ,KAAKV,IAAI,IAAI3B,QAAQ,CAACqC,QAAQ,CAACC,UAAU,CAACX,IAAI,CAAC;EACzE,CAAC;EAED,oBACEnC,OAAA,CAAAE,SAAA;IAAA6C,QAAA,GAIG3C,MAAM,iBACLJ,OAAA;MACEgD,OAAO,EAAEA,CAAA,KAAM3C,SAAS,CAAC,KAAK,CAAE;MAChC4C,SAAS,EAAC;IAAkE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACF,EAGAjD,MAAM,iBACLJ,OAAA;MACEiD,SAAS,EAAC,gFAAgF;MACxFrB,KAAK,EAAE;QACL0B,KAAK,EAAEZ,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGb,MAAM,CAACa,UAAU,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO;QACxFC,QAAQ,EAAEd,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;MACjD,CAAE;MAAAR,QAAA,gBAGF/C,OAAA;QACEiD,SAAS,EAAC,gEAAgE;QAC1ErB,KAAK,EAAE;UACL6B,OAAO,EAAEf,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG;QACpD,CAAE;QAAAR,QAAA,gBAGF/C,OAAA;UACEgD,OAAO,EAAEA,CAAA,KAAM3C,SAAS,CAAC,KAAK,CAAE;UAChC4C,SAAS,EAAC,kFAAkF;UAC5FrB,KAAK,EAAE;YACL8B,GAAG,EAAEhB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;YAC9CI,KAAK,EAAEjB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;YAChDE,OAAO,EAAEf,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;UAC9C,CAAE;UACFvB,KAAK,EAAC,YAAY;UAAAe,QAAA,eAElB/C,OAAA,CAACf,GAAG;YACFgE,SAAS,EAAC,YAAY;YACtBrB,KAAK,EAAE;cACL0B,KAAK,EAAEZ,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDK,MAAM,EAAElB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC9C;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAETrD,OAAA;UACEiD,SAAS,EAAC,aAAa;UACvBrB,KAAK,EAAE;YACLiC,YAAY,EAAEnB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;YACxDO,SAAS,EAAEpB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UACjD,CAAE;UAAAR,QAAA,gBAEF/C,OAAA;YACEiD,SAAS,EAAC,gBAAgB;YAC1BrB,KAAK,EAAE;cACLmC,QAAQ,EAAErB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAChD,CAAE;YAAAR,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrD,OAAA;YACEiD,SAAS,EAAC,eAAe;YACzBrB,KAAK,EAAE;cACLmC,QAAQ,EAAErB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAChD,CAAE;YAAAR,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrD,OAAA;QACEiD,SAAS,EAAC,wBAAwB;QAClCrB,KAAK,EAAE;UACL6B,OAAO,EAAEf,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACnDS,GAAG,EAAEtB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;UAC9CU,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAnB,QAAA,EAEDhB,eAAe,CAACoC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACpC,MAAMC,aAAa,GAAGF,IAAI,CAAClC,IAAI;UAC/B,MAAMqC,QAAQ,GAAGH,IAAI,CAACjC,IAAI,KAAK,QAAQ,IAAIS,YAAY,CAACwB,IAAI,CAACjC,IAAI,CAAC;UAClE,MAAMqC,QAAQ,GAAGJ,IAAI,CAACjC,IAAI,KAAK,QAAQ;UAEvC,oBACEnC,OAAA;YAEEiD,SAAS,EAAC,kBAAkB;YAC5BD,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAAC8B,IAAI,CAACjC,IAAI,CAAE;YAC3Cc,SAAS,EAAG,mFACVsB,QAAQ,GACJ,+CAA+C,GAC/CC,QAAQ,GACR,6CAA6C,GAC7C,8CACL,EAAE;YACH5C,KAAK,EAAE;cACL6B,OAAO,EAAEf,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,UAAU,GAAG,WAAW;cAC5DkB,YAAY,EAAE/B,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YACnD,CAAE;YAAAR,QAAA,gBAEF/C,OAAA;cACEiD,SAAS,EAAC,mBAAmB;cAC7BrB,KAAK,EAAE;gBACLoC,GAAG,EAAEtB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;cAC1C,CAAE;cAAAR,QAAA,gBAEF/C,OAAA;gBACEiD,SAAS,EAAG,+BAA8BmB,IAAI,CAAChC,KAAM,mCAAmC;gBACxFR,KAAK,EAAE;kBACL0B,KAAK,EAAEZ,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDK,MAAM,EAAElB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAR,QAAA,eAEF/C,OAAA,CAACsE,aAAa;kBACZrB,SAAS,EAAC,YAAY;kBACtBrB,KAAK,EAAE;oBACL0B,KAAK,EAAEZ,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;oBACjDK,MAAM,EAAElB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAC9C;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrD,OAAA;gBAAKiD,SAAS,EAAC,kBAAkB;gBAAAF,QAAA,gBAC/B/C,OAAA;kBACEiD,SAAS,EAAG,eACVsB,QAAQ,GACJ,eAAe,GACf,YACL,EAAE;kBACH3C,KAAK,EAAE;oBACLmC,QAAQ,EAAErB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAChD,CAAE;kBAAAR,QAAA,EAEDqB,IAAI,CAACpC;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACJrD,OAAA;kBACEiD,SAAS,EAAG,GACVsB,QAAQ,GACJ,eAAe,GACf,eACL,EAAE;kBACH3C,KAAK,EAAE;oBACLmC,QAAQ,EAAErB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAChD,CAAE;kBAAAR,QAAA,EAEDqB,IAAI,CAACnC;gBAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA,CAACH,cAAc;cACboD,SAAS,EAAG,GACVsB,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;cACH5C,KAAK,EAAE;gBACL0B,KAAK,EAAEZ,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDK,MAAM,EAAElB,MAAM,CAACa,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3EGe,IAAI,CAACjC,IAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4ER,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACH,CAAC;AAEP,CAAC;AAAC/C,EAAA,CA9SIH,aAAa;EAAA,QACAvB,WAAW,EACXC,WAAW,EACXC,WAAW,EACDC,WAAW;AAAA;AAAA2F,EAAA,GAJlCvE,aAAa;AAgTnB,eAAeA,aAAa;;AAE5B;AACA,MAAMwE,aAAa,GAAI;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOlD,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMmD,aAAa,GAAGnD,QAAQ,CAACoD,cAAc,CAAC,gBAAgB,CAAC;EAC/D,IAAI,CAACD,aAAa,EAAE;IAClB,MAAME,UAAU,GAAGrD,QAAQ,CAACsD,aAAa,CAAC,OAAO,CAAC;IAClDD,UAAU,CAACE,EAAE,GAAG,gBAAgB;IAChCF,UAAU,CAACG,WAAW,GAAGN,aAAa;IACtClD,QAAQ,CAACyD,IAAI,CAACC,WAAW,CAACL,UAAU,CAAC;EACvC;AACF;AAAC,IAAAJ,EAAA;AAAAU,YAAA,CAAAV,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}