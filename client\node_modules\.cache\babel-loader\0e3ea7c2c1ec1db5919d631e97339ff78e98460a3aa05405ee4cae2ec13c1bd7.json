{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\"Karibu tena\", \"Habari za asubuhi\", \"Umefika salama\", \"Karibu sana\", \"Hujambo\", \"Habari za leo\", \"Karibu kwenye mafunzo\", \"Umekuja vizuri\", \"Karibu darasani\", \"Habari za masomo\"] : [\"Welcome back\", \"Great to see you\", \"Hello there\", \"Good to have you here\", \"Nice to see you again\", \"Ready to learn\", \"Let's get started\", \"Time to shine\", \"Here we go\", \"Ready for success\"];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome(prev => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700'\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full max-w-4xl mx-auto flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center\",\n              style: {\n                maxWidth: '800px',\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 w-1/4\",\n            children: user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [welcomeMessages[currentWelcome], \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"QVrrehD5i6wlocOUY5BYhWKzqMg=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "ProfilePicture", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "currentWelcome", "setCurrentWelcome", "inspiringQuotes", "welcomeMessages", "interval", "setInterval", "prev", "length", "clearInterval", "welcomeInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "header", "initial", "y", "opacity", "animate", "div", "scale", "transition", "duration", "delay", "style", "max<PERSON><PERSON><PERSON>", "width", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "fontWeight", "backgroundColor", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "size", "showOnlineStatus", "height", "name", "level", "class", "firstName", "marginRight", "marginLeft", "fontSize", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "cursor", "touchAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\n    \"Karibu tena\",\n    \"Habari za asubuhi\",\n    \"Umefika salama\",\n    \"Karibu sana\",\n    \"Hujambo\",\n    \"Habari za leo\",\n    \"Karibu kwenye mafunzo\",\n    \"Umekuja vizuri\",\n    \"Karibu darasani\",\n    \"Habari za masomo\"\n  ] : [\n    \"Welcome back\",\n    \"Great to see you\",\n    \"Hello there\",\n    \"Good to have you here\",\n    \"Nice to see you again\",\n    \"Ready to learn\",\n    \"Let's get started\",\n    \"Time to shine\",\n    \"Here we go\",\n    \"Ready for success\"\n  ];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome((prev) => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      {/* Header with User Profile and Notification Bell - Same as Home */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\"\n      >\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-center h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Center Section - Brainwave Title Only - Aligned with Welcome Message */}\n            <div className=\"w-full max-w-4xl mx-auto flex justify-center\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center\"\n                style={{\n                  maxWidth: '800px',\n                  width: '100%'\n                }}\n              >\n                {/* Amazing Animated Brainwave Text */}\n                <div className=\"relative brainwave-container\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects (no space) */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n\n            {/* Right Section - User Profile */}\n            <div className=\"flex items-center justify-end space-x-2 w-1/4\">\n              {user && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                  className=\"flex items-center space-x-2 group\"\n                >\n                  {/* Profile Picture with Online Status */}\n                  <ProfilePicture\n                    user={user}\n                    size=\"sm\"\n                    showOnlineStatus={true}\n                    style={{\n                      width: '32px',\n                      height: '32px'\n                    }}\n                  />\n\n                  {/* User Name and Class */}\n                  <div className=\"hidden sm:block text-right\">\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\n                      {user?.name || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {welcomeMessages[currentWelcome]}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGzB,WAAW,CAAC,CAAC;EACxC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAKvD;EACA,MAAMoC,eAAe,GAAGL,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA,MAAMM,eAAe,GAAGN,WAAW,GAAG,CACpC,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,CACnB,GAAG,CACF,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,mBAAmB,CACpB;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMqC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCN,eAAe,CAAEO,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIJ,eAAe,CAACK,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,eAAe,CAACK,MAAM,CAAC,CAAC;;EAE5B;EACAxC,SAAS,CAAC,MAAM;IACd,MAAM0C,eAAe,GAAGJ,WAAW,CAAC,MAAM;MACxCJ,iBAAiB,CAAEK,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACN,eAAe,CAACI,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAzC,OAAO,CAAC0C,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACApB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAMqB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAElB,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDmB,WAAW,EAAEnB,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEoB,IAAI,EAAE1C,gBAAgB;IACtB2C,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3DmB,WAAW,EAAEnB,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClFoB,IAAI,EAAEzC,MAAM;IACZ0C,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDmB,WAAW,EAAEnB,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjFoB,IAAI,EAAE7B,OAAO;IACb8B,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCmB,WAAW,EAAEnB,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EoB,IAAI,EAAExC,WAAW;IACjByC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpDmB,WAAW,EAAEnB,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClEoB,IAAI,EAAElC,QAAQ;IACdmC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvCmB,WAAW,EAAEnB,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEoB,IAAI,EAAEjC,MAAM;IACZkC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhC,OAAA,CAACpB,MAAM,CAACqD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5KhC,OAAA;QAAK+B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDhC,OAAA;UAAK+B,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAE5EhC,OAAA;YAAK+B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,eAC3DhC,OAAA,CAACpB,MAAM,CAAC0D,GAAG;cACTJ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAI,CAAE;cACpCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CX,SAAS,EAAC,iDAAiD;cAC3DY,KAAK,EAAE;gBACLC,QAAQ,EAAE,OAAO;gBACjBC,KAAK,EAAE;cACT,CAAE;cAAAb,QAAA,gBAGFhC,OAAA;gBAAK+B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3ChC,OAAA;kBAAI+B,SAAS,EAAC,gGAAgG;kBAC1GY,KAAK,EAAE;oBACLG,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAAf,QAAA,gBAEJhC,OAAA,CAACpB,MAAM,CAACoE,IAAI;oBACVjB,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEa,CAAC,EAAE,CAAC,EAAE;sBAAEV,KAAK,EAAE;oBAAI,CAAE;oBAC5CF,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVa,CAAC,EAAE,CAAC;sBACJV,KAAK,EAAE,CAAC;sBACRW,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFV,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVQ,UAAU,EAAE;wBACVT,QAAQ,EAAE,CAAC;wBACXU,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVf,KAAK,EAAE,GAAG;sBACVgB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBf,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLd,KAAK,EAAE,SAAS;sBAChB2B,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlB,QAAA,GACH,OAGC,eACAhC,OAAA,CAACpB,MAAM,CAAC0D,GAAG;sBACTP,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBG,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBkB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFjB,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbU,MAAM,EAAEC,QAAQ;wBAChBV,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLc,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGd9D,OAAA,CAACpB,MAAM,CAACoE,IAAI;oBACVjB,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEa,CAAC,EAAE,EAAE;sBAAEV,KAAK,EAAE;oBAAI,CAAE;oBAC3CF,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVa,CAAC,EAAE,CAAC;sBACJV,KAAK,EAAE,CAAC;sBACRJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnBe,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFV,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVP,CAAC,EAAE;wBACDM,QAAQ,EAAE,CAAC;wBACXU,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVT,QAAQ,EAAE,GAAG;wBACbU,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVf,KAAK,EAAE,GAAG;sBACVgB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBf,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLd,KAAK,EAAE,SAAS;sBAChB2B,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlB,QAAA,GACH,MAGC,eACAhC,OAAA,CAACpB,MAAM,CAAC0D,GAAG;sBACTP,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBa,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACdd,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBsB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFjB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXU,MAAM,EAAEC,QAAQ;wBAChBV,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLc,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGL9D,OAAA,CAACpB,MAAM,CAAC0D,GAAG;kBACTP,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEW,KAAK,EAAE,CAAC;oBAAET,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPQ,KAAK,EAAE,MAAM;oBACbT,OAAO,EAAE,CAAC;oBACVsB,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVgB,SAAS,EAAE;sBACTjB,QAAQ,EAAE,CAAC;sBACXU,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFV,KAAK,EAAE;oBACLoB,UAAU,EAAE,mDAAmD;oBAC/DL,SAAS,EAAE;kBACb;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9D,OAAA;gBAAK+B,SAAS,EAAC;cAAyK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN9D,OAAA;YAAK+B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC3D5B,IAAI,iBACHJ,OAAA,CAACpB,MAAM,CAAC0D,GAAG;cACTJ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAI,CAAE;cACpCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CX,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7ChC,OAAA,CAACjB,cAAc;gBACbqB,IAAI,EAAEA,IAAK;gBACX4D,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvBtB,KAAK,EAAE;kBACLE,KAAK,EAAE,MAAM;kBACbqB,MAAM,EAAE;gBACV;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGF9D,OAAA;gBAAK+B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzChC,OAAA;kBAAK+B,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,EACnH,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,IAAI,KAAI;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACN9D,OAAA;kBAAK+B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAC5F,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,KAAK,MAAK,SAAS,GAAI,SAAQhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,KAAM,EAAC,GAAGjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB9D,OAAA;MAAK+B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhC,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhC,OAAA;UAAI+B,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBnB,eAAe,CAACH,cAAc,CAAC,EAAC,IAAE,EAAC,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,SAAS,MAAIlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,IAAI,MAAK5D,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACL9D,OAAA;UAAG+B,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBzB,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJ9D,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA,CAACN,MAAM;YAACiD,KAAK,EAAE;cAAEd,KAAK,EAAE,SAAS;cAAE0C,WAAW,EAAE;YAAS;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAClD,eAAe,CAACJ,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAACiD,KAAK,EAAE;cAAEd,KAAK,EAAE,SAAS;cAAE2C,UAAU,EAAE;YAAS;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D9D,OAAA;YAAK2C,KAAK,EAAE;cAAE8B,QAAQ,EAAE,UAAU;cAAE5C,KAAK,EAAE,SAAS;cAAE6C,SAAS,EAAE;YAAS,CAAE;YAAA1C,QAAA,EAAC;UAE7E;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMN9D,OAAA;QAAK+B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChC,OAAA;UAAK+B,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAACmD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACjD,IAAI;YAC/B,oBACE3B,OAAA,CAACpB,MAAM,CAAC0D,GAAG;cAETJ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BK,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEmC,KAAK,GAAG;cAAI,CAAE;cAClD9C,SAAS,EAAG,kBAAiB6C,IAAI,CAAC9C,UAAW,IAAG8C,IAAI,CAAC/C,KAAM,EAAE;cAC7DkD,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAACyE,IAAI,CAAChD,IAAI,CAAE;cACnCoD,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtCjF,QAAQ,CAACyE,IAAI,CAAChD,IAAI,CAAC;gBACrB;cACF,CAAE;cACFe,KAAK,EAAE;gBACL0C,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAAtD,QAAA,gBAIFhC,OAAA;gBAAK+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BhC,OAAA,CAAC8E,aAAa;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAEN9D,OAAA;gBAAI+B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3B4C,IAAI,CAACnD;cAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEL9D,OAAA;gBAAG+B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChC4C,IAAI,CAAClD;cAAW;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GA9BCc,IAAI,CAACnD,KAAK;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9D,OAAA,CAACpB,MAAM,CAAC0D,GAAG;UACTJ,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CX,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjChC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChC,OAAA,CAACR,eAAe;cAACuC,SAAS,EAAC;YAAuC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE9D,OAAA;cAAAgC,QAAA,EAAM;YAAkC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C9D,OAAA,CAACL,QAAQ;cAACoC,SAAS,EAAC;YAAuC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CApcID,GAAG;EAAA,QACUvB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAAyG,EAAA,GAHlCtF,GAAG;AAscT,eAAeA,GAAG;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}