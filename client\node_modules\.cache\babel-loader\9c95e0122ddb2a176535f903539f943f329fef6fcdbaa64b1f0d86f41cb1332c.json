{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\UserReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './index.css';\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag, Modal, Descriptions } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { motion } from \"framer-motion\";\nimport { TbTrophy, TbTarget, TbTrendingUp, TbCalendar, Tb<PERSON><PERSON>, TbA<PERSON>, TbChartBar, TbD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>, <PERSON>b<PERSON><PERSON><PERSON> } from \"react-icons/tb\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nfunction UserReports() {\n  _s();\n  var _selectedReport$exam, _selectedReport$exam2, _selectedReport$exam3, _selectedReport$exam4, _selectedReport$resul, _selectedReport$resul2, _selectedReport$exam5, _selectedReport$resul3, _selectedReport$resul4, _selectedReport$resul5, _selectedReport$resul6, _selectedReport$exam6, _selectedReport$resul7, _selectedReport$resul8, _selectedReport$exam7, _selectedReport$resul9, _selectedReport$resul10, _selectedReport$resul11, _selectedReport$resul12, _selectedReport$resul13;\n  const [reportsData, setReportsData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [filterSubject, setFilterSubject] = useState('all');\n  const [filterVerdict, setFilterVerdict] = useState('all');\n  const [dateRange, setDateRange] = useState(null);\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\n  const [stats, setStats] = useState({\n    totalExams: 0,\n    passedExams: 0,\n    averageScore: 0,\n    streak: 0,\n    bestScore: 0\n  });\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  const [isTablet, setIsTablet] = useState(window.innerWidth >= 768 && window.innerWidth < 1024);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const dispatch = useDispatch();\n  const calculateStats = data => {\n    if (!data || data.length === 0) {\n      setStats({\n        totalExams: 0,\n        passedExams: 0,\n        averageScore: 0,\n        streak: 0,\n        bestScore: 0\n      });\n      return;\n    }\n    const totalExams = data.length;\n    const passedExams = data.filter(report => {\n      var _report$result;\n      return ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.verdict) === 'Pass';\n    }).length;\n    const scores = data.map(report => {\n      var _report$result2, _report$result2$corre, _report$exam;\n      const obtained = ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0;\n      const total = ((_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam.totalMarks) || 1;\n      return obtained / total * 100;\n    });\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\n    const bestScore = Math.max(...scores);\n\n    // Calculate streak (consecutive passes)\n    let currentStreak = 0;\n    let maxStreak = 0;\n    for (let i = data.length - 1; i >= 0; i--) {\n      var _data$i$result;\n      if (((_data$i$result = data[i].result) === null || _data$i$result === void 0 ? void 0 : _data$i$result.verdict) === 'Pass') {\n        currentStreak++;\n        maxStreak = Math.max(maxStreak, currentStreak);\n      } else {\n        currentStreak = 0;\n      }\n    }\n    setStats({\n      totalExams,\n      passedExams,\n      averageScore: Math.round(averageScore),\n      streak: maxStreak,\n      bestScore: Math.round(bestScore)\n    });\n  };\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(response.data);\n        setFilteredData(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const applyFilters = () => {\n    let filtered = [...reportsData];\n    if (filterSubject !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$exam2, _report$exam2$subject;\n        return (_report$exam2 = report.exam) === null || _report$exam2 === void 0 ? void 0 : (_report$exam2$subject = _report$exam2.subject) === null || _report$exam2$subject === void 0 ? void 0 : _report$exam2$subject.toLowerCase().includes(filterSubject.toLowerCase());\n      });\n    }\n    if (filterVerdict !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$result3;\n        return ((_report$result3 = report.result) === null || _report$result3 === void 0 ? void 0 : _report$result3.verdict) === filterVerdict;\n      });\n    }\n    if (dateRange && dateRange.length === 2) {\n      filtered = filtered.filter(report => {\n        const reportDate = moment(report.createdAt);\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\n      });\n    }\n    setFilteredData(filtered);\n    calculateStats(filtered);\n  };\n  useEffect(() => {\n    getData();\n  }, []);\n  useEffect(() => {\n    applyFilters();\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-blue-600';\n    if (score >= 40) return 'text-orange-600';\n    return 'text-red-600';\n  };\n  const getVerdictIcon = verdict => {\n    return verdict === 'Pass' ? /*#__PURE__*/_jsxDEV(TbCheck, {\n      className: \"w-5 h-5 text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n      className: \"w-5 h-5 text-red-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this);\n  };\n  const getUniqueSubjects = () => {\n    const subjects = reportsData.map(report => {\n      var _report$exam3;\n      return (_report$exam3 = report.exam) === null || _report$exam3 === void 0 ? void 0 : _report$exam3.subject;\n    }).filter(Boolean);\n    return [...new Set(subjects)];\n  };\n  const handleViewDetails = record => {\n    setSelectedReport(record);\n    setIsModalVisible(true);\n  };\n  const handleCloseModal = () => {\n    setIsModalVisible(false);\n    setSelectedReport(null);\n  };\n  const getResponsiveColumns = () => {\n    const isMobile = window.innerWidth < 768;\n    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;\n    const baseColumns = [{\n      title: 'Exam',\n      dataIndex: 'examName',\n      key: 'examName',\n      render: (text, record) => {\n        var _record$exam, _record$exam2;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold text-gray-900 text-sm sm:text-base truncate\",\n            children: ((_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name) || 'Unnamed Exam'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs sm:text-sm text-gray-500 truncate\",\n            children: ((_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.subject) || 'General'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400\",\n              children: moment(record.createdAt).format(\"MMM DD, YYYY\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 25\n              }, this),\n              onClick: () => handleViewDetails(record),\n              className: \"text-blue-500 p-0 h-auto\",\n              children: \"View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this);\n      },\n      width: isMobile ? 180 : isTablet ? 200 : 250,\n      ellipsis: true\n    }, {\n      title: 'Score',\n      dataIndex: 'score',\n      key: 'score',\n      render: (text, record) => {\n        var _record$result, _record$result$correc, _record$exam3;\n        const obtained = ((_record$result = record.result) === null || _record$result === void 0 ? void 0 : (_record$result$correc = _record$result.correctAnswers) === null || _record$result$correc === void 0 ? void 0 : _record$result$correc.length) || 0;\n        const total = ((_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.totalMarks) || 1;\n        const percentage = Math.round(obtained / total * 100);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm sm:text-base font-bold text-gray-900\",\n            children: [obtained, \"/\", total]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: percentage,\n            size: \"small\",\n            strokeColor: percentage >= 60 ? '#10b981' : '#ef4444',\n            showInfo: false,\n            className: \"mb-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-xs sm:text-sm font-medium ${getScoreColor(percentage)}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this);\n      },\n      width: isMobile ? 80 : 120,\n      sorter: (a, b) => {\n        var _a$result, _a$result$correctAnsw, _a$exam, _b$result, _b$result$correctAnsw, _b$exam;\n        const scoreA = Math.round((((_a$result = a.result) === null || _a$result === void 0 ? void 0 : (_a$result$correctAnsw = _a$result.correctAnswers) === null || _a$result$correctAnsw === void 0 ? void 0 : _a$result$correctAnsw.length) || 0) / (((_a$exam = a.exam) === null || _a$exam === void 0 ? void 0 : _a$exam.totalMarks) || 1) * 100);\n        const scoreB = Math.round((((_b$result = b.result) === null || _b$result === void 0 ? void 0 : (_b$result$correctAnsw = _b$result.correctAnswers) === null || _b$result$correctAnsw === void 0 ? void 0 : _b$result$correctAnsw.length) || 0) / (((_b$exam = b.exam) === null || _b$exam === void 0 ? void 0 : _b$exam.totalMarks) || 1) * 100);\n        return scoreA - scoreB;\n      }\n    }, {\n      title: 'Result',\n      dataIndex: 'verdict',\n      key: 'verdict',\n      render: (text, record) => {\n        var _record$result2;\n        const verdict = (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict;\n        const isPassed = verdict === 'Pass';\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          icon: !isMobile ? getVerdictIcon(verdict) : null,\n          color: isPassed ? 'success' : 'error',\n          className: \"font-medium text-xs sm:text-sm\",\n          children: isMobile ? isPassed ? 'P' : 'F' : verdict || 'N/A'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this);\n      },\n      width: isMobile ? 50 : 100,\n      filters: !isMobile ? [{\n        text: 'Pass',\n        value: 'Pass'\n      }, {\n        text: 'Fail',\n        value: 'Fail'\n      }] : undefined,\n      onFilter: !isMobile ? (value, record) => {\n        var _record$result3;\n        return ((_record$result3 = record.result) === null || _record$result3 === void 0 ? void 0 : _record$result3.verdict) === value;\n      } : undefined\n    }];\n\n    // Add date column for tablet and desktop\n    if (!isMobile) {\n      baseColumns.splice(1, 0, {\n        title: 'Date',\n        dataIndex: 'createdAt',\n        key: 'date',\n        render: date => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium\",\n            children: moment(date).format(\"MMM DD, YYYY\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500\",\n            children: moment(date).format(\"HH:mm\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this),\n        width: isTablet ? 100 : 120\n      });\n    }\n\n    // Add actions column for desktop\n    if (!isMobile) {\n      baseColumns.push({\n        title: 'Actions',\n        key: 'actions',\n        render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 19\n          }, this),\n          onClick: () => handleViewDetails(record),\n          className: \"bg-blue-500 hover:bg-blue-600\",\n          children: isTablet ? '' : 'View'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this),\n        width: isTablet ? 60 : 80\n      });\n    }\n    return baseColumns;\n  };\n  const columns = getResponsiveColumns();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Performance Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 sm:mb-10 lg:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: [\"Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 18\n          }, this), \" Journey\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4\",\n          children: \"Track your progress, analyze your performance, and celebrate your achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Exams\",\n              value: stats.totalExams,\n              valueStyle: {\n                color: '#1e40af',\n                fontWeight: 'bold',\n                fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px',\n                textAlign: 'center'\n              },\n              className: \"responsive-statistic text-center\",\n              style: {\n                textAlign: 'center'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Passed\",\n              value: stats.passedExams,\n              valueStyle: {\n                color: '#059669',\n                fontWeight: 'bold',\n                fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\n              },\n              className: \"responsive-statistic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Average Score\",\n              value: stats.averageScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#7c3aed',\n                fontWeight: 'bold',\n                fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\n              },\n              className: \"responsive-statistic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-orange-600/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Score\",\n              value: stats.bestScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#ea580c',\n                fontWeight: 'bold',\n                fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\n              },\n              className: \"responsive-statistic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 via-pink-100 to-pink-200 relative overflow-hidden sm:col-span-3 lg:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-br from-pink-500/5 to-pink-600/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Streak\",\n              value: stats.streak,\n              valueStyle: {\n                color: '#db2777',\n                fontWeight: 'bold',\n                fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\n              },\n              className: \"responsive-statistic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Filter Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Subject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"All Subjects\",\n                value: filterSubject,\n                onChange: setFilterSubject,\n                className: \"w-full\",\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"all\",\n                  children: \"All Subjects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this), getUniqueSubjects().map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                  value: subject,\n                  children: subject\n                }, subject, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Result\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"All Results\",\n                value: filterVerdict,\n                onChange: setFilterVerdict,\n                className: \"w-full\",\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"all\",\n                  children: \"All Results\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Pass\",\n                  children: \"Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Fail\",\n                  children: \"Failed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 sm:col-span-2 lg:col-span-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Date Range\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n                value: dateRange,\n                onChange: setDateRange,\n                className: \"w-full\",\n                size: \"large\",\n                placeholder: ['From', 'To'],\n                format: \"DD/MM/YYYY\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 opacity-0\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  setFilterSubject('all');\n                  setFilterVerdict('all');\n                  setDateRange(null);\n                },\n                size: \"large\",\n                className: \"w-full\",\n                icon: /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 25\n                }, this),\n                children: \"Clear All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), (filterSubject !== 'all' || filterVerdict !== 'all' || dateRange) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2 pt-3 border-t border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Active filters:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), filterSubject !== 'all' && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setFilterSubject('all'),\n              className: \"bg-blue-50 border-blue-200 text-blue-700\",\n              children: filterSubject\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 19\n            }, this), filterVerdict !== 'all' && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setFilterVerdict('all'),\n              className: filterVerdict === 'Pass' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700',\n              children: filterVerdict\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 19\n            }, this), dateRange && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setDateRange(null),\n              className: \"bg-purple-50 border-purple-200 text-purple-700\",\n              children: [dateRange[0].format('DD/MM/YY'), \" - \", dateRange[1].format('DD/MM/YY')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: filteredData,\n          rowKey: record => record._id,\n          pagination: {\n            pageSize: window.innerWidth < 768 ? 5 : 10,\n            showSizeChanger: window.innerWidth >= 768,\n            showQuickJumper: window.innerWidth >= 768,\n            showTotal: (total, range) => window.innerWidth >= 640 ? `${range[0]}-${range[1]} of ${total} results` : `${range[0]}-${range[1]} / ${total}`,\n            className: \"px-3 sm:px-6 py-2 sm:py-4\",\n            simple: window.innerWidth < 640\n          },\n          scroll: {\n            x: window.innerWidth < 768 ? 600 : 800\n          },\n          className: \"modern-table\",\n          size: window.innerWidth < 768 ? \"middle\" : \"large\",\n          locale: {\n            emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n              image: Empty.PRESENTED_IMAGE_SIMPLE,\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-base sm:text-lg font-medium text-gray-900 mb-2\",\n                  children: \"No exam results found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm sm:text-base text-gray-500 px-4\",\n                  children: \"Try adjusting your filters or take some exams to see your results here.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbEye, {\n              className: \"w-4 h-4 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg font-semibold\",\n            children: \"Exam Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this),\n        open: isModalVisible,\n        onCancel: handleCloseModal,\n        footer: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseModal,\n          size: \"large\",\n          children: \"Close\"\n        }, \"close\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this)],\n        width: isMobile ? '95%' : isTablet ? 600 : 700,\n        className: \"exam-details-modal\",\n        children: selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Exam Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n              column: isMobile ? 1 : 2,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"Exam Name\",\n                span: isMobile ? 1 : 2,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: ((_selectedReport$exam = selectedReport.exam) === null || _selectedReport$exam === void 0 ? void 0 : _selectedReport$exam.name) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"Subject\",\n                children: ((_selectedReport$exam2 = selectedReport.exam) === null || _selectedReport$exam2 === void 0 ? void 0 : _selectedReport$exam2.subject) || 'General'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"Date Taken\",\n                children: moment(selectedReport.createdAt).format(\"MMMM DD, YYYY [at] HH:mm\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"Total Questions\",\n                children: ((_selectedReport$exam3 = selectedReport.exam) === null || _selectedReport$exam3 === void 0 ? void 0 : _selectedReport$exam3.totalMarks) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"Passing Marks\",\n                children: ((_selectedReport$exam4 = selectedReport.exam) === null || _selectedReport$exam4 === void 0 ? void 0 : _selectedReport$exam4.passingMarks) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Performance Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-3 bg-white rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: ((_selectedReport$resul = selectedReport.result) === null || _selectedReport$resul === void 0 ? void 0 : (_selectedReport$resul2 = _selectedReport$resul.correctAnswers) === null || _selectedReport$resul2 === void 0 ? void 0 : _selectedReport$resul2.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Correct Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-3 bg-white rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-gray-600\",\n                  children: (((_selectedReport$exam5 = selectedReport.exam) === null || _selectedReport$exam5 === void 0 ? void 0 : _selectedReport$exam5.totalMarks) || 0) - (((_selectedReport$resul3 = selectedReport.result) === null || _selectedReport$resul3 === void 0 ? void 0 : (_selectedReport$resul4 = _selectedReport$resul3.correctAnswers) === null || _selectedReport$resul4 === void 0 ? void 0 : _selectedReport$resul4.length) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Wrong Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-3 bg-white rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-2xl font-bold ${Math.round((((_selectedReport$resul5 = selectedReport.result) === null || _selectedReport$resul5 === void 0 ? void 0 : (_selectedReport$resul6 = _selectedReport$resul5.correctAnswers) === null || _selectedReport$resul6 === void 0 ? void 0 : _selectedReport$resul6.length) || 0) / (((_selectedReport$exam6 = selectedReport.exam) === null || _selectedReport$exam6 === void 0 ? void 0 : _selectedReport$exam6.totalMarks) || 1) * 100) >= 60 ? 'text-green-600' : 'text-red-600'}`,\n                  children: [Math.round((((_selectedReport$resul7 = selectedReport.result) === null || _selectedReport$resul7 === void 0 ? void 0 : (_selectedReport$resul8 = _selectedReport$resul7.correctAnswers) === null || _selectedReport$resul8 === void 0 ? void 0 : _selectedReport$resul8.length) || 0) / (((_selectedReport$exam7 = selectedReport.exam) === null || _selectedReport$exam7 === void 0 ? void 0 : _selectedReport$exam7.totalMarks) || 1) * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg p-4 ${((_selectedReport$resul9 = selectedReport.result) === null || _selectedReport$resul9 === void 0 ? void 0 : _selectedReport$resul9.verdict) === 'Pass' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-3\",\n              children: [getVerdictIcon((_selectedReport$resul10 = selectedReport.result) === null || _selectedReport$resul10 === void 0 ? void 0 : _selectedReport$resul10.verdict), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-xl font-semibold ${((_selectedReport$resul11 = selectedReport.result) === null || _selectedReport$resul11 === void 0 ? void 0 : _selectedReport$resul11.verdict) === 'Pass' ? 'text-green-700' : 'text-red-700'}`,\n                children: ((_selectedReport$resul12 = selectedReport.result) === null || _selectedReport$resul12 === void 0 ? void 0 : _selectedReport$resul12.verdict) === 'Pass' ? 'Congratulations! You Passed' : 'Keep Trying! You Can Do Better'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: ((_selectedReport$resul13 = selectedReport.result) === null || _selectedReport$resul13 === void 0 ? void 0 : _selectedReport$resul13.verdict) === 'Pass' ? 'Great job on passing this exam!' : 'Review the material and try again to improve your score.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 322,\n    columnNumber: 5\n  }, this);\n}\n_s(UserReports, \"BJMX/r5JtQ8m4Fk03wWS7ZEhBss=\", false, function () {\n  return [useDispatch];\n});\n_c = UserReports;\nexport default UserReports;\nvar _c;\n$RefreshReg$(_c, \"UserReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Page<PERSON><PERSON>le", "message", "Card", "Progress", "Statistic", "Select", "DatePicker", "<PERSON><PERSON>", "Empty", "Table", "Tag", "Modal", "Descriptions", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsByUser", "motion", "TbTrophy", "TbTarget", "TbTrendingUp", "TbCalendar", "TbClock", "TbAward", "TbChartBar", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheck", "TbX", "TbFlame", "moment", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "UserReports", "_s", "_selectedReport$exam", "_selectedReport$exam2", "_selectedReport$exam3", "_selectedReport$exam4", "_selectedReport$resul", "_selectedReport$resul2", "_selectedReport$exam5", "_selectedReport$resul3", "_selectedReport$resul4", "_selectedReport$resul5", "_selectedReport$resul6", "_selectedReport$exam6", "_selectedReport$resul7", "_selectedReport$resul8", "_selectedReport$exam7", "_selectedReport$resul9", "_selectedReport$resul10", "_selectedReport$resul11", "_selectedReport$resul12", "_selectedReport$resul13", "reportsData", "setReportsData", "filteredData", "setFilteredData", "filterSubject", "setFilterSubject", "filterVerdict", "setFilterVerdict", "date<PERSON><PERSON><PERSON>", "setDateRange", "viewMode", "setViewMode", "stats", "setStats", "totalExams", "passedExams", "averageScore", "streak", "bestScore", "isMobile", "setIsMobile", "window", "innerWidth", "isTablet", "setIsTablet", "isModalVisible", "setIsModalVisible", "selectedReport", "setSelectedReport", "dispatch", "calculateStats", "data", "length", "filter", "report", "_report$result", "result", "verdict", "scores", "map", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "total", "exam", "totalMarks", "reduce", "sum", "score", "Math", "max", "currentStreak", "maxStreak", "i", "_data$i$result", "round", "getData", "response", "success", "error", "applyFilters", "filtered", "_report$exam2", "_report$exam2$subject", "subject", "toLowerCase", "includes", "_report$result3", "reportDate", "createdAt", "isBetween", "handleResize", "addEventListener", "removeEventListener", "getScoreColor", "getVerdictIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getUniqueSubjects", "subjects", "_report$exam3", "Boolean", "Set", "handleViewDetails", "record", "handleCloseModal", "getResponsiveColumns", "baseColumns", "title", "dataIndex", "key", "render", "text", "_record$exam", "_record$exam2", "children", "name", "format", "type", "size", "icon", "onClick", "width", "ellipsis", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "strokeColor", "showInfo", "sorter", "a", "b", "_a$result", "_a$result$correctAnsw", "_a$exam", "_b$result", "_b$result$correctAnsw", "_b$exam", "scoreA", "scoreB", "_record$result2", "isPassed", "color", "filters", "value", "undefined", "onFilter", "_record$result3", "splice", "date", "push", "columns", "div", "initial", "opacity", "y", "animate", "transition", "delay", "valueStyle", "fontWeight", "fontSize", "textAlign", "style", "suffix", "placeholder", "onChange", "allowClear", "closable", "onClose", "dataSource", "<PERSON><PERSON><PERSON>", "_id", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "simple", "scroll", "x", "locale", "emptyText", "image", "PRESENTED_IMAGE_SIMPLE", "description", "open", "onCancel", "footer", "column", "<PERSON><PERSON>", "label", "span", "passingMarks", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/UserReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag, Modal, Descriptions } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbTrophy,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbAward,\r\n  TbChartBar,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbFlame\r\n} from \"react-icons/tb\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [filteredData, setFilteredData] = useState([]);\r\n  const [filterSubject, setFilterSubject] = useState('all');\r\n  const [filterVerdict, setFilterVerdict] = useState('all');\r\n  const [dateRange, setDateRange] = useState(null);\r\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\r\n  const [stats, setStats] = useState({\r\n    totalExams: 0,\r\n    passedExams: 0,\r\n    averageScore: 0,\r\n    streak: 0,\r\n    bestScore: 0\r\n  });\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\r\n  const [isTablet, setIsTablet] = useState(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n  const [isModalVisible, setIsModalVisible] = useState(false);\r\n  const [selectedReport, setSelectedReport] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) {\r\n      setStats({\r\n        totalExams: 0,\r\n        passedExams: 0,\r\n        averageScore: 0,\r\n        streak: 0,\r\n        bestScore: 0\r\n      });\r\n      return;\r\n    }\r\n\r\n    const totalExams = data.length;\r\n    const passedExams = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\r\n    const bestScore = Math.max(...scores);\r\n\r\n    // Calculate streak (consecutive passes)\r\n    let currentStreak = 0;\r\n    let maxStreak = 0;\r\n    for (let i = data.length - 1; i >= 0; i--) {\r\n      if (data[i].result?.verdict === 'Pass') {\r\n        currentStreak++;\r\n        maxStreak = Math.max(maxStreak, currentStreak);\r\n      } else {\r\n        currentStreak = 0;\r\n      }\r\n    }\r\n\r\n    setStats({\r\n      totalExams,\r\n      passedExams,\r\n      averageScore: Math.round(averageScore),\r\n      streak: maxStreak,\r\n      bestScore: Math.round(bestScore)\r\n    });\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setFilteredData(response.data);\r\n        calculateStats(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...reportsData];\r\n\r\n    if (filterSubject !== 'all') {\r\n      filtered = filtered.filter(report =>\r\n        report.exam?.subject?.toLowerCase().includes(filterSubject.toLowerCase())\r\n      );\r\n    }\r\n\r\n    if (filterVerdict !== 'all') {\r\n      filtered = filtered.filter(report => report.result?.verdict === filterVerdict);\r\n    }\r\n\r\n    if (dateRange && dateRange.length === 2) {\r\n      filtered = filtered.filter(report => {\r\n        const reportDate = moment(report.createdAt);\r\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\r\n      });\r\n    }\r\n\r\n    setFilteredData(filtered);\r\n    calculateStats(filtered);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  const getScoreColor = (score) => {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-blue-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  };\r\n\r\n  const getVerdictIcon = (verdict) => {\r\n    return verdict === 'Pass' ?\r\n      <TbCheck className=\"w-5 h-5 text-green-600\" /> :\r\n      <TbX className=\"w-5 h-5 text-red-600\" />;\r\n  };\r\n\r\n  const getUniqueSubjects = () => {\r\n    const subjects = reportsData.map(report => report.exam?.subject).filter(Boolean);\r\n    return [...new Set(subjects)];\r\n  };\r\n\r\n  const handleViewDetails = (record) => {\r\n    setSelectedReport(record);\r\n    setIsModalVisible(true);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setIsModalVisible(false);\r\n    setSelectedReport(null);\r\n  };\r\n\r\n  const getResponsiveColumns = () => {\r\n    const isMobile = window.innerWidth < 768;\r\n    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;\r\n\r\n    const baseColumns = [\r\n      {\r\n        title: 'Exam',\r\n        dataIndex: 'examName',\r\n        key: 'examName',\r\n        render: (text, record) => (\r\n          <div className=\"min-w-0\">\r\n            <div className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">\r\n              {record.exam?.name || 'Unnamed Exam'}\r\n            </div>\r\n            <div className=\"text-xs sm:text-sm text-gray-500 truncate\">\r\n              {record.exam?.subject || 'General'}\r\n            </div>\r\n            {isMobile && (\r\n              <div className=\"flex items-center justify-between mt-2\">\r\n                <div className=\"text-xs text-gray-400\">\r\n                  {moment(record.createdAt).format(\"MMM DD, YYYY\")}\r\n                </div>\r\n                <Button\r\n                  type=\"link\"\r\n                  size=\"small\"\r\n                  icon={<TbEye />}\r\n                  onClick={() => handleViewDetails(record)}\r\n                  className=\"text-blue-500 p-0 h-auto\"\r\n                >\r\n                  View\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ),\r\n        width: isMobile ? 180 : isTablet ? 200 : 250,\r\n        ellipsis: true,\r\n      },\r\n      {\r\n        title: 'Score',\r\n        dataIndex: 'score',\r\n        key: 'score',\r\n        render: (text, record) => {\r\n          const obtained = record.result?.correctAnswers?.length || 0;\r\n          const total = record.exam?.totalMarks || 1;\r\n          const percentage = Math.round((obtained / total) * 100);\r\n\r\n          return (\r\n            <div className=\"text-center\">\r\n              <div className=\"text-sm sm:text-base font-bold text-gray-900\">\r\n                {obtained}/{total}\r\n              </div>\r\n              <Progress\r\n                percent={percentage}\r\n                size=\"small\"\r\n                strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n                showInfo={false}\r\n                className=\"mb-1\"\r\n              />\r\n              <div className={`text-xs sm:text-sm font-medium ${getScoreColor(percentage)}`}>\r\n                {percentage}%\r\n              </div>\r\n            </div>\r\n          );\r\n        },\r\n        width: isMobile ? 80 : 120,\r\n        sorter: (a, b) => {\r\n          const scoreA = Math.round(((a.result?.correctAnswers?.length || 0) / (a.exam?.totalMarks || 1)) * 100);\r\n          const scoreB = Math.round(((b.result?.correctAnswers?.length || 0) / (b.exam?.totalMarks || 1)) * 100);\r\n          return scoreA - scoreB;\r\n        },\r\n      },\r\n      {\r\n        title: 'Result',\r\n        dataIndex: 'verdict',\r\n        key: 'verdict',\r\n        render: (text, record) => {\r\n          const verdict = record.result?.verdict;\r\n          const isPassed = verdict === 'Pass';\r\n\r\n          return (\r\n            <Tag\r\n              icon={!isMobile ? getVerdictIcon(verdict) : null}\r\n              color={isPassed ? 'success' : 'error'}\r\n              className=\"font-medium text-xs sm:text-sm\"\r\n            >\r\n              {isMobile ? (isPassed ? 'P' : 'F') : (verdict || 'N/A')}\r\n            </Tag>\r\n          );\r\n        },\r\n        width: isMobile ? 50 : 100,\r\n        filters: !isMobile ? [\r\n          { text: 'Pass', value: 'Pass' },\r\n          { text: 'Fail', value: 'Fail' },\r\n        ] : undefined,\r\n        onFilter: !isMobile ? (value, record) => record.result?.verdict === value : undefined,\r\n      },\r\n    ];\r\n\r\n    // Add date column for tablet and desktop\r\n    if (!isMobile) {\r\n      baseColumns.splice(1, 0, {\r\n        title: 'Date',\r\n        dataIndex: 'createdAt',\r\n        key: 'date',\r\n        render: (date) => (\r\n          <div className=\"text-sm\">\r\n            <div className=\"font-medium\">{moment(date).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-gray-500\">{moment(date).format(\"HH:mm\")}</div>\r\n          </div>\r\n        ),\r\n        width: isTablet ? 100 : 120,\r\n      });\r\n    }\r\n\r\n    // Add actions column for desktop\r\n    if (!isMobile) {\r\n      baseColumns.push({\r\n        title: 'Actions',\r\n        key: 'actions',\r\n        render: (text, record) => (\r\n          <Button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon={<TbEye />}\r\n            onClick={() => handleViewDetails(record)}\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isTablet ? '' : 'View'}\r\n          </Button>\r\n        ),\r\n        width: isTablet ? 60 : 80,\r\n      });\r\n    }\r\n\r\n    return baseColumns;\r\n  };\r\n\r\n  const columns = getResponsiveColumns();\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Performance Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Header removed - using ProtectedRoute header only */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-8 sm:mb-10 lg:mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Journey\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4\">\r\n            Track your progress, analyze your performance, and celebrate your achievements\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10\"></div>\r\n            <div className=\"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\">\r\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\">\r\n                <TbTarget className=\"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{\r\n                  color: '#1e40af',\r\n                  fontWeight: 'bold',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px',\r\n                  textAlign: 'center'\r\n                }}\r\n                className=\"responsive-statistic text-center\"\r\n                style={{ textAlign: 'center' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10\"></div>\r\n            <div className=\"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\">\r\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\">\r\n                <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Passed\"\r\n                value={stats.passedExams}\r\n                valueStyle={{\r\n                  color: '#059669',\r\n                  fontWeight: 'bold',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 relative overflow-hidden\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10\"></div>\r\n            <div className=\"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\">\r\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\">\r\n                <TbTrendingUp className=\"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#7c3aed',\r\n                  fontWeight: 'bold',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 via-orange-100 to-orange-200 relative overflow-hidden\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-orange-600/10\"></div>\r\n            <div className=\"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\">\r\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\">\r\n                <TbTrophy className=\"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Score\"\r\n                value={stats.bestScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#ea580c',\r\n                  fontWeight: 'bold',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 via-pink-100 to-pink-200 relative overflow-hidden sm:col-span-3 lg:col-span-1\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-br from-pink-500/5 to-pink-600/10\"></div>\r\n            <div className=\"relative flex flex-col items-center p-3 sm:p-4 lg:p-6\">\r\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 bg-gradient-to-r from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-3 sm:mb-4 shadow-lg\">\r\n                <TbFlame className=\"w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Streak\"\r\n                value={stats.streak}\r\n                valueStyle={{\r\n                  color: '#db2777',\r\n                  fontWeight: 'bold',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : window.innerWidth < 1024 ? '20px' : '24px'\r\n                }}\r\n                className=\"responsive-statistic\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Results</h3>\r\n            </div>\r\n\r\n            {/* Filter Controls */}\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n              {/* Subject Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Subject</label>\r\n                <Select\r\n                  placeholder=\"All Subjects\"\r\n                  value={filterSubject}\r\n                  onChange={setFilterSubject}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Subjects</Option>\r\n                  {getUniqueSubjects().map(subject => (\r\n                    <Option key={subject} value={subject}>{subject}</Option>\r\n                  ))}\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Result Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Result</label>\r\n                <Select\r\n                  placeholder=\"All Results\"\r\n                  value={filterVerdict}\r\n                  onChange={setFilterVerdict}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Results</Option>\r\n                  <Option value=\"Pass\">Passed</Option>\r\n                  <Option value=\"Fail\">Failed</Option>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Date Range Filter */}\r\n              <div className=\"space-y-2 sm:col-span-2 lg:col-span-1\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Date Range</label>\r\n                <RangePicker\r\n                  value={dateRange}\r\n                  onChange={setDateRange}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                  placeholder={['From', 'To']}\r\n                  format=\"DD/MM/YYYY\"\r\n                  allowClear\r\n                />\r\n              </div>\r\n\r\n              {/* Clear Button */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700 opacity-0\">Actions</label>\r\n                <Button\r\n                  onClick={() => {\r\n                    setFilterSubject('all');\r\n                    setFilterVerdict('all');\r\n                    setDateRange(null);\r\n                  }}\r\n                  size=\"large\"\r\n                  className=\"w-full\"\r\n                  icon={<TbX />}\r\n                >\r\n                  Clear All\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Active Filters Display */}\r\n            {(filterSubject !== 'all' || filterVerdict !== 'all' || dateRange) && (\r\n              <div className=\"flex flex-wrap gap-2 pt-3 border-t border-gray-100\">\r\n                <span className=\"text-sm font-medium text-gray-700\">Active filters:</span>\r\n                {filterSubject !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterSubject('all')}\r\n                    className=\"bg-blue-50 border-blue-200 text-blue-700\"\r\n                  >\r\n                    {filterSubject}\r\n                  </Tag>\r\n                )}\r\n                {filterVerdict !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterVerdict('all')}\r\n                    className={filterVerdict === 'Pass' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'}\r\n                  >\r\n                    {filterVerdict}\r\n                  </Tag>\r\n                )}\r\n                {dateRange && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setDateRange(null)}\r\n                    className=\"bg-purple-50 border-purple-200 text-purple-700\"\r\n                  >\r\n                    {dateRange[0].format('DD/MM/YY')} - {dateRange[1].format('DD/MM/YY')}\r\n                  </Tag>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Exam Results Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={filteredData}\r\n            rowKey={(record) => record._id}\r\n            pagination={{\r\n              pageSize: window.innerWidth < 768 ? 5 : 10,\r\n              showSizeChanger: window.innerWidth >= 768,\r\n              showQuickJumper: window.innerWidth >= 768,\r\n              showTotal: (total, range) =>\r\n                window.innerWidth >= 640\r\n                  ? `${range[0]}-${range[1]} of ${total} results`\r\n                  : `${range[0]}-${range[1]} / ${total}`,\r\n              className: \"px-3 sm:px-6 py-2 sm:py-4\",\r\n              simple: window.innerWidth < 640\r\n            }}\r\n            scroll={{ x: window.innerWidth < 768 ? 600 : 800 }}\r\n            className=\"modern-table\"\r\n            size={window.innerWidth < 768 ? \"middle\" : \"large\"}\r\n            locale={{\r\n              emptyText: (\r\n                <Empty\r\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n                  description={\r\n                    <div className=\"py-8\">\r\n                      <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-2\">No exam results found</h3>\r\n                      <p className=\"text-sm sm:text-base text-gray-500 px-4\">Try adjusting your filters or take some exams to see your results here.</p>\r\n                    </div>\r\n                  }\r\n                />\r\n              )\r\n            }}\r\n          />\r\n        </motion.div>\r\n\r\n        {/* Details Modal */}\r\n        <Modal\r\n          title={\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                <TbEye className=\"w-4 h-4 text-white\" />\r\n              </div>\r\n              <span className=\"text-lg font-semibold\">Exam Details</span>\r\n            </div>\r\n          }\r\n          open={isModalVisible}\r\n          onCancel={handleCloseModal}\r\n          footer={[\r\n            <Button key=\"close\" onClick={handleCloseModal} size=\"large\">\r\n              Close\r\n            </Button>\r\n          ]}\r\n          width={isMobile ? '95%' : isTablet ? 600 : 700}\r\n          className=\"exam-details-modal\"\r\n        >\r\n          {selectedReport && (\r\n            <div className=\"space-y-6\">\r\n              {/* Exam Information */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Exam Information</h3>\r\n                <Descriptions column={isMobile ? 1 : 2} size=\"small\">\r\n                  <Descriptions.Item label=\"Exam Name\" span={isMobile ? 1 : 2}>\r\n                    <span className=\"font-medium\">{selectedReport.exam?.name || 'N/A'}</span>\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Subject\">\r\n                    {selectedReport.exam?.subject || 'General'}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Date Taken\">\r\n                    {moment(selectedReport.createdAt).format(\"MMMM DD, YYYY [at] HH:mm\")}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Total Questions\">\r\n                    {selectedReport.exam?.totalMarks || 0}\r\n                  </Descriptions.Item>\r\n                  <Descriptions.Item label=\"Passing Marks\">\r\n                    {selectedReport.exam?.passingMarks || 0}\r\n                  </Descriptions.Item>\r\n                </Descriptions>\r\n              </div>\r\n\r\n              {/* Performance Summary */}\r\n              <div className=\"bg-blue-50 rounded-lg p-4\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance Summary</h3>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className=\"text-2xl font-bold text-blue-600\">\r\n                      {selectedReport.result?.correctAnswers?.length || 0}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Correct Answers</div>\r\n                  </div>\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className=\"text-2xl font-bold text-gray-600\">\r\n                      {(selectedReport.exam?.totalMarks || 0) - (selectedReport.result?.correctAnswers?.length || 0)}\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Wrong Answers</div>\r\n                  </div>\r\n                  <div className=\"text-center p-3 bg-white rounded-lg\">\r\n                    <div className={`text-2xl font-bold ${\r\n                      Math.round(((selectedReport.result?.correctAnswers?.length || 0) / (selectedReport.exam?.totalMarks || 1)) * 100) >= 60\r\n                        ? 'text-green-600'\r\n                        : 'text-red-600'\r\n                    }`}>\r\n                      {Math.round(((selectedReport.result?.correctAnswers?.length || 0) / (selectedReport.exam?.totalMarks || 1)) * 100)}%\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-600\">Score</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Result Status */}\r\n              <div className={`rounded-lg p-4 ${\r\n                selectedReport.result?.verdict === 'Pass'\r\n                  ? 'bg-green-50 border border-green-200'\r\n                  : 'bg-red-50 border border-red-200'\r\n              }`}>\r\n                <div className=\"flex items-center justify-center gap-3\">\r\n                  {getVerdictIcon(selectedReport.result?.verdict)}\r\n                  <span className={`text-xl font-semibold ${\r\n                    selectedReport.result?.verdict === 'Pass' ? 'text-green-700' : 'text-red-700'\r\n                  }`}>\r\n                    {selectedReport.result?.verdict === 'Pass' ? 'Congratulations! You Passed' : 'Keep Trying! You Can Do Better'}\r\n                  </span>\r\n                </div>\r\n                <div className=\"text-center mt-2\">\r\n                  <span className=\"text-sm text-gray-600\">\r\n                    {selectedReport.result?.verdict === 'Pass'\r\n                      ? 'Great job on passing this exam!'\r\n                      : 'Review the material and try again to improve your score.'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </Modal>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,YAAY,QAAQ,MAAM;AAC7H,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,OAAO,QACF,gBAAgB;AACvB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC;AAAO,CAAC,GAAG7B,MAAM;AACzB,MAAM;EAAE8B;AAAY,CAAC,GAAG7B,UAAU;AAElC,SAAS8B,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC;IACjC0E,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAACiF,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;EACjE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAACiF,MAAM,CAACC,UAAU,IAAI,GAAG,IAAID,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC;EAC9F,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAMyF,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAE9B,MAAM2E,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9BnB,QAAQ,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IAEA,MAAMJ,UAAU,GAAGiB,IAAI,CAACC,MAAM;IAC9B,MAAMjB,WAAW,GAAGgB,IAAI,CAACE,MAAM,CAACC,MAAM;MAAA,IAAAC,cAAA;MAAA,OAAI,EAAAA,cAAA,GAAAD,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,OAAO,MAAK,MAAM;IAAA,EAAC,CAACL,MAAM;IACnF,MAAMM,MAAM,GAAGP,IAAI,CAACQ,GAAG,CAACL,MAAM,IAAI;MAAA,IAAAM,eAAA,EAAAC,qBAAA,EAAAC,YAAA;MAChC,MAAMC,QAAQ,GAAG,EAAAH,eAAA,GAAAN,MAAM,CAACE,MAAM,cAAAI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeI,cAAc,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BT,MAAM,KAAI,CAAC;MAC3D,MAAMa,KAAK,GAAG,EAAAH,YAAA,GAAAR,MAAM,CAACY,IAAI,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,UAAU,KAAI,CAAC;MAC1C,OAAQJ,QAAQ,GAAGE,KAAK,GAAI,GAAG;IACjC,CAAC,CAAC;IAEF,MAAM7B,YAAY,GAAGsB,MAAM,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGpC,UAAU;IAC/E,MAAMI,SAAS,GAAGiC,IAAI,CAACC,GAAG,CAAC,GAAGd,MAAM,CAAC;;IAErC;IACA,IAAIe,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAGxB,IAAI,CAACC,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAAA,IAAAC,cAAA;MACzC,IAAI,EAAAA,cAAA,GAAAzB,IAAI,CAACwB,CAAC,CAAC,CAACnB,MAAM,cAAAoB,cAAA,uBAAdA,cAAA,CAAgBnB,OAAO,MAAK,MAAM,EAAE;QACtCgB,aAAa,EAAE;QACfC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAACE,SAAS,EAAED,aAAa,CAAC;MAChD,CAAC,MAAM;QACLA,aAAa,GAAG,CAAC;MACnB;IACF;IAEAxC,QAAQ,CAAC;MACPC,UAAU;MACVC,WAAW;MACXC,YAAY,EAAEmC,IAAI,CAACM,KAAK,CAACzC,YAAY,CAAC;MACtCC,MAAM,EAAEqC,SAAS;MACjBpC,SAAS,EAAEiC,IAAI,CAACM,KAAK,CAACvC,SAAS;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF7B,QAAQ,CAACxE,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMsG,QAAQ,GAAG,MAAMrG,mBAAmB,CAAC,CAAC;MAC5C,IAAIqG,QAAQ,CAACC,OAAO,EAAE;QACpB3D,cAAc,CAAC0D,QAAQ,CAAC5B,IAAI,CAAC;QAC7B5B,eAAe,CAACwD,QAAQ,CAAC5B,IAAI,CAAC;QAC9BD,cAAc,CAAC6B,QAAQ,CAAC5B,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLxF,OAAO,CAACsH,KAAK,CAACF,QAAQ,CAACpH,OAAO,CAAC;MACjC;MACAsF,QAAQ,CAACzE,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOyG,KAAK,EAAE;MACdhC,QAAQ,CAACzE,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACsH,KAAK,CAACA,KAAK,CAACtH,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMuH,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,QAAQ,GAAG,CAAC,GAAG/D,WAAW,CAAC;IAE/B,IAAII,aAAa,KAAK,KAAK,EAAE;MAC3B2D,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAA8B,aAAA,EAAAC,qBAAA;QAAA,QAAAD,aAAA,GAC/B9B,MAAM,CAACY,IAAI,cAAAkB,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaE,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChE,aAAa,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAAA,CAC3E,CAAC;IACH;IAEA,IAAI7D,aAAa,KAAK,KAAK,EAAE;MAC3ByD,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAAmC,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAAnC,MAAM,CAACE,MAAM,cAAAiC,eAAA,uBAAbA,eAAA,CAAehC,OAAO,MAAK/B,aAAa;MAAA,EAAC;IAChF;IAEA,IAAIE,SAAS,IAAIA,SAAS,CAACwB,MAAM,KAAK,CAAC,EAAE;MACvC+B,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM,IAAI;QACnC,MAAMoC,UAAU,GAAGjG,MAAM,CAAC6D,MAAM,CAACqC,SAAS,CAAC;QAC3C,OAAOD,UAAU,CAACE,SAAS,CAAChE,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACtE,CAAC,CAAC;IACJ;IAEAL,eAAe,CAAC4D,QAAQ,CAAC;IACzBjC,cAAc,CAACiC,QAAQ,CAAC;EAC1B,CAAC;EAED1H,SAAS,CAAC,MAAM;IACdqH,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAENrH,SAAS,CAAC,MAAM;IACdyH,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC1D,aAAa,EAAEE,aAAa,EAAEE,SAAS,EAAER,WAAW,CAAC,CAAC;EAE1D3D,SAAS,CAAC,MAAM;IACd,MAAMoI,YAAY,GAAGA,CAAA,KAAM;MACzBrD,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACpCE,WAAW,CAACH,MAAM,CAACC,UAAU,IAAI,GAAG,IAAID,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC;IACnE,CAAC;IAEDD,MAAM,CAACqD,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMpD,MAAM,CAACsD,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAI1B,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAM2B,cAAc,GAAIxC,OAAO,IAAK;IAClC,OAAOA,OAAO,KAAK,MAAM,gBACvB9D,OAAA,CAACL,OAAO;MAAC4G,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAC9C3G,OAAA,CAACJ,GAAG;MAAC2G,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAGpF,WAAW,CAACuC,GAAG,CAACL,MAAM;MAAA,IAAAmD,aAAA;MAAA,QAAAA,aAAA,GAAInD,MAAM,CAACY,IAAI,cAAAuC,aAAA,uBAAXA,aAAA,CAAanB,OAAO;IAAA,EAAC,CAACjC,MAAM,CAACqD,OAAO,CAAC;IAChF,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMI,iBAAiB,GAAIC,MAAM,IAAK;IACpC7D,iBAAiB,CAAC6D,MAAM,CAAC;IACzB/D,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+D,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMxE,QAAQ,GAAGE,MAAM,CAACC,UAAU,GAAG,GAAG;IACxC,MAAMC,QAAQ,GAAGF,MAAM,CAACC,UAAU,IAAI,GAAG,IAAID,MAAM,CAACC,UAAU,GAAG,IAAI;IAErE,MAAMsE,WAAW,GAAG,CAClB;MACEC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,UAAU;MACrBC,GAAG,EAAE,UAAU;MACfC,MAAM,EAAEA,CAACC,IAAI,EAAER,MAAM;QAAA,IAAAS,YAAA,EAAAC,aAAA;QAAA,oBACnB5H,OAAA;UAAKuG,SAAS,EAAC,SAAS;UAAAsB,QAAA,gBACtB7H,OAAA;YAAKuG,SAAS,EAAC,2DAA2D;YAAAsB,QAAA,EACvE,EAAAF,YAAA,GAAAT,MAAM,CAAC3C,IAAI,cAAAoD,YAAA,uBAAXA,YAAA,CAAaG,IAAI,KAAI;UAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACN3G,OAAA;YAAKuG,SAAS,EAAC,2CAA2C;YAAAsB,QAAA,EACvD,EAAAD,aAAA,GAAAV,MAAM,CAAC3C,IAAI,cAAAqD,aAAA,uBAAXA,aAAA,CAAajC,OAAO,KAAI;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACL/D,QAAQ,iBACP5C,OAAA;YAAKuG,SAAS,EAAC,wCAAwC;YAAAsB,QAAA,gBACrD7H,OAAA;cAAKuG,SAAS,EAAC,uBAAuB;cAAAsB,QAAA,EACnC/H,MAAM,CAACoH,MAAM,CAAClB,SAAS,CAAC,CAAC+B,MAAM,CAAC,cAAc;YAAC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN3G,OAAA,CAAC1B,MAAM;cACL0J,IAAI,EAAC,MAAM;cACXC,IAAI,EAAC,OAAO;cACZC,IAAI,eAAElI,OAAA,CAACN,KAAK;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChBwB,OAAO,EAAEA,CAAA,KAAMlB,iBAAiB,CAACC,MAAM,CAAE;cACzCX,SAAS,EAAC,0BAA0B;cAAAsB,QAAA,EACrC;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,CACP;MACDyB,KAAK,EAAExF,QAAQ,GAAG,GAAG,GAAGI,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC5CqF,QAAQ,EAAE;IACZ,CAAC,EACD;MACEf,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE,OAAO;MAClBC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAEA,CAACC,IAAI,EAAER,MAAM,KAAK;QAAA,IAAAoB,cAAA,EAAAC,qBAAA,EAAAC,aAAA;QACxB,MAAMpE,QAAQ,GAAG,EAAAkE,cAAA,GAAApB,MAAM,CAACrD,MAAM,cAAAyE,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAejE,cAAc,cAAAkE,qBAAA,uBAA7BA,qBAAA,CAA+B9E,MAAM,KAAI,CAAC;QAC3D,MAAMa,KAAK,GAAG,EAAAkE,aAAA,GAAAtB,MAAM,CAAC3C,IAAI,cAAAiE,aAAA,uBAAXA,aAAA,CAAahE,UAAU,KAAI,CAAC;QAC1C,MAAMiE,UAAU,GAAG7D,IAAI,CAACM,KAAK,CAAEd,QAAQ,GAAGE,KAAK,GAAI,GAAG,CAAC;QAEvD,oBACEtE,OAAA;UAAKuG,SAAS,EAAC,aAAa;UAAAsB,QAAA,gBAC1B7H,OAAA;YAAKuG,SAAS,EAAC,8CAA8C;YAAAsB,QAAA,GAC1DzD,QAAQ,EAAC,GAAC,EAACE,KAAK;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACN3G,OAAA,CAAC9B,QAAQ;YACPwK,OAAO,EAAED,UAAW;YACpBR,IAAI,EAAC,OAAO;YACZU,WAAW,EAAEF,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,SAAU;YACtDG,QAAQ,EAAE,KAAM;YAChBrC,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF3G,OAAA;YAAKuG,SAAS,EAAG,kCAAiCF,aAAa,CAACoC,UAAU,CAAE,EAAE;YAAAZ,QAAA,GAC3EY,UAAU,EAAC,GACd;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,CAAC;MACDyB,KAAK,EAAExF,QAAQ,GAAG,EAAE,GAAG,GAAG;MAC1BiG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;QAAA,IAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA;QAChB,MAAMC,MAAM,GAAG1E,IAAI,CAACM,KAAK,CAAE,CAAC,EAAA8D,SAAA,GAAAF,CAAC,CAACjF,MAAM,cAAAmF,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAU3E,cAAc,cAAA4E,qBAAA,uBAAxBA,qBAAA,CAA0BxF,MAAM,KAAI,CAAC,KAAK,EAAAyF,OAAA,GAAAJ,CAAC,CAACvE,IAAI,cAAA2E,OAAA,uBAANA,OAAA,CAAQ1E,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;QACtG,MAAM+E,MAAM,GAAG3E,IAAI,CAACM,KAAK,CAAE,CAAC,EAAAiE,SAAA,GAAAJ,CAAC,CAAClF,MAAM,cAAAsF,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAU9E,cAAc,cAAA+E,qBAAA,uBAAxBA,qBAAA,CAA0B3F,MAAM,KAAI,CAAC,KAAK,EAAA4F,OAAA,GAAAN,CAAC,CAACxE,IAAI,cAAA8E,OAAA,uBAANA,OAAA,CAAQ7E,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;QACtG,OAAO8E,MAAM,GAAGC,MAAM;MACxB;IACF,CAAC,EACD;MACEjC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,SAAS;MACpBC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAEA,CAACC,IAAI,EAAER,MAAM,KAAK;QAAA,IAAAsC,eAAA;QACxB,MAAM1F,OAAO,IAAA0F,eAAA,GAAGtC,MAAM,CAACrD,MAAM,cAAA2F,eAAA,uBAAbA,eAAA,CAAe1F,OAAO;QACtC,MAAM2F,QAAQ,GAAG3F,OAAO,KAAK,MAAM;QAEnC,oBACE9D,OAAA,CAACvB,GAAG;UACFyJ,IAAI,EAAE,CAACtF,QAAQ,GAAG0D,cAAc,CAACxC,OAAO,CAAC,GAAG,IAAK;UACjD4F,KAAK,EAAED,QAAQ,GAAG,SAAS,GAAG,OAAQ;UACtClD,SAAS,EAAC,gCAAgC;UAAAsB,QAAA,EAEzCjF,QAAQ,GAAI6G,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAK3F,OAAO,IAAI;QAAM;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAEV,CAAC;MACDyB,KAAK,EAAExF,QAAQ,GAAG,EAAE,GAAG,GAAG;MAC1B+G,OAAO,EAAE,CAAC/G,QAAQ,GAAG,CACnB;QAAE8E,IAAI,EAAE,MAAM;QAAEkC,KAAK,EAAE;MAAO,CAAC,EAC/B;QAAElC,IAAI,EAAE,MAAM;QAAEkC,KAAK,EAAE;MAAO,CAAC,CAChC,GAAGC,SAAS;MACbC,QAAQ,EAAE,CAAClH,QAAQ,GAAG,CAACgH,KAAK,EAAE1C,MAAM;QAAA,IAAA6C,eAAA;QAAA,OAAK,EAAAA,eAAA,GAAA7C,MAAM,CAACrD,MAAM,cAAAkG,eAAA,uBAAbA,eAAA,CAAejG,OAAO,MAAK8F,KAAK;MAAA,IAAGC;IAC9E,CAAC,CACF;;IAED;IACA,IAAI,CAACjH,QAAQ,EAAE;MACbyE,WAAW,CAAC2C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACvB1C,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,WAAW;QACtBC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAGwC,IAAI,iBACXjK,OAAA;UAAKuG,SAAS,EAAC,SAAS;UAAAsB,QAAA,gBACtB7H,OAAA;YAAKuG,SAAS,EAAC,aAAa;YAAAsB,QAAA,EAAE/H,MAAM,CAACmK,IAAI,CAAC,CAAClC,MAAM,CAAC,cAAc;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxE3G,OAAA;YAAKuG,SAAS,EAAC,eAAe;YAAAsB,QAAA,EAAE/H,MAAM,CAACmK,IAAI,CAAC,CAAClC,MAAM,CAAC,OAAO;UAAC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;QACDyB,KAAK,EAAEpF,QAAQ,GAAG,GAAG,GAAG;MAC1B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACJ,QAAQ,EAAE;MACbyE,WAAW,CAAC6C,IAAI,CAAC;QACf5C,KAAK,EAAE,SAAS;QAChBE,GAAG,EAAE,SAAS;QACdC,MAAM,EAAEA,CAACC,IAAI,EAAER,MAAM,kBACnBlH,OAAA,CAAC1B,MAAM;UACL0J,IAAI,EAAC,SAAS;UACdC,IAAI,EAAC,OAAO;UACZC,IAAI,eAAElI,OAAA,CAACN,KAAK;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChBwB,OAAO,EAAEA,CAAA,KAAMlB,iBAAiB,CAACC,MAAM,CAAE;UACzCX,SAAS,EAAC,+BAA+B;UAAAsB,QAAA,EAExC7E,QAAQ,GAAG,EAAE,GAAG;QAAM;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACT;QACDyB,KAAK,EAAEpF,QAAQ,GAAG,EAAE,GAAG;MACzB,CAAC,CAAC;IACJ;IAEA,OAAOqE,WAAW;EACpB,CAAC;EAED,MAAM8C,OAAO,GAAG/C,oBAAoB,CAAC,CAAC;EAEtC,oBACEpH,OAAA;IAAKuG,SAAS,EAAC,oEAAoE;IAAAsB,QAAA,gBACjF7H,OAAA,CAACjC,SAAS;MAACuJ,KAAK,EAAC;IAAqB;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzC3G,OAAA;MAAKuG,SAAS,EAAC,qEAAqE;MAAAsB,QAAA,gBAElF7H,OAAA,CAAChB,MAAM,CAACoL,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BhE,SAAS,EAAC,oCAAoC;QAAAsB,QAAA,gBAE9C7H,OAAA;UAAKuG,SAAS,EAAC,iLAAiL;UAAAsB,QAAA,eAC9L7H,OAAA,CAACT,UAAU;YAACgH,SAAS,EAAC;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACN3G,OAAA;UAAIuG,SAAS,EAAC,4EAA4E;UAAAsB,QAAA,GAAC,OACpF,eAAA7H,OAAA;YAAMuG,SAAS,EAAC,4EAA4E;YAAAsB,QAAA,EAAC;UAAW;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,YACtH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3G,OAAA;UAAGuG,SAAS,EAAC,kFAAkF;UAAAsB,QAAA,EAAC;QAEhG;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb3G,OAAA,CAAChB,MAAM,CAACoL,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BnE,SAAS,EAAC,qFAAqF;QAAAsB,QAAA,gBAE/F7H,OAAA,CAAC/B,IAAI;UAACsI,SAAS,EAAC,mKAAmK;UAAAsB,QAAA,gBACjL7H,OAAA;YAAKuG,SAAS,EAAC;UAAmE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzF3G,OAAA;YAAKuG,SAAS,EAAC,uDAAuD;YAAAsB,QAAA,gBACpE7H,OAAA;cAAKuG,SAAS,EAAC,yJAAyJ;cAAAsB,QAAA,eACtK7H,OAAA,CAACd,QAAQ;gBAACqH,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN3G,OAAA,CAAC7B,SAAS;cACRmJ,KAAK,EAAC,aAAa;cACnBsC,KAAK,EAAEvH,KAAK,CAACE,UAAW;cACxBoI,UAAU,EAAE;gBACVjB,KAAK,EAAE,SAAS;gBAChBkB,UAAU,EAAE,MAAM;gBAClBC,QAAQ,EAAE/H,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM;gBACvF+H,SAAS,EAAE;cACb,CAAE;cACFvE,SAAS,EAAC,kCAAkC;cAC5CwE,KAAK,EAAE;gBAAED,SAAS,EAAE;cAAS;YAAE;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP3G,OAAA,CAAC/B,IAAI;UAACsI,SAAS,EAAC,sKAAsK;UAAAsB,QAAA,gBACpL7H,OAAA;YAAKuG,SAAS,EAAC;UAAqE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3F3G,OAAA;YAAKuG,SAAS,EAAC,uDAAuD;YAAAsB,QAAA,gBACpE7H,OAAA;cAAKuG,SAAS,EAAC,2JAA2J;cAAAsB,QAAA,eACxK7H,OAAA,CAACL,OAAO;gBAAC4G,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN3G,OAAA,CAAC7B,SAAS;cACRmJ,KAAK,EAAC,QAAQ;cACdsC,KAAK,EAAEvH,KAAK,CAACG,WAAY;cACzBmI,UAAU,EAAE;gBACVjB,KAAK,EAAE,SAAS;gBAChBkB,UAAU,EAAE,MAAM;gBAClBC,QAAQ,EAAE/H,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG;cACnF,CAAE;cACFwD,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP3G,OAAA,CAAC/B,IAAI;UAACsI,SAAS,EAAC,yKAAyK;UAAAsB,QAAA,gBACvL7H,OAAA;YAAKuG,SAAS,EAAC;UAAuE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7F3G,OAAA;YAAKuG,SAAS,EAAC,uDAAuD;YAAAsB,QAAA,gBACpE7H,OAAA;cAAKuG,SAAS,EAAC,6JAA6J;cAAAsB,QAAA,eAC1K7H,OAAA,CAACb,YAAY;gBAACoH,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACN3G,OAAA,CAAC7B,SAAS;cACRmJ,KAAK,EAAC,eAAe;cACrBsC,KAAK,EAAEvH,KAAK,CAACI,YAAa;cAC1BuI,MAAM,EAAC,GAAG;cACVL,UAAU,EAAE;gBACVjB,KAAK,EAAE,SAAS;gBAChBkB,UAAU,EAAE,MAAM;gBAClBC,QAAQ,EAAE/H,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG;cACnF,CAAE;cACFwD,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP3G,OAAA,CAAC/B,IAAI;UAACsI,SAAS,EAAC,yKAAyK;UAAAsB,QAAA,gBACvL7H,OAAA;YAAKuG,SAAS,EAAC;UAAuE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7F3G,OAAA;YAAKuG,SAAS,EAAC,uDAAuD;YAAAsB,QAAA,gBACpE7H,OAAA;cAAKuG,SAAS,EAAC,6JAA6J;cAAAsB,QAAA,eAC1K7H,OAAA,CAACf,QAAQ;gBAACsH,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN3G,OAAA,CAAC7B,SAAS;cACRmJ,KAAK,EAAC,YAAY;cAClBsC,KAAK,EAAEvH,KAAK,CAACM,SAAU;cACvBqI,MAAM,EAAC,GAAG;cACVL,UAAU,EAAE;gBACVjB,KAAK,EAAE,SAAS;gBAChBkB,UAAU,EAAE,MAAM;gBAClBC,QAAQ,EAAE/H,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG;cACnF,CAAE;cACFwD,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP3G,OAAA,CAAC/B,IAAI;UAACsI,SAAS,EAAC,+LAA+L;UAAAsB,QAAA,gBAC7M7H,OAAA;YAAKuG,SAAS,EAAC;UAAmE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzF3G,OAAA;YAAKuG,SAAS,EAAC,uDAAuD;YAAAsB,QAAA,gBACpE7H,OAAA;cAAKuG,SAAS,EAAC,yJAAyJ;cAAAsB,QAAA,eACtK7H,OAAA,CAACH,OAAO;gBAAC0G,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN3G,OAAA,CAAC7B,SAAS;cACRmJ,KAAK,EAAC,aAAa;cACnBsC,KAAK,EAAEvH,KAAK,CAACK,MAAO;cACpBiI,UAAU,EAAE;gBACVjB,KAAK,EAAE,SAAS;gBAChBkB,UAAU,EAAE,MAAM;gBAClBC,QAAQ,EAAE/H,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG;cACnF,CAAE;cACFwD,SAAS,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGG,CAAC,eAGb3G,OAAA,CAAChB,MAAM,CAACoL,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BnE,SAAS,EAAC,6FAA6F;QAAAsB,QAAA,eAEvG7H,OAAA;UAAKuG,SAAS,EAAC,qBAAqB;UAAAsB,QAAA,gBAClC7H,OAAA;YAAKuG,SAAS,EAAC,yBAAyB;YAAAsB,QAAA,gBACtC7H,OAAA,CAACP,QAAQ;cAAC8G,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C3G,OAAA;cAAIuG,SAAS,EAAC,qCAAqC;cAAAsB,QAAA,EAAC;YAAc;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAGN3G,OAAA;YAAKuG,SAAS,EAAC,sDAAsD;YAAAsB,QAAA,gBAEnE7H,OAAA;cAAKuG,SAAS,EAAC,WAAW;cAAAsB,QAAA,gBACxB7H,OAAA;gBAAOuG,SAAS,EAAC,mCAAmC;gBAAAsB,QAAA,EAAC;cAAO;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpE3G,OAAA,CAAC5B,MAAM;gBACL6M,WAAW,EAAC,cAAc;gBAC1BrB,KAAK,EAAE/H,aAAc;gBACrBqJ,QAAQ,EAAEpJ,gBAAiB;gBAC3ByE,SAAS,EAAC,QAAQ;gBAClB0B,IAAI,EAAC,OAAO;gBAAAJ,QAAA,gBAEZ7H,OAAA,CAACC,MAAM;kBAAC2J,KAAK,EAAC,KAAK;kBAAA/B,QAAA,EAAC;gBAAY;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCC,iBAAiB,CAAC,CAAC,CAAC5C,GAAG,CAAC2B,OAAO,iBAC9B3F,OAAA,CAACC,MAAM;kBAAe2J,KAAK,EAAEjE,OAAQ;kBAAAkC,QAAA,EAAElC;gBAAO,GAAjCA,OAAO;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN3G,OAAA;cAAKuG,SAAS,EAAC,WAAW;cAAAsB,QAAA,gBACxB7H,OAAA;gBAAOuG,SAAS,EAAC,mCAAmC;gBAAAsB,QAAA,EAAC;cAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnE3G,OAAA,CAAC5B,MAAM;gBACL6M,WAAW,EAAC,aAAa;gBACzBrB,KAAK,EAAE7H,aAAc;gBACrBmJ,QAAQ,EAAElJ,gBAAiB;gBAC3BuE,SAAS,EAAC,QAAQ;gBAClB0B,IAAI,EAAC,OAAO;gBAAAJ,QAAA,gBAEZ7H,OAAA,CAACC,MAAM;kBAAC2J,KAAK,EAAC,KAAK;kBAAA/B,QAAA,EAAC;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC3G,OAAA,CAACC,MAAM;kBAAC2J,KAAK,EAAC,MAAM;kBAAA/B,QAAA,EAAC;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3G,OAAA,CAACC,MAAM;kBAAC2J,KAAK,EAAC,MAAM;kBAAA/B,QAAA,EAAC;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN3G,OAAA;cAAKuG,SAAS,EAAC,uCAAuC;cAAAsB,QAAA,gBACpD7H,OAAA;gBAAOuG,SAAS,EAAC,mCAAmC;gBAAAsB,QAAA,EAAC;cAAU;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvE3G,OAAA,CAACE,WAAW;gBACV0J,KAAK,EAAE3H,SAAU;gBACjBiJ,QAAQ,EAAEhJ,YAAa;gBACvBqE,SAAS,EAAC,QAAQ;gBAClB0B,IAAI,EAAC,OAAO;gBACZgD,WAAW,EAAE,CAAC,MAAM,EAAE,IAAI,CAAE;gBAC5BlD,MAAM,EAAC,YAAY;gBACnBoD,UAAU;cAAA;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN3G,OAAA;cAAKuG,SAAS,EAAC,WAAW;cAAAsB,QAAA,gBACxB7H,OAAA;gBAAOuG,SAAS,EAAC,6CAA6C;gBAAAsB,QAAA,EAAC;cAAO;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E3G,OAAA,CAAC1B,MAAM;gBACL6J,OAAO,EAAEA,CAAA,KAAM;kBACbrG,gBAAgB,CAAC,KAAK,CAAC;kBACvBE,gBAAgB,CAAC,KAAK,CAAC;kBACvBE,YAAY,CAAC,IAAI,CAAC;gBACpB,CAAE;gBACF+F,IAAI,EAAC,OAAO;gBACZ1B,SAAS,EAAC,QAAQ;gBAClB2B,IAAI,eAAElI,OAAA,CAACJ,GAAG;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAkB,QAAA,EACf;cAED;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAAC9E,aAAa,KAAK,KAAK,IAAIE,aAAa,KAAK,KAAK,IAAIE,SAAS,kBAC/DjC,OAAA;YAAKuG,SAAS,EAAC,oDAAoD;YAAAsB,QAAA,gBACjE7H,OAAA;cAAMuG,SAAS,EAAC,mCAAmC;cAAAsB,QAAA,EAAC;YAAe;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACzE9E,aAAa,KAAK,KAAK,iBACtB7B,OAAA,CAACvB,GAAG;cACF2M,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAMvJ,gBAAgB,CAAC,KAAK,CAAE;cACvCyE,SAAS,EAAC,0CAA0C;cAAAsB,QAAA,EAEnDhG;YAAa;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACN,EACA5E,aAAa,KAAK,KAAK,iBACtB/B,OAAA,CAACvB,GAAG;cACF2M,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAMrJ,gBAAgB,CAAC,KAAK,CAAE;cACvCuE,SAAS,EAAExE,aAAa,KAAK,MAAM,GAAG,6CAA6C,GAAG,uCAAwC;cAAA8F,QAAA,EAE7H9F;YAAa;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACN,EACA1E,SAAS,iBACRjC,OAAA,CAACvB,GAAG;cACF2M,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAMnJ,YAAY,CAAC,IAAI,CAAE;cAClCqE,SAAS,EAAC,gDAAgD;cAAAsB,QAAA,GAEzD5F,SAAS,CAAC,CAAC,CAAC,CAAC8F,MAAM,CAAC,UAAU,CAAC,EAAC,KAAG,EAAC9F,SAAS,CAAC,CAAC,CAAC,CAAC8F,MAAM,CAAC,UAAU,CAAC;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3G,OAAA,CAAChB,MAAM,CAACoL,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BnE,SAAS,EAAC,qFAAqF;QAAAsB,QAAA,eAE/F7H,OAAA,CAACxB,KAAK;UACJ2L,OAAO,EAAEA,OAAQ;UACjBmB,UAAU,EAAE3J,YAAa;UACzB4J,MAAM,EAAGrE,MAAM,IAAKA,MAAM,CAACsE,GAAI;UAC/BC,UAAU,EAAE;YACVC,QAAQ,EAAE5I,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE;YAC1C4I,eAAe,EAAE7I,MAAM,CAACC,UAAU,IAAI,GAAG;YACzC6I,eAAe,EAAE9I,MAAM,CAACC,UAAU,IAAI,GAAG;YACzC8I,SAAS,EAAEA,CAACvH,KAAK,EAAEwH,KAAK,KACtBhJ,MAAM,CAACC,UAAU,IAAI,GAAG,GACnB,GAAE+I,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAMxH,KAAM,UAAS,GAC5C,GAAEwH,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,MAAKxH,KAAM,EAAC;YAC1CiC,SAAS,EAAE,2BAA2B;YACtCwF,MAAM,EAAEjJ,MAAM,CAACC,UAAU,GAAG;UAC9B,CAAE;UACFiJ,MAAM,EAAE;YAAEC,CAAC,EAAEnJ,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG;UAAI,CAAE;UACnDwD,SAAS,EAAC,cAAc;UACxB0B,IAAI,EAAEnF,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,OAAQ;UACnDmJ,MAAM,EAAE;YACNC,SAAS,eACPnM,OAAA,CAACzB,KAAK;cACJ6N,KAAK,EAAE7N,KAAK,CAAC8N,sBAAuB;cACpCC,WAAW,eACTtM,OAAA;gBAAKuG,SAAS,EAAC,MAAM;gBAAAsB,QAAA,gBACnB7H,OAAA;kBAAIuG,SAAS,EAAC,qDAAqD;kBAAAsB,QAAA,EAAC;gBAAqB;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9F3G,OAAA;kBAAGuG,SAAS,EAAC,yCAAyC;kBAAAsB,QAAA,EAAC;gBAAuE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAEL;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb3G,OAAA,CAACtB,KAAK;QACJ4I,KAAK,eACHtH,OAAA;UAAKuG,SAAS,EAAC,yBAAyB;UAAAsB,QAAA,gBACtC7H,OAAA;YAAKuG,SAAS,EAAC,mEAAmE;YAAAsB,QAAA,eAChF7H,OAAA,CAACN,KAAK;cAAC6G,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN3G,OAAA;YAAMuG,SAAS,EAAC,uBAAuB;YAAAsB,QAAA,EAAC;UAAY;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACN;QACD4F,IAAI,EAAErJ,cAAe;QACrBsJ,QAAQ,EAAErF,gBAAiB;QAC3BsF,MAAM,EAAE,cACNzM,OAAA,CAAC1B,MAAM;UAAa6J,OAAO,EAAEhB,gBAAiB;UAACc,IAAI,EAAC,OAAO;UAAAJ,QAAA,EAAC;QAE5D,GAFY,OAAO;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEX,CAAC,CACT;QACFyB,KAAK,EAAExF,QAAQ,GAAG,KAAK,GAAGI,QAAQ,GAAG,GAAG,GAAG,GAAI;QAC/CuD,SAAS,EAAC,oBAAoB;QAAAsB,QAAA,EAE7BzE,cAAc,iBACbpD,OAAA;UAAKuG,SAAS,EAAC,WAAW;UAAAsB,QAAA,gBAExB7H,OAAA;YAAKuG,SAAS,EAAC,2BAA2B;YAAAsB,QAAA,gBACxC7H,OAAA;cAAIuG,SAAS,EAAC,0CAA0C;cAAAsB,QAAA,EAAC;YAAgB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E3G,OAAA,CAACrB,YAAY;cAAC+N,MAAM,EAAE9J,QAAQ,GAAG,CAAC,GAAG,CAAE;cAACqF,IAAI,EAAC,OAAO;cAAAJ,QAAA,gBAClD7H,OAAA,CAACrB,YAAY,CAACgO,IAAI;gBAACC,KAAK,EAAC,WAAW;gBAACC,IAAI,EAAEjK,QAAQ,GAAG,CAAC,GAAG,CAAE;gBAAAiF,QAAA,eAC1D7H,OAAA;kBAAMuG,SAAS,EAAC,aAAa;kBAAAsB,QAAA,EAAE,EAAAxH,oBAAA,GAAA+C,cAAc,CAACmB,IAAI,cAAAlE,oBAAA,uBAAnBA,oBAAA,CAAqByH,IAAI,KAAI;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACpB3G,OAAA,CAACrB,YAAY,CAACgO,IAAI;gBAACC,KAAK,EAAC,SAAS;gBAAA/E,QAAA,EAC/B,EAAAvH,qBAAA,GAAA8C,cAAc,CAACmB,IAAI,cAAAjE,qBAAA,uBAAnBA,qBAAA,CAAqBqF,OAAO,KAAI;cAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACpB3G,OAAA,CAACrB,YAAY,CAACgO,IAAI;gBAACC,KAAK,EAAC,YAAY;gBAAA/E,QAAA,EAClC/H,MAAM,CAACsD,cAAc,CAAC4C,SAAS,CAAC,CAAC+B,MAAM,CAAC,0BAA0B;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACpB3G,OAAA,CAACrB,YAAY,CAACgO,IAAI;gBAACC,KAAK,EAAC,iBAAiB;gBAAA/E,QAAA,EACvC,EAAAtH,qBAAA,GAAA6C,cAAc,CAACmB,IAAI,cAAAhE,qBAAA,uBAAnBA,qBAAA,CAAqBiE,UAAU,KAAI;cAAC;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACpB3G,OAAA,CAACrB,YAAY,CAACgO,IAAI;gBAACC,KAAK,EAAC,eAAe;gBAAA/E,QAAA,EACrC,EAAArH,qBAAA,GAAA4C,cAAc,CAACmB,IAAI,cAAA/D,qBAAA,uBAAnBA,qBAAA,CAAqBsM,YAAY,KAAI;cAAC;gBAAAtG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAGN3G,OAAA;YAAKuG,SAAS,EAAC,2BAA2B;YAAAsB,QAAA,gBACxC7H,OAAA;cAAIuG,SAAS,EAAC,0CAA0C;cAAAsB,QAAA,EAAC;YAAmB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF3G,OAAA;cAAKuG,SAAS,EAAC,uCAAuC;cAAAsB,QAAA,gBACpD7H,OAAA;gBAAKuG,SAAS,EAAC,qCAAqC;gBAAAsB,QAAA,gBAClD7H,OAAA;kBAAKuG,SAAS,EAAC,kCAAkC;kBAAAsB,QAAA,EAC9C,EAAApH,qBAAA,GAAA2C,cAAc,CAACS,MAAM,cAAApD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuB4D,cAAc,cAAA3D,sBAAA,uBAArCA,sBAAA,CAAuC+C,MAAM,KAAI;gBAAC;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN3G,OAAA;kBAAKuG,SAAS,EAAC,uBAAuB;kBAAAsB,QAAA,EAAC;gBAAe;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACN3G,OAAA;gBAAKuG,SAAS,EAAC,qCAAqC;gBAAAsB,QAAA,gBAClD7H,OAAA;kBAAKuG,SAAS,EAAC,kCAAkC;kBAAAsB,QAAA,EAC9C,CAAC,EAAAlH,qBAAA,GAAAyC,cAAc,CAACmB,IAAI,cAAA5D,qBAAA,uBAAnBA,qBAAA,CAAqB6D,UAAU,KAAI,CAAC,KAAK,EAAA5D,sBAAA,GAAAwC,cAAc,CAACS,MAAM,cAAAjD,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuByD,cAAc,cAAAxD,sBAAA,uBAArCA,sBAAA,CAAuC4C,MAAM,KAAI,CAAC;gBAAC;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACN3G,OAAA;kBAAKuG,SAAS,EAAC,uBAAuB;kBAAAsB,QAAA,EAAC;gBAAa;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN3G,OAAA;gBAAKuG,SAAS,EAAC,qCAAqC;gBAAAsB,QAAA,gBAClD7H,OAAA;kBAAKuG,SAAS,EAAG,sBACf3B,IAAI,CAACM,KAAK,CAAE,CAAC,EAAApE,sBAAA,GAAAsC,cAAc,CAACS,MAAM,cAAA/C,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBuD,cAAc,cAAAtD,sBAAA,uBAArCA,sBAAA,CAAuC0C,MAAM,KAAI,CAAC,KAAK,EAAAzC,qBAAA,GAAAoC,cAAc,CAACmB,IAAI,cAAAvD,qBAAA,uBAAnBA,qBAAA,CAAqBwD,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC,IAAI,EAAE,GACnH,gBAAgB,GAChB,cACL,EAAE;kBAAAqD,QAAA,GACAjD,IAAI,CAACM,KAAK,CAAE,CAAC,EAAAjE,sBAAA,GAAAmC,cAAc,CAACS,MAAM,cAAA5C,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBoD,cAAc,cAAAnD,sBAAA,uBAArCA,sBAAA,CAAuCuC,MAAM,KAAI,CAAC,KAAK,EAAAtC,qBAAA,GAAAiC,cAAc,CAACmB,IAAI,cAAApD,qBAAA,uBAAnBA,qBAAA,CAAqBqD,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC,EAAC,GACrH;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN3G,OAAA;kBAAKuG,SAAS,EAAC,uBAAuB;kBAAAsB,QAAA,EAAC;gBAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKuG,SAAS,EAAG,kBACf,EAAAnF,sBAAA,GAAAgC,cAAc,CAACS,MAAM,cAAAzC,sBAAA,uBAArBA,sBAAA,CAAuB0C,OAAO,MAAK,MAAM,GACrC,qCAAqC,GACrC,iCACL,EAAE;YAAA+D,QAAA,gBACD7H,OAAA;cAAKuG,SAAS,EAAC,wCAAwC;cAAAsB,QAAA,GACpDvB,cAAc,EAAAjF,uBAAA,GAAC+B,cAAc,CAACS,MAAM,cAAAxC,uBAAA,uBAArBA,uBAAA,CAAuByC,OAAO,CAAC,eAC/C9D,OAAA;gBAAMuG,SAAS,EAAG,yBAChB,EAAAjF,uBAAA,GAAA8B,cAAc,CAACS,MAAM,cAAAvC,uBAAA,uBAArBA,uBAAA,CAAuBwC,OAAO,MAAK,MAAM,GAAG,gBAAgB,GAAG,cAChE,EAAE;gBAAA+D,QAAA,EACA,EAAAtG,uBAAA,GAAA6B,cAAc,CAACS,MAAM,cAAAtC,uBAAA,uBAArBA,uBAAA,CAAuBuC,OAAO,MAAK,MAAM,GAAG,6BAA6B,GAAG;cAAgC;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3G,OAAA;cAAKuG,SAAS,EAAC,kBAAkB;cAAAsB,QAAA,eAC/B7H,OAAA;gBAAMuG,SAAS,EAAC,uBAAuB;gBAAAsB,QAAA,EACpC,EAAArG,uBAAA,GAAA4B,cAAc,CAACS,MAAM,cAAArC,uBAAA,uBAArBA,uBAAA,CAAuBsC,OAAO,MAAK,MAAM,GACtC,iCAAiC,GACjC;cAA0D;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvG,EAAA,CAzqBQD,WAAW;EAAA,QAkBDvB,WAAW;AAAA;AAAAmO,EAAA,GAlBrB5M,WAAW;AA2qBpB,eAAeA,WAAW;AAAC,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}