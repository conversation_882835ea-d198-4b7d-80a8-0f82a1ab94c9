{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\BrainwaveHeader.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport ProfilePicture from './ProfilePicture';\nimport ModernSidebar from '../ModernSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BrainwaveHeader = ({\n  title = \"Brainwave\"\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 w-1/4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSidebarOpen(!sidebarOpen),\n              className: \"group relative\",\n              style: {\n                padding: window.innerWidth <= 480 ? '8px' : window.innerWidth <= 768 ? '10px' : '12px',\n                width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'rgba(0, 0, 0, 0.9)',\n                border: '3px solid rgba(255, 255, 255, 0.8)',\n                borderRadius: '16px',\n                cursor: 'pointer',\n                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'scale(1.05)';\n                e.currentTarget.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.3), 0 3px 12px rgba(0, 0, 0, 0.15)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'scale(1)';\n                e.currentTarget.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)';\n              },\n              title: \"Open Menu\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'space-between',\n                  width: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '20px' : '22px',\n                  height: window.innerWidth <= 480 ? '14px' : window.innerWidth <= 768 ? '16px' : '18px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                    background: '#ffffff',\n                    borderRadius: '3px',\n                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                    background: '#ffffff',\n                    borderRadius: '3px',\n                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                    background: '#ffffff',\n                    borderRadius: '3px',\n                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center space-x-2 sm:space-x-3\",\n              style: {\n                maxWidth: '800px',\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none flex items-center\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rounded-full overflow-hidden border-2 border-white/20 relative inline-block\",\n                    style: {\n                      background: '#f0f0f0',\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                      width: window.innerWidth <= 480 ? '24px' : '32px',\n                      height: window.innerWidth <= 480 ? '24px' : '32px',\n                      marginLeft: '8px',\n                      verticalAlign: 'middle'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: \"/favicon.png\",\n                      alt: \"Brainwave Logo\",\n                      className: \"w-full h-full object-cover\",\n                      style: {\n                        objectFit: 'cover'\n                      },\n                      onError: e => {\n                        e.target.style.display = 'none';\n                        e.target.nextSibling.style.display = 'flex';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                      style: {\n                        display: 'none',\n                        fontSize: '12px'\n                      },\n                      children: \"\\uD83E\\uDDE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 w-1/4\",\n            children: user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(BrainwaveHeader, \"VVnGvKdb5Y3bI/bQiSJK7J8EhV4=\", false, function () {\n  return [useSelector];\n});\n_c = BrainwaveHeader;\nexport default BrainwaveHeader;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveHeader\");", "map": {"version": 3, "names": ["React", "useState", "useSelector", "motion", "ProfilePicture", "ModernSidebar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BrainwaveHeader", "title", "_s", "user", "state", "sidebarOpen", "setSidebarOpen", "children", "className", "onClick", "style", "padding", "window", "innerWidth", "width", "height", "display", "alignItems", "justifyContent", "background", "border", "borderRadius", "cursor", "boxShadow", "<PERSON><PERSON>ilter", "transition", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "flexDirection", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "scale", "animate", "duration", "delay", "max<PERSON><PERSON><PERSON>", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "y", "marginLeft", "verticalAlign", "src", "alt", "objectFit", "onError", "target", "nextS<PERSON>ling", "fontSize", "size", "showOnlineStatus", "name", "level", "class", "isOpen", "setIsOpen", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/BrainwaveHeader.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport ProfilePicture from './ProfilePicture';\nimport ModernSidebar from '../ModernSidebar';\n\nconst BrainwaveHeader = ({ title = \"Brainwave\" }) => {\n  const { user } = useSelector((state) => state.user);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-30\">\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Left section - Menu <PERSON>ton */}\n            <div className=\"flex items-center space-x-2 w-1/4\">\n              <button\n                onClick={() => setSidebarOpen(!sidebarOpen)}\n                className=\"group relative\"\n                style={{\n                  padding: window.innerWidth <= 480 ? '8px' : window.innerWidth <= 768 ? '10px' : '12px',\n                  width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                  height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  background: 'rgba(0, 0, 0, 0.9)',\n                  border: '3px solid rgba(255, 255, 255, 0.8)',\n                  borderRadius: '16px',\n                  cursor: 'pointer',\n                  boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  transition: 'all 0.3s ease',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'scale(1.05)';\n                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.3), 0 3px 12px rgba(0, 0, 0, 0.15)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'scale(1)';\n                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)';\n                }}\n                title=\"Open Menu\"\n              >\n                {/* Modern Menu Icon */}\n                <div \n                  style={{\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'space-between',\n                    width: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '20px' : '22px',\n                    height: window.innerWidth <= 480 ? '14px' : window.innerWidth <= 768 ? '16px' : '18px'\n                  }}\n                >\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                      background: '#ffffff',\n                      borderRadius: '3px',\n                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                      background: '#ffffff',\n                      borderRadius: '3px',\n                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '4px' : '5px',\n                      background: '#ffffff',\n                      borderRadius: '3px',\n                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.4)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                </div>\n              </button>\n            </div>\n\n            {/* Center Section - Brainwave Title + Logo */}\n            <div className=\"flex-1 flex justify-center\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center space-x-2 sm:space-x-3\"\n                style={{\n                  maxWidth: '800px',\n                  width: '100%'\n                }}\n              >\n                {/* Amazing Animated Brainwave Text with Logo */}\n                <div className=\"relative brainwave-container flex items-center\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none flex items-center\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Official Logo - Horizontally next to \"wave\" */}\n                    <div\n                      className=\"rounded-full overflow-hidden border-2 border-white/20 relative inline-block\"\n                      style={{\n                        background: '#f0f0f0',\n                        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                        width: window.innerWidth <= 480 ? '24px' : '32px',\n                        height: window.innerWidth <= 480 ? '24px' : '32px',\n                        marginLeft: '8px',\n                        verticalAlign: 'middle'\n                      }}\n                    >\n                      <img\n                        src=\"/favicon.png\"\n                        alt=\"Brainwave Logo\"\n                        className=\"w-full h-full object-cover\"\n                        style={{ objectFit: 'cover' }}\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'flex';\n                        }}\n                      />\n                      <div\n                        className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\n                        style={{\n                          display: 'none',\n                          fontSize: '12px'\n                        }}\n                      >\n                        🧠\n                      </div>\n                    </div>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n\n            {/* Right Section - User Profile */}\n            <div className=\"flex items-center justify-end space-x-2 w-1/4\">\n              {user && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                  className=\"flex items-center space-x-2 group\"\n                >\n                  {/* Profile Picture with Online Status */}\n                  <ProfilePicture\n                    user={user}\n                    size=\"sm\"\n                    showOnlineStatus={true}\n                    style={{\n                      width: '32px',\n                      height: '32px'\n                    }}\n                  />\n\n                  {/* User Name and Class */}\n                  <div className=\"hidden sm:block text-right\">\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\n                      {user?.name || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modern Sidebar */}\n      <ModernSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n    </>\n  );\n};\n\nexport default BrainwaveHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,KAAK,GAAG;AAAY,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM;IAAEC;EAAK,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEM,OAAA,CAAAE,SAAA;IAAAQ,QAAA,gBAEEV,OAAA;MAAKW,SAAS,EAAC,+DAA+D;MAAAD,QAAA,eAC5EV,OAAA;QAAKW,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpDV,OAAA;UAAKW,SAAS,EAAC,gEAAgE;UAAAD,QAAA,gBAE7EV,OAAA;YAAKW,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAChDV,OAAA;cACEY,OAAO,EAAEA,CAAA,KAAMH,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CG,SAAS,EAAC,gBAAgB;cAC1BE,KAAK,EAAE;gBACLC,OAAO,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACtFC,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACrFE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACtFG,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,UAAU,EAAE,oBAAoB;gBAChCC,MAAM,EAAE,oCAAoC;gBAC5CC,YAAY,EAAE,MAAM;gBACpBC,MAAM,EAAE,SAAS;gBACjBC,SAAS,EAAE,kEAAkE;gBAC7EC,cAAc,EAAE,YAAY;gBAC5BC,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACmB,SAAS,GAAG,aAAa;gBAC/CF,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACa,SAAS,GAAG,oEAAoE;cACxG,CAAE;cACFO,YAAY,EAAGH,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACmB,SAAS,GAAG,UAAU;gBAC5CF,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACa,SAAS,GAAG,kEAAkE;cACtG,CAAE;cACFtB,KAAK,EAAC,WAAW;cAAAM,QAAA,eAGjBV,OAAA;gBACEa,KAAK,EAAE;kBACLM,OAAO,EAAE,MAAM;kBACfe,aAAa,EAAE,QAAQ;kBACvBb,cAAc,EAAE,eAAe;kBAC/BJ,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACrFE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAClF,CAAE;gBAAAN,QAAA,gBAEFV,OAAA;kBACEa,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;oBACnFM,UAAU,EAAE,SAAS;oBACrBE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,8BAA8B;oBACzCE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFtC,OAAA;kBACEa,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;oBACnFM,UAAU,EAAE,SAAS;oBACrBE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,8BAA8B;oBACzCE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFtC,OAAA;kBACEa,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;oBACnFM,UAAU,EAAE,SAAS;oBACrBE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,8BAA8B;oBACzCE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtC,OAAA;YAAKW,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzCV,OAAA,CAACJ,MAAM,CAAC2C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAI,CAAE;cACpCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCd,UAAU,EAAE;gBAAEgB,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ClC,SAAS,EAAC,wEAAwE;cAClFE,KAAK,EAAE;gBACLiC,QAAQ,EAAE,OAAO;gBACjB7B,KAAK,EAAE;cACT,CAAE;cAAAP,QAAA,gBAGFV,OAAA;gBAAKW,SAAS,EAAC,gDAAgD;gBAAAD,QAAA,gBAC7DV,OAAA;kBAAIW,SAAS,EAAC,kHAAkH;kBAC5HE,KAAK,EAAE;oBACLkC,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAAtC,QAAA,gBAEJV,OAAA,CAACJ,MAAM,CAACqD,IAAI;oBACVtC,SAAS,EAAC,uBAAuB;oBACjC6B,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAES,CAAC,EAAE,CAAC,EAAE;sBAAER,KAAK,EAAE;oBAAI,CAAE;oBAC5CC,OAAO,EAAE;sBACPF,OAAO,EAAE,CAAC;sBACVS,CAAC,EAAE,CAAC;sBACJR,KAAK,EAAE,CAAC;sBACRS,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFvB,UAAU,EAAE;sBACVgB,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVM,UAAU,EAAE;wBACVP,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVb,KAAK,EAAE,GAAG;sBACVc,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrB5B,UAAU,EAAE;wBAAEgB,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACF/B,KAAK,EAAE;sBACL4C,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAAzC,QAAA,GACH,OAGC,eACAV,OAAA,CAACJ,MAAM,CAAC2C,GAAG;sBACT5B,SAAS,EAAC,+CAA+C;sBACzDgC,OAAO,EAAE;wBACPF,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBiB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF/B,UAAU,EAAE;wBACVgB,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACFhC,KAAK,EAAE;wBACL8C,eAAe,EAAE,SAAS;wBAC1BjC,SAAS,EAAE;sBACb;oBAAE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdtC,OAAA,CAACJ,MAAM,CAACqD,IAAI;oBACVtC,SAAS,EAAC,uBAAuB;oBACjC6B,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAES,CAAC,EAAE,EAAE;sBAAER,KAAK,EAAE;oBAAI,CAAE;oBAC3CC,OAAO,EAAE;sBACPF,OAAO,EAAE,CAAC;sBACVS,CAAC,EAAE,CAAC;sBACJR,KAAK,EAAE,CAAC;sBACRkB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnBT,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFvB,UAAU,EAAE;sBACVgB,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVe,CAAC,EAAE;wBACDhB,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVP,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVb,KAAK,EAAE,GAAG;sBACVc,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrB5B,UAAU,EAAE;wBAAEgB,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACF/B,KAAK,EAAE;sBACL4C,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAAzC,QAAA,GACH,MAGC,eACAV,OAAA,CAACJ,MAAM,CAAC2C,GAAG;sBACT5B,SAAS,EAAC,gDAAgD;sBAC1DgC,OAAO,EAAE;wBACPF,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBS,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACdU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBD,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF/B,UAAU,EAAE;wBACVgB,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACFhC,KAAK,EAAE;wBACL8C,eAAe,EAAE,SAAS;wBAC1BjC,SAAS,EAAE;sBACb;oBAAE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdtC,OAAA;oBACEW,SAAS,EAAC,6EAA6E;oBACvFE,KAAK,EAAE;sBACLS,UAAU,EAAE,SAAS;sBACrBI,SAAS,EAAE,4BAA4B;sBACvCT,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;sBACjDE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;sBAClD6C,UAAU,EAAE,KAAK;sBACjBC,aAAa,EAAE;oBACjB,CAAE;oBAAApD,QAAA,gBAEFV,OAAA;sBACE+D,GAAG,EAAC,cAAc;sBAClBC,GAAG,EAAC,gBAAgB;sBACpBrD,SAAS,EAAC,4BAA4B;sBACtCE,KAAK,EAAE;wBAAEoD,SAAS,EAAE;sBAAQ,CAAE;sBAC9BC,OAAO,EAAGpC,CAAC,IAAK;wBACdA,CAAC,CAACqC,MAAM,CAACtD,KAAK,CAACM,OAAO,GAAG,MAAM;wBAC/BW,CAAC,CAACqC,MAAM,CAACC,WAAW,CAACvD,KAAK,CAACM,OAAO,GAAG,MAAM;sBAC7C;oBAAE;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFtC,OAAA;sBACEW,SAAS,EAAC,gHAAgH;sBAC1HE,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfkD,QAAQ,EAAE;sBACZ,CAAE;sBAAA3D,QAAA,EACH;oBAED;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGLtC,OAAA,CAACJ,MAAM,CAAC2C,GAAG;kBACT5B,SAAS,EAAC,4CAA4C;kBACtD6B,OAAO,EAAE;oBAAEvB,KAAK,EAAE,CAAC;oBAAEwB,OAAO,EAAE;kBAAE,CAAE;kBAClCE,OAAO,EAAE;oBACP1B,KAAK,EAAE,MAAM;oBACbwB,OAAO,EAAE,CAAC;oBACVf,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFE,UAAU,EAAE;oBACVgB,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVnB,SAAS,EAAE;sBACTkB,QAAQ,EAAE,CAAC;sBACXQ,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFzC,KAAK,EAAE;oBACLS,UAAU,EAAE,mDAAmD;oBAC/DI,SAAS,EAAE;kBACb;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNtC,OAAA;gBAAKW,SAAS,EAAC;cAAyK;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNtC,OAAA;YAAKW,SAAS,EAAC,+CAA+C;YAAAD,QAAA,EAC3DJ,IAAI,iBACHN,OAAA,CAACJ,MAAM,CAAC2C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAI,CAAE;cACpCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCd,UAAU,EAAE;gBAAEgB,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ClC,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAG7CV,OAAA,CAACH,cAAc;gBACbS,IAAI,EAAEA,IAAK;gBACXgE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB1D,KAAK,EAAE;kBACLI,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFtC,OAAA;gBAAKW,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,gBACzCV,OAAA;kBAAKW,SAAS,EAAC,uGAAuG;kBAAAD,QAAA,EACnH,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,IAAI,KAAI;gBAAM;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNtC,OAAA;kBAAKW,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAC5F,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,KAAK,MAAK,SAAS,GAAI,SAAQnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,KAAM,EAAC,GAAGpE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE;gBAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA,CAACF,aAAa;MAAC6E,MAAM,EAAEnE,WAAY;MAACoE,SAAS,EAAEnE;IAAe;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACjE,CAAC;AAEP,CAAC;AAACjC,EAAA,CA1UIF,eAAe;EAAA,QACFR,WAAW;AAAA;AAAAkF,EAAA,GADxB1E,eAAe;AA4UrB,eAAeA,eAAe;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}