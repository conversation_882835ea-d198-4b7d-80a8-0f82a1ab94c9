{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Rotate quotes every 4 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700'\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-800px mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center space-x-2 sm:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: window.innerWidth <= 480 ? '24px' : '32px',\n                  height: window.innerWidth <= 480 ? '18px' : '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: window.innerWidth <= 480 ? '24px' : '32px',\n                  height: window.innerWidth <= 480 ? '24px' : '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [isKiswahili ? 'Karibu' : 'Welcome', \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"KjngAm+3V/a9XLrvpuaEcldfchY=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "inspiringQuotes", "interval", "setInterval", "prev", "length", "clearInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "header", "initial", "y", "opacity", "animate", "div", "scale", "transition", "duration", "delay", "style", "width", "window", "innerWidth", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "firstName", "name", "marginRight", "marginLeft", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "key", "cursor", "touchAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Rotate quotes every 4 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      {/* Header with User Profile and Notification Bell - Same as Home */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\"\n      >\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-center h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\n            <div className=\"max-w-800px mx-auto\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center space-x-2 sm:space-x-3\"\n              >\n                {/* Tanzania Flag - Using actual flag image */}\n                <div\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\n                  style={{\n                    width: window.innerWidth <= 480 ? '24px' : '32px',\n                    height: window.innerWidth <= 480 ? '18px' : '24px'\n                  }}\n                >\n                  <img\n                    src=\"https://flagcdn.com/w40/tz.png\"\n                    alt=\"Tanzania Flag\"\n                    className=\"w-full h-full object-cover\"\n                    style={{ objectFit: 'cover' }}\n                    onError={(e) => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }}\n                  />\n                </div>\n\n                {/* Amazing Animated Brainwave Text */}\n                <div className=\"relative brainwave-container\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects (no space) */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Official Logo - Small like profile */}\n                <div\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                  style={{\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: window.innerWidth <= 480 ? '24px' : '32px',\n                    height: window.innerWidth <= 480 ? '24px' : '32px'\n                  }}\n                >\n                  <img\n                    src=\"/favicon.png\"\n                    alt=\"Brainwave Logo\"\n                    className=\"w-full h-full object-cover\"\n                    style={{ objectFit: 'cover' }}\n                    onError={(e) => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }}\n                  />\n                  <div\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\n                    style={{\n                      display: 'none',\n                      fontSize: '12px'\n                    }}\n                  >\n                    🧠\n                  </div>\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {isKiswahili ? 'Karibu' : 'Welcome'}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGxB,WAAW,CAAC,CAAC;EACxC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;;EAKnD;EACA,MAAMiC,eAAe,GAAGH,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCH,eAAe,CAAEI,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACD,eAAe,CAACI,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACApC,OAAO,CAACqC,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAhB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAMiB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAEd,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDe,WAAW,EAAEf,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEgB,IAAI,EAAEtC,gBAAgB;IACtBuC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEd,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3De,WAAW,EAAEf,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClFgB,IAAI,EAAErC,MAAM;IACZsC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEd,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDe,WAAW,EAAEf,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjFgB,IAAI,EAAEzB,OAAO;IACb0B,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEd,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCe,WAAW,EAAEf,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EgB,IAAI,EAAEpC,WAAW;IACjBqC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEd,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpDe,WAAW,EAAEf,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClEgB,IAAI,EAAE9B,QAAQ;IACd+B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEd,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvCe,WAAW,EAAEf,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEgB,IAAI,EAAE7B,MAAM;IACZ8B,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAElC,UAAU;IAChBmC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACE1B,OAAA;IAAK2B,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B5B,OAAA,CAACnB,MAAM,CAACgD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K5B,OAAA;QAAK2B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD5B,OAAA;UAAK2B,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAE5E5B,OAAA;YAAK2B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC5B,OAAA,CAACnB,MAAM,CAACqD,GAAG;cACTJ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAI,CAAE;cACpCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CX,SAAS,EAAC,wEAAwE;cAAAC,QAAA,gBAGlF5B,OAAA;gBACE2B,SAAS,EAAC,wEAAwE;gBAClFY,KAAK,EAAE;kBACLC,KAAK,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDC,MAAM,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAd,QAAA,eAEF5B,OAAA;kBACE4C,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBlB,SAAS,EAAC,4BAA4B;kBACtCY,KAAK,EAAE;oBAAEO,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGC,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,8GAA8G;oBAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;sBACvB;sBACAF,CAAC,CAACC,MAAM,CAACV,KAAK,CAACY,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNzD,OAAA;gBAAK2B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C5B,OAAA;kBAAI2B,SAAS,EAAC,gGAAgG;kBAC1GY,KAAK,EAAE;oBACLmB,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA/B,QAAA,gBAEJ5B,OAAA,CAACnB,MAAM,CAAC+E,IAAI;oBACVjC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE6B,CAAC,EAAE,CAAC,EAAE;sBAAE1B,KAAK,EAAE;oBAAI,CAAE;oBAC5CF,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV6B,CAAC,EAAE,CAAC;sBACJ1B,KAAK,EAAE,CAAC;sBACR2B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVwB,UAAU,EAAE;wBACVzB,QAAQ,EAAE,CAAC;wBACX0B,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACV/B,KAAK,EAAE,GAAG;sBACVgC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrB/B,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLd,KAAK,EAAE,SAAS;sBAChB2C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlC,QAAA,GACH,OAGC,eACA5B,OAAA,CAACnB,MAAM,CAACqD,GAAG;sBACTP,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBG,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBkC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFjC,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACb0B,MAAM,EAAEC,QAAQ;wBAChB1B,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACL8B,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdzD,OAAA,CAACnB,MAAM,CAAC+E,IAAI;oBACVjC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE6B,CAAC,EAAE,EAAE;sBAAE1B,KAAK,EAAE;oBAAI,CAAE;oBAC3CF,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV6B,CAAC,EAAE,CAAC;sBACJ1B,KAAK,EAAE,CAAC;sBACRJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB+B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACF1B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVP,CAAC,EAAE;wBACDM,QAAQ,EAAE,CAAC;wBACX0B,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVzB,QAAQ,EAAE,GAAG;wBACb0B,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACV/B,KAAK,EAAE,GAAG;sBACVgC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrB/B,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLd,KAAK,EAAE,SAAS;sBAChB2C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlC,QAAA,GACH,MAGC,eACA5B,OAAA,CAACnB,MAAM,CAACqD,GAAG;sBACTP,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB6B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd9B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBsC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFjC,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACX0B,MAAM,EAAEC,QAAQ;wBAChB1B,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACL8B,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLzD,OAAA,CAACnB,MAAM,CAACqD,GAAG;kBACTP,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEU,KAAK,EAAE,CAAC;oBAAER,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPO,KAAK,EAAE,MAAM;oBACbR,OAAO,EAAE,CAAC;oBACVsC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlC,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVgC,SAAS,EAAE;sBACTjC,QAAQ,EAAE,CAAC;sBACX0B,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACF1B,KAAK,EAAE;oBACLgC,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNzD,OAAA;gBACE2B,SAAS,EAAC,gEAAgE;gBAC1EY,KAAK,EAAE;kBACLgC,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvC9B,KAAK,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDC,MAAM,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAd,QAAA,gBAEF5B,OAAA;kBACE4C,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBlB,SAAS,EAAC,4BAA4B;kBACtCY,KAAK,EAAE;oBAAEO,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACV,KAAK,CAACY,OAAO,GAAG,MAAM;oBAC/BH,CAAC,CAACC,MAAM,CAACuB,WAAW,CAACjC,KAAK,CAACY,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzD,OAAA;kBACE2B,SAAS,EAAC,gHAAgH;kBAC1HY,KAAK,EAAE;oBACLY,OAAO,EAAE,MAAM;oBACfsB,QAAQ,EAAE;kBACZ,CAAE;kBAAA7C,QAAA,EACH;gBAED;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzD,OAAA;gBAAK2B,SAAS,EAAC;cAAyK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBzD,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAK2B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5B,OAAA;UAAI2B,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBrB,WAAW,GAAG,QAAQ,GAAG,SAAS,EAAC,IAAE,EAAC,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,SAAS,MAAItE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,IAAI,MAAKpE,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eACLzD,OAAA;UAAG2B,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBrB,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJzD,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5B,OAAA,CAACN,MAAM;YAAC6C,KAAK,EAAE;cAAEd,KAAK,EAAE,SAAS;cAAEmD,WAAW,EAAE;YAAS;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC/C,eAAe,CAACF,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAAC6C,KAAK,EAAE;cAAEd,KAAK,EAAE,SAAS;cAAEoD,UAAU,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DzD,OAAA;YAAKuC,KAAK,EAAE;cAAEkC,QAAQ,EAAE,UAAU;cAAEhD,KAAK,EAAE,SAAS;cAAEqD,SAAS,EAAE;YAAS,CAAE;YAAAlD,QAAA,EAAC;UAE7E;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMNzD,OAAA;QAAK2B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC5B,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAAC2D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACzD,IAAI;YAC/B,oBACEvB,OAAA,CAACnB,MAAM,CAACqD,GAAG;cAETJ,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BK,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE2C,KAAK,GAAG;cAAI,CAAE;cAClDtD,SAAS,EAAG,kBAAiBqD,IAAI,CAACtD,UAAW,IAAGsD,IAAI,CAACvD,KAAM,EAAE;cAC7D0D,OAAO,EAAEA,CAAA,KAAMhF,QAAQ,CAAC6E,IAAI,CAACxD,IAAI,CAAE;cACnC4D,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGtC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACuC,GAAG,KAAK,OAAO,IAAIvC,CAAC,CAACuC,GAAG,KAAK,GAAG,EAAE;kBACtCpF,QAAQ,CAAC6E,IAAI,CAACxD,IAAI,CAAC;gBACrB;cACF,CAAE;cACFe,KAAK,EAAE;gBACLiD,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAA7D,QAAA,gBAIF5B,OAAA;gBAAK2B,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B5B,OAAA,CAACkF,aAAa;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENzD,OAAA;gBAAI2B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BoD,IAAI,CAAC3D;cAAK;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELzD,OAAA;gBAAG2B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCoD,IAAI,CAAC1D;cAAW;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GA9BCuB,IAAI,CAAC3D,KAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzD,OAAA,CAACnB,MAAM,CAACqD,GAAG;UACTJ,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CX,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjC5B,OAAA;YAAK2B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5B,OAAA,CAACR,eAAe;cAACmC,SAAS,EAAC;YAAuC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEzD,OAAA;cAAA4B,QAAA,EAAM;YAAkC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CzD,OAAA,CAACL,QAAQ;cAACgC,SAAS,EAAC;YAAuC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CArbID,GAAG;EAAA,QACUtB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAA2G,EAAA,GAHlCzF,GAAG;AAubT,eAAeA,GAAG;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}