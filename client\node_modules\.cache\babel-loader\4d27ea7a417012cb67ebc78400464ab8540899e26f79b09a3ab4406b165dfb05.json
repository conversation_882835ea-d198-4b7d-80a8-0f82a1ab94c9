{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\UserProfileHeader.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport { Tb<PERSON><PERSON>, TbUser } from 'react-icons/tb';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserProfileHeader = ({\n  className = \"\"\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    getClassName\n  } = useLanguage();\n  if (!user) return null;\n\n  // Get display name (fallback to username if name not available)\n  const displayName = user.name || user.username || 'User';\n\n  // Get class display with proper formatting\n  const getClassDisplay = () => {\n    var _user$level;\n    if (!user.class || !user.level) return '';\n\n    // Use the language context function if available\n    if (getClassName) {\n      return getClassName(user.class, user.level);\n    }\n\n    // Fallback formatting\n    const level = (_user$level = user.level) === null || _user$level === void 0 ? void 0 : _user$level.toLowerCase();\n    const classNum = user.class;\n    if (level === 'primary') {\n      return `Class ${classNum}`;\n    } else if (level === 'secondary') {\n      return `Form ${classNum}`;\n    } else if (level === 'advance') {\n      return `Form ${classNum}`;\n    }\n    return `${user.level} - Class ${classNum}`;\n  };\n  const classDisplay = getClassDisplay();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center space-x-3 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm\",\n        children: user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profileImage,\n          alt: \"Profile\",\n          className: \"w-full h-full rounded-full object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TbUser, {\n          className: \"w-4 h-4 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-1\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-900 truncate max-w-[120px]\",\n            children: displayName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), classDisplay && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 truncate max-w-[120px]\",\n          children: classDisplay\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: /*#__PURE__*/_jsxDEV(TbBell, {\n        className: \"w-5 h-5 text-gray-600 hover:text-gray-800 cursor-pointer transition-colors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfileHeader, \"sbTRCFI/qaotiwhZj0f0tfHnbYI=\", false, function () {\n  return [useSelector, useLanguage];\n});\n_c = UserProfileHeader;\nexport default UserProfileHeader;\nvar _c;\n$RefreshReg$(_c, \"UserProfileHeader\");", "map": {"version": 3, "names": ["React", "useSelector", "TbBell", "TbUser", "useLanguage", "jsxDEV", "_jsxDEV", "UserProfileHeader", "className", "_s", "user", "state", "getClassName", "displayName", "name", "username", "getClassDisplay", "_user$level", "class", "level", "toLowerCase", "classNum", "classDisplay", "children", "profileImage", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/UserProfileHeader.js"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport { Tb<PERSON><PERSON>, TbUser } from 'react-icons/tb';\nimport { useLanguage } from '../contexts/LanguageContext';\n\nconst UserProfileHeader = ({ className = \"\" }) => {\n  const { user } = useSelector((state) => state.user);\n  const { getClassName } = useLanguage();\n\n  if (!user) return null;\n\n  // Get display name (fallback to username if name not available)\n  const displayName = user.name || user.username || 'User';\n  \n  // Get class display with proper formatting\n  const getClassDisplay = () => {\n    if (!user.class || !user.level) return '';\n    \n    // Use the language context function if available\n    if (getClassName) {\n      return getClassName(user.class, user.level);\n    }\n    \n    // Fallback formatting\n    const level = user.level?.toLowerCase();\n    const classNum = user.class;\n    \n    if (level === 'primary') {\n      return `Class ${classNum}`;\n    } else if (level === 'secondary') {\n      return `Form ${classNum}`;\n    } else if (level === 'advance') {\n      return `Form ${classNum}`;\n    }\n    \n    return `${user.level} - Class ${classNum}`;\n  };\n\n  const classDisplay = getClassDisplay();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {/* User Profile Section */}\n      <div className=\"flex items-center space-x-2\">\n        {/* Profile Picture or Avatar */}\n        <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm\">\n          {user.profileImage ? (\n            <img \n              src={user.profileImage} \n              alt=\"Profile\" \n              className=\"w-full h-full rounded-full object-cover\"\n            />\n          ) : (\n            <TbUser className=\"w-4 h-4 text-white\" />\n          )}\n        </div>\n        \n        {/* User Info */}\n        <div className=\"text-right\">\n          <div className=\"flex items-center justify-end space-x-1\">\n            <p className=\"text-sm font-medium text-gray-900 truncate max-w-[120px]\">\n              {displayName}\n            </p>\n          </div>\n          {classDisplay && (\n            <p className=\"text-xs text-gray-500 truncate max-w-[120px]\">\n              {classDisplay}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Bell Icon */}\n      <div className=\"relative\">\n        <TbBell className=\"w-5 h-5 text-gray-600 hover:text-gray-800 cursor-pointer transition-colors\" />\n        {/* Optional notification dot */}\n        {/* <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full\"></div> */}\n      </div>\n    </div>\n  );\n};\n\nexport default UserProfileHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM;IAAEC;EAAK,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAa,CAAC,GAAGR,WAAW,CAAC,CAAC;EAEtC,IAAI,CAACM,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMG,WAAW,GAAGH,IAAI,CAACI,IAAI,IAAIJ,IAAI,CAACK,QAAQ,IAAI,MAAM;;EAExD;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IAC5B,IAAI,CAACP,IAAI,CAACQ,KAAK,IAAI,CAACR,IAAI,CAACS,KAAK,EAAE,OAAO,EAAE;;IAEzC;IACA,IAAIP,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACF,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACS,KAAK,CAAC;IAC7C;;IAEA;IACA,MAAMA,KAAK,IAAAF,WAAA,GAAGP,IAAI,CAACS,KAAK,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,WAAW,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAGX,IAAI,CAACQ,KAAK;IAE3B,IAAIC,KAAK,KAAK,SAAS,EAAE;MACvB,OAAQ,SAAQE,QAAS,EAAC;IAC5B,CAAC,MAAM,IAAIF,KAAK,KAAK,WAAW,EAAE;MAChC,OAAQ,QAAOE,QAAS,EAAC;IAC3B,CAAC,MAAM,IAAIF,KAAK,KAAK,SAAS,EAAE;MAC9B,OAAQ,QAAOE,QAAS,EAAC;IAC3B;IAEA,OAAQ,GAAEX,IAAI,CAACS,KAAM,YAAWE,QAAS,EAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAGN,eAAe,CAAC,CAAC;EAEtC,oBACEV,OAAA;IAAKE,SAAS,EAAG,+BAA8BA,SAAU,EAAE;IAAAe,QAAA,gBAEzDjB,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAe,QAAA,gBAE1CjB,OAAA;QAAKE,SAAS,EAAC,+GAA+G;QAAAe,QAAA,EAC3Hb,IAAI,CAACc,YAAY,gBAChBlB,OAAA;UACEmB,GAAG,EAAEf,IAAI,CAACc,YAAa;UACvBE,GAAG,EAAC,SAAS;UACblB,SAAS,EAAC;QAAyC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,gBAEFxB,OAAA,CAACH,MAAM;UAACK,SAAS,EAAC;QAAoB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACzC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxB,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAe,QAAA,gBACzBjB,OAAA;UAAKE,SAAS,EAAC,yCAAyC;UAAAe,QAAA,eACtDjB,OAAA;YAAGE,SAAS,EAAC,0DAA0D;YAAAe,QAAA,EACpEV;UAAW;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLR,YAAY,iBACXhB,OAAA;UAAGE,SAAS,EAAC,8CAA8C;UAAAe,QAAA,EACxDD;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKE,SAAS,EAAC,UAAU;MAAAe,QAAA,eACvBjB,OAAA,CAACJ,MAAM;QAACM,SAAS,EAAC;MAA4E;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAG9F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA3EIF,iBAAiB;EAAA,QACJN,WAAW,EACHG,WAAW;AAAA;AAAA2B,EAAA,GAFhCxB,iBAAiB;AA6EvB,eAAeA,iBAAiB;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}