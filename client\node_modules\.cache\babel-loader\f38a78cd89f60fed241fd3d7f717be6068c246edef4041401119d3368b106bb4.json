{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n  // Notification counts for Hub navigation\n  const hubNotificationCounts = {\n    takeQuiz: 2,\n    // 2 new quizzes\n    studyMaterials: 4,\n    // 4 new study materials (past papers)\n    videoLessons: 3,\n    // 3 new video lessons\n    reports: 0,\n    // No notifications for reports\n    forum: 5,\n    // 5 new forum posts\n    ranking: 0,\n    // No notifications for ranking\n    skills: 1 // 1 new skill video\n  };\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\"Karibu tena\", \"Habari za asubuhi\", \"Umefika salama\", \"Karibu sana\", \"Hujambo\", \"Habari za leo\", \"Karibu kwenye mafunzo\", \"Umekuja vizuri\", \"Karibu darasani\", \"Habari za masomo\"] : [\"Welcome back\", \"Great to see you\", \"Hello there\", \"Good to have you here\", \"Nice to see you again\", \"Ready to learn\", \"Let's get started\", \"Time to shine\", \"Here we go\", \"Ready for success\"];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome(prev => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700',\n    notificationCount: hubNotificationCounts.takeQuiz\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700',\n    notificationCount: hubNotificationCounts.studyMaterials\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700',\n    notificationCount: hubNotificationCounts.videoLessons\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700',\n    notificationCount: hubNotificationCounts.reports\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700',\n    notificationCount: hubNotificationCounts.ranking\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700',\n    notificationCount: hubNotificationCounts.skills\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700',\n    notificationCount: hubNotificationCounts.forum\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [welcomeMessages[currentWelcome], \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), item.notificationCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '-8px',\n                    backgroundColor: '#ef4444',\n                    color: 'white',\n                    borderRadius: '50%',\n                    width: '20px',\n                    height: '20px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '12px',\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 8px rgba(239, 68, 68, 0.4)',\n                    zIndex: 10\n                  },\n                  children: item.notificationCount > 99 ? '99+' : item.notificationCount\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"QVrrehD5i6wlocOUY5BYhWKzqMg=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "currentWelcome", "setCurrentWelcome", "hubNotificationCounts", "takeQuiz", "studyMaterials", "videoLessons", "reports", "forum", "ranking", "skills", "inspiringQuotes", "welcomeMessages", "interval", "setInterval", "prev", "length", "clearInterval", "welcomeInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "notificationCount", "className", "children", "firstName", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "fontSize", "marginTop", "map", "item", "index", "IconComponent", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "cursor", "touchAction", "position", "top", "right", "backgroundColor", "borderRadius", "width", "height", "display", "alignItems", "justifyContent", "fontWeight", "boxShadow", "zIndex", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n  // Notification counts for Hub navigation\n  const hubNotificationCounts = {\n    takeQuiz: 2, // 2 new quizzes\n    studyMaterials: 4, // 4 new study materials (past papers)\n    videoLessons: 3, // 3 new video lessons\n    reports: 0, // No notifications for reports\n    forum: 5, // 5 new forum posts\n    ranking: 0, // No notifications for ranking\n    skills: 1 // 1 new skill video\n  };\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\n    \"Karibu tena\",\n    \"Habari za asubuhi\",\n    \"Umefika salama\",\n    \"Karibu sana\",\n    \"Hujambo\",\n    \"Habari za leo\",\n    \"Karibu kwenye mafunzo\",\n    \"Umekuja vizuri\",\n    \"Karibu darasani\",\n    \"Habari za masomo\"\n  ] : [\n    \"Welcome back\",\n    \"Great to see you\",\n    \"Hello there\",\n    \"Good to have you here\",\n    \"Nice to see you again\",\n    \"Ready to learn\",\n    \"Let's get started\",\n    \"Time to shine\",\n    \"Here we go\",\n    \"Ready for success\"\n  ];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome((prev) => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700',\n      notificationCount: hubNotificationCounts.takeQuiz\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700',\n      notificationCount: hubNotificationCounts.studyMaterials\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700',\n      notificationCount: hubNotificationCounts.videoLessons\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700',\n      notificationCount: hubNotificationCounts.reports\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700',\n      notificationCount: hubNotificationCounts.ranking\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700',\n      notificationCount: hubNotificationCounts.skills\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700',\n      notificationCount: hubNotificationCounts.forum\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {welcomeMessages[currentWelcome]}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\" style={{ position: 'relative' }}>\n                    <IconComponent />\n                    {/* Notification Badge */}\n                    {item.notificationCount > 0 && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '-8px',\n                          backgroundColor: '#ef4444',\n                          color: 'white',\n                          borderRadius: '50%',\n                          width: '20px',\n                          height: '20px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          fontSize: '12px',\n                          fontWeight: 'bold',\n                          boxShadow: '0 2px 8px rgba(239, 68, 68, 0.4)',\n                          zIndex: 10\n                        }}\n                      >\n                        {item.notificationCount > 99 ? '99+' : item.notificationCount}\n                      </div>\n                    )}\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGxB,WAAW,CAAC,CAAC;EACxC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAMmC,qBAAqB,GAAG;IAC5BC,QAAQ,EAAE,CAAC;IAAE;IACbC,cAAc,EAAE,CAAC;IAAE;IACnBC,YAAY,EAAE,CAAC;IAAE;IACjBC,OAAO,EAAE,CAAC;IAAE;IACZC,KAAK,EAAE,CAAC;IAAE;IACVC,OAAO,EAAE,CAAC;IAAE;IACZC,MAAM,EAAE,CAAC,CAAC;EACZ,CAAC;;EAKD;EACA,MAAMC,eAAe,GAAGb,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA,MAAMc,eAAe,GAAGd,WAAW,GAAG,CACpC,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,CACnB,GAAG,CACF,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,mBAAmB,CACpB;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM4C,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCd,eAAe,CAAEe,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIJ,eAAe,CAACK,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,eAAe,CAACK,MAAM,CAAC,CAAC;;EAE5B;EACA/C,SAAS,CAAC,MAAM;IACd,MAAMiD,eAAe,GAAGJ,WAAW,CAAC,MAAM;MACxCZ,iBAAiB,CAAEa,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACN,eAAe,CAACI,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAhD,OAAO,CAACiD,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACA5B,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAM6B,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE1B,WAAW,GAAG,eAAe,GAAG,WAAW;IAClD2B,WAAW,EAAE3B,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxE4B,IAAI,EAAElD,gBAAgB;IACtBmD,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE,2BAA2B;IACvCC,iBAAiB,EAAE3B,qBAAqB,CAACC;EAC3C,CAAC,EACD;IACEoB,KAAK,EAAE1B,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3D2B,WAAW,EAAE3B,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClF4B,IAAI,EAAEjD,MAAM;IACZkD,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE,+BAA+B;IAC3CC,iBAAiB,EAAE3B,qBAAqB,CAACE;EAC3C,CAAC,EACD;IACEmB,KAAK,EAAE1B,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxD2B,WAAW,EAAE3B,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjF4B,IAAI,EAAErC,OAAO;IACbsC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE,yBAAyB;IACrCC,iBAAiB,EAAE3B,qBAAqB,CAACG;EAC3C,CAAC,EACD;IACEkB,KAAK,EAAE1B,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzC2B,WAAW,EAAE3B,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5E4B,IAAI,EAAEhD,WAAW;IACjBiD,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE,6BAA6B;IACzCC,iBAAiB,EAAE3B,qBAAqB,CAACI;EAC3C,CAAC,EACD;IACEiB,KAAK,EAAE1B,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpD2B,WAAW,EAAE3B,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClE4B,IAAI,EAAE1C,QAAQ;IACd2C,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE,+BAA+B;IAC3CC,iBAAiB,EAAE3B,qBAAqB,CAACM;EAC3C,CAAC,EACD;IACEe,KAAK,EAAE1B,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvC2B,WAAW,EAAE3B,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtE4B,IAAI,EAAEzC,MAAM;IACZ0C,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE,+BAA+B;IAC3CC,iBAAiB,EAAE3B,qBAAqB,CAACO;EAC3C,CAAC,EACD;IACEc,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE9C,UAAU;IAChB+C,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE,2BAA2B;IACvCC,iBAAiB,EAAE3B,qBAAqB,CAACK;EAC3C,CAAC,CACF;EAED,oBACEjB,OAAA;IAAKwC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAG5BzC,OAAA;MAAKwC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzC,OAAA;QAAKwC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzC,OAAA;UAAIwC,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBpB,eAAe,CAACX,cAAc,CAAC,EAAC,IAAE,EAAC,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,SAAS,MAAItC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,IAAI,MAAKpC,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACL/C,OAAA;UAAGwC,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBlC,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJ/C,OAAA;UAAKwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBzC,OAAA,CAACN,MAAM;YAACsD,KAAK,EAAE;cAAEX,KAAK,EAAE,SAAS;cAAEY,WAAW,EAAE;YAAS;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC3B,eAAe,CAACZ,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAACsD,KAAK,EAAE;cAAEX,KAAK,EAAE,SAAS;cAAEa,UAAU,EAAE;YAAS;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D/C,OAAA;YAAKgD,KAAK,EAAE;cAAEG,QAAQ,EAAE,UAAU;cAAEd,KAAK,EAAE,SAAS;cAAEe,SAAS,EAAE;YAAS,CAAE;YAAAX,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMN/C,OAAA;QAAKwC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzC,OAAA;UAAKwC,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBT,eAAe,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAACnB,IAAI;YAC/B,oBACEnC,OAAA,CAACnB,MAAM,CAAC4E,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAET,KAAK,GAAG;cAAI,CAAE;cAClDf,SAAS,EAAG,kBAAiBc,IAAI,CAAChB,UAAW,IAAGgB,IAAI,CAACjB,KAAM,EAAE;cAC7D4B,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAACmD,IAAI,CAAClB,IAAI,CAAE;cACnC8B,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtCnE,QAAQ,CAACmD,IAAI,CAAClB,IAAI,CAAC;gBACrB;cACF,CAAE;cACFY,KAAK,EAAE;gBACLuB,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAA/B,QAAA,gBAIFzC,OAAA;gBAAKwC,SAAS,EAAC,eAAe;gBAACQ,KAAK,EAAE;kBAAEyB,QAAQ,EAAE;gBAAW,CAAE;gBAAAhC,QAAA,gBAC7DzC,OAAA,CAACwD,aAAa;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAEhBO,IAAI,CAACf,iBAAiB,GAAG,CAAC,iBACzBvC,OAAA;kBACEgD,KAAK,EAAE;oBACLyB,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,MAAM;oBACXC,KAAK,EAAE,MAAM;oBACbC,eAAe,EAAE,SAAS;oBAC1BvC,KAAK,EAAE,OAAO;oBACdwC,YAAY,EAAE,KAAK;oBACnBC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxB/B,QAAQ,EAAE,MAAM;oBAChBgC,UAAU,EAAE,MAAM;oBAClBC,SAAS,EAAE,kCAAkC;oBAC7CC,MAAM,EAAE;kBACV,CAAE;kBAAA5C,QAAA,EAEDa,IAAI,CAACf,iBAAiB,GAAG,EAAE,GAAG,KAAK,GAAGe,IAAI,CAACf;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/C,OAAA;gBAAIwC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3Ba,IAAI,CAACrB;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEL/C,OAAA;gBAAGwC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCa,IAAI,CAACpB;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GAtDCO,IAAI,CAACrB,KAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuDL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/C,OAAA,CAACnB,MAAM,CAAC4E,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CxB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjCzC,OAAA;YAAKwC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCzC,OAAA,CAACR,eAAe;cAACgD,SAAS,EAAC;YAAuC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE/C,OAAA;cAAAyC,QAAA,EAAM;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C/C,OAAA,CAACL,QAAQ;cAAC6C,SAAS,EAAC;YAAuC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAxRID,GAAG;EAAA,QACUtB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAAuG,EAAA,GAHlCrF,GAAG;AA0RT,eAAeA,GAAG;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}