{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\VideoGrid.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  _s();\n  // Mobile detection state\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 767);\n\n  // Update mobile state on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 767);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"videos-grid\",\n    children: paginatedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: paginatedVideos.map((video, index) => {\n        var _user$name, _user$name$charAt;\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    // Fallback logic for failed thumbnails\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      // For YouTube videos, try different quality thumbnails\n                      let videoId = video.videoID;\n                      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                        videoId = match ? match[1] : videoId;\n                      }\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, 'https://via.placeholder.com/320x180/4A90E2/FFFFFF?text=Video+Lesson'];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = 'https://via.placeholder.com/320x180/4A90E2/FFFFFF?text=Video+Lesson';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 21\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 25\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 39\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: [video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                ref: ref => setVideoRef(ref),\n                controls: true,\n                autoPlay: true,\n                playsInline: true,\n                preload: \"metadata\",\n                width: \"100%\",\n                height: \"100%\",\n                poster: getThumbnailUrl(video),\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  backgroundColor: '#000',\n                  objectFit: 'contain'\n                },\n                onError: e => {\n                  setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                },\n                onCanPlay: () => {\n                  setVideoError(null);\n                },\n                crossOrigin: \"anonymous\",\n                children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                  src: video.signedVideoUrl || video.videoUrl,\n                  type: \"video/mp4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 25\n                }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => /*#__PURE__*/_jsxDEV(\"track\", {\n                  kind: \"subtitles\",\n                  src: subtitle.url,\n                  srcLang: subtitle.language,\n                  label: subtitle.languageName,\n                  default: subtitle.isDefault || subIndex === 0\n                }, `${subtitle.language}-${subIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 27\n                }, this)), \"Your browser does not support the video tag.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 23\n              }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                style: {\n                  width: '100%',\n                  height: '100%',\n                  border: 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-actions-horizontal\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`,\n                  onClick: () => setCommentsExpanded(!commentsExpanded),\n                  children: [\"\\uD83D\\uDCAC Comments (\", getCurrentVideoComments().length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"youtube-action-btn-small\",\n                  children: \"\\uD83D\\uDC4D Like\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"youtube-action-btn-small close-btn\",\n                  onClick: () => setCurrentVideoIndex(null),\n                  children: \"\\u2715 Close\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), (commentsExpanded || isMobile) && currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-comments-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [getCurrentVideoComments().length, \" Comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comment-input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-avatar\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"youtube-comment-input-field\",\n                      value: newComment,\n                      onChange: e => setNewComment(e.target.value),\n                      placeholder: isKiswahili ? \"Andika maoni yako...\" : \"Add a comment...\",\n                      rows: \"1\",\n                      style: {\n                        minHeight: '20px',\n                        resize: 'none',\n                        overflow: 'hidden'\n                      },\n                      onInput: e => {\n                        e.target.style.height = 'auto';\n                        e.target.style.height = e.target.scrollHeight + 'px';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 29\n                    }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn cancel\",\n                        onClick: () => setNewComment(''),\n                        children: isKiswahili ? 'Ghairi' : 'Cancel'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn submit\",\n                        onClick: handleAddComment,\n                        disabled: !newComment.trim(),\n                        children: isKiswahili ? 'Tuma' : 'Comment'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-list\",\n                  children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center',\n                      padding: '40px 0',\n                      color: '#606060'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '48px',\n                        marginBottom: '16px'\n                      },\n                      children: \"\\uD83D\\uDCAC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: isKiswahili ? 'Hakuna maoni bado. Kuwa wa kwanza kushiriki mawazo yako!' : 'No comments yet. Be the first to share your thoughts!'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 29\n                  }, this) : getCurrentVideoComments().map(comment => {\n                    var _comment$author, _comment$author$charA, _comment$likedBy, _comment$likedBy2;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-avatar\",\n                        children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 255,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-header\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-author\",\n                            children: comment.author\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 260,\n                            columnNumber: 37\n                          }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                            style: {\n                              color: '#1d9bf0',\n                              fontSize: '12px',\n                              marginLeft: '4px'\n                            },\n                            title: \"Verified Admin\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 262,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-time\",\n                            children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 264,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 259,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-text\",\n                          children: comment.text\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 268,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-actions\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => handleLikeComment(comment._id || comment.id),\n                            className: `youtube-comment-action ${(_comment$likedBy = comment.likedBy) !== null && _comment$likedBy !== void 0 && _comment$likedBy.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: (_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 276,\n                              columnNumber: 39\n                            }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: comment.likes\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 277,\n                              columnNumber: 61\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 272,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-comment-action\",\n                            children: isKiswahili ? 'Jibu' : 'Reply'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 279,\n                            columnNumber: 37\n                          }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"youtube-comment-action\",\n                            onClick: () => {\n                              if (window.confirm(isKiswahili ? 'Una uhakika unataka kufuta maoni haya?' : 'Are you sure you want to delete this comment?')) {\n                                handleDeleteComment(comment._id || comment.id);\n                              }\n                            },\n                            children: isKiswahili ? 'Futa' : 'Delete'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 283,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 33\n                      }, this)]\n                    }, comment._id || comment.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 31\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n        className: \"empty-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"suggestion\",\n        children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoGrid, \"bAK1OGtJJbb1pBfFY9WxZQMDfa8=\");\n_c = VideoGrid;\nexport default VideoGrid;\nvar _c;\n$RefreshReg$(_c, \"VideoGrid\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "MdVerified", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoGrid", "paginatedVideos", "currentVideoIndex", "handleShowVideo", "getThumbnailUrl", "getSubjectName", "selectedLevel", "isKiswahili", "setVideoRef", "setVideoError", "videoError", "setCurrentVideoIndex", "commentsExpanded", "setCommentsExpanded", "getCurrentVideoComments", "newComment", "setNewComment", "handleAddComment", "handleLikeComment", "handleDeleteComment", "formatTimeAgo", "user", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "className", "children", "length", "map", "video", "index", "_user$name", "_user$name$charAt", "onClick", "src", "alt", "title", "loading", "onError", "e", "videoID", "includes", "videoId", "match", "fallbacks", "currentSrc", "target", "currentIndex", "findIndex", "url", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "duration", "subtitles", "subject", "class", "topic", "sharedFromClass", "videoUrl", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "signedVideoUrl", "type", "subtitle", "subIndex", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "name", "char<PERSON>t", "toUpperCase", "flex", "value", "onChange", "placeholder", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "trim", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "comment", "_comment$author", "_comment$author$charA", "_comment$likedBy", "_comment$likedBy2", "avatar", "author", "userRole", "isAdmin", "marginLeft", "createdAt", "timestamp", "text", "_id", "id", "<PERSON><PERSON><PERSON>", "likes", "confirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/VideoGrid.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\n\nconst VideoGrid = ({\n  paginatedVideos,\n  currentVideoIndex,\n  handleShowVideo,\n  getThumbnailUrl,\n  getSubjectName,\n  selectedLevel,\n  isKiswahili,\n  setVideoRef,\n  setVideoError,\n  videoError,\n  setCurrentVideoIndex,\n  commentsExpanded,\n  setCommentsExpanded,\n  getCurrentVideoComments,\n  newComment,\n  setNewComment,\n  handleAddComment,\n  handleLikeComment,\n  handleDeleteComment,\n  formatTimeAgo,\n  user\n}) => {\n  // Mobile detection state\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 767);\n\n  // Update mobile state on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 767);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  return (\n    <div className=\"videos-grid\">\n      {paginatedVideos.length > 0 ? (\n        <>\n          {paginatedVideos.map((video, index) => (\n            <React.Fragment key={index}>\n              <div className=\"video-item\">\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = video.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            'https://via.placeholder.com/320x180/4A90E2/FFFFFF?text=Video+Lesson'\n                          ];\n\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          }\n                        } else {\n                          e.target.src = 'https://via.placeholder.com/320x180/4A90E2/FFFFFF?text=Video+Lesson';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                          `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                            (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                            `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    {video.videoUrl ? (\n                      <video\n                        ref={(ref) => setVideoRef(ref)}\n                        controls\n                        autoPlay\n                        playsInline\n                        preload=\"metadata\"\n                        width=\"100%\"\n                        height=\"100%\"\n                        poster={getThumbnailUrl(video)}\n                        style={{\n                          width: '100%',\n                          height: '100%',\n                          backgroundColor: '#000',\n                          objectFit: 'contain'\n                        }}\n                        onError={(e) => {\n                          setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                        }}\n                        onCanPlay={() => {\n                          setVideoError(null);\n                        }}\n                        crossOrigin=\"anonymous\"\n                      >\n                        <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                        {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, subIndex) => (\n                          <track\n                            key={`${subtitle.language}-${subIndex}`}\n                            kind=\"subtitles\"\n                            src={subtitle.url}\n                            srcLang={subtitle.language}\n                            label={subtitle.languageName}\n                            default={subtitle.isDefault || subIndex === 0}\n                          />\n                        ))}\n                        Your browser does not support the video tag.\n                      </video>\n                    ) : video.videoID ? (\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        style={{ \n                          width: '100%', \n                          height: '100%', \n                          border: 'none'\n                        }}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                      </div>\n                    )}\n                    \n                    {/* Horizontal Action Buttons */}\n                    <div className=\"youtube-video-actions-horizontal\">\n                      <button\n                        className={`youtube-action-btn-small ${commentsExpanded ? 'active' : ''}`}\n                        onClick={() => setCommentsExpanded(!commentsExpanded)}\n                      >\n                        💬 Comments ({getCurrentVideoComments().length})\n                      </button>\n                      <button className=\"youtube-action-btn-small\">\n                        👍 Like\n                      </button>\n                      <button\n                        className=\"youtube-action-btn-small close-btn\"\n                        onClick={() => setCurrentVideoIndex(null)}\n                      >\n                        ✕ Close\n                      </button>\n                    </div>\n\n                    {/* Comments Section */}\n                    {(commentsExpanded || isMobile) && currentVideoIndex === index && (\n                      <div className=\"youtube-comments-section\">\n                        <div className=\"youtube-comments-header\">\n                          <span>{getCurrentVideoComments().length} Comments</span>\n                        </div>\n\n                        {/* Add Comment */}\n                        <div className=\"youtube-comment-input\">\n                          <div className=\"youtube-comment-avatar\">\n                            {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                          </div>\n                          <div style={{ flex: 1 }}>\n                            <textarea\n                              className=\"youtube-comment-input-field\"\n                              value={newComment}\n                              onChange={(e) => setNewComment(e.target.value)}\n                              placeholder={isKiswahili ? \"Andika maoni yako...\" : \"Add a comment...\"}\n                              rows=\"1\"\n                              style={{\n                                minHeight: '20px',\n                                resize: 'none',\n                                overflow: 'hidden'\n                              }}\n                              onInput={(e) => {\n                                e.target.style.height = 'auto';\n                                e.target.style.height = e.target.scrollHeight + 'px';\n                              }}\n                            />\n                            {newComment.trim() && (\n                              <div className=\"youtube-comment-actions\">\n                                <button\n                                  className=\"youtube-comment-btn cancel\"\n                                  onClick={() => setNewComment('')}\n                                >\n                                  {isKiswahili ? 'Ghairi' : 'Cancel'}\n                                </button>\n                                <button\n                                  className=\"youtube-comment-btn submit\"\n                                  onClick={handleAddComment}\n                                  disabled={!newComment.trim()}\n                                >\n                                  {isKiswahili ? 'Tuma' : 'Comment'}\n                                </button>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Comments List */}\n                        <div className=\"youtube-comments-list\">\n                          {getCurrentVideoComments().length === 0 ? (\n                            <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                              <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                              <p>{isKiswahili ? 'Hakuna maoni bado. Kuwa wa kwanza kushiriki mawazo yako!' : 'No comments yet. Be the first to share your thoughts!'}</p>\n                            </div>\n                          ) : (\n                            getCurrentVideoComments().map((comment) => (\n                              <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                <div className=\"youtube-comment-avatar\">\n                                  {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"youtube-comment-content\">\n                                  <div className=\"youtube-comment-header\">\n                                    <span className=\"youtube-comment-author\">{comment.author}</span>\n                                    {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                      <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                    )}\n                                    <span className=\"youtube-comment-time\">\n                                      {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                    </span>\n                                  </div>\n                                  <div className=\"youtube-comment-text\">\n                                    {comment.text}\n                                  </div>\n                                  <div className=\"youtube-comment-actions\">\n                                    <button\n                                      onClick={() => handleLikeComment(comment._id || comment.id)}\n                                      className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                    >\n                                      <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                      {comment.likes > 0 && <span>{comment.likes}</span>}\n                                    </button>\n                                    <button className=\"youtube-comment-action\">\n                                      {isKiswahili ? 'Jibu' : 'Reply'}\n                                    </button>\n                                    {comment.user === user?._id && (\n                                      <button\n                                        className=\"youtube-comment-action\"\n                                        onClick={() => {\n                                          if (window.confirm(isKiswahili ? 'Una uhakika unataka kufuta maoni haya?' : 'Are you sure you want to delete this comment?')) {\n                                            handleDeleteComment(comment._id || comment.id);\n                                          }\n                                        }}\n                                      >\n                                        {isKiswahili ? 'Futa' : 'Delete'}\n                                      </button>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            ))\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </React.Fragment>\n          ))}\n        </>\n      ) : (\n        <div className=\"empty-state\">\n          <FaGraduationCap className=\"empty-icon\" />\n          <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n          <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n          <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoGrid;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,SAAS,GAAGA,CAAC;EACjBC,eAAe;EACfC,iBAAiB;EACjBC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,aAAa;EACbC,WAAW;EACXC,WAAW;EACXC,aAAa;EACbC,UAAU;EACVC,oBAAoB;EACpBC,gBAAgB;EAChBC,mBAAmB;EACnBC,uBAAuB;EACvBC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,iBAAiB;EACjBC,mBAAmB;EACnBC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAACmC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoC,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EACN,oBACE9B,OAAA;IAAKiC,SAAS,EAAC,aAAa;IAAAC,QAAA,EACzB9B,eAAe,CAAC+B,MAAM,GAAG,CAAC,gBACzBnC,OAAA,CAAAE,SAAA;MAAAgC,QAAA,EACG9B,eAAe,CAACgC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK;QAAA,IAAAC,UAAA,EAAAC,iBAAA;QAAA,oBAChCxC,OAAA,CAACR,KAAK,CAACS,QAAQ;UAAAiC,QAAA,eACblC,OAAA;YAAKiC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAACQ,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACgC,KAAK,CAAE;cAAAJ,QAAA,gBAChElC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBACE0C,GAAG,EAAEnC,eAAe,CAAC8B,KAAK,CAAE;kBAC5BM,GAAG,EAAEN,KAAK,CAACO,KAAM;kBACjBX,SAAS,EAAC,iBAAiB;kBAC3BY,OAAO,EAAC,MAAM;kBACdC,OAAO,EAAGC,CAAC,IAAK;oBACd;oBACA,IAAIV,KAAK,CAACW,OAAO,IAAI,CAACX,KAAK,CAACW,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D;sBACA,IAAIC,OAAO,GAAGb,KAAK,CAACW,OAAO;sBAC3B,IAAIE,OAAO,CAACD,QAAQ,CAAC,aAAa,CAAC,IAAIC,OAAO,CAACD,QAAQ,CAAC,UAAU,CAAC,EAAE;wBACnE,MAAME,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;wBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;sBACtC;sBAEA,MAAME,SAAS,GAAG,CACf,8BAA6BF,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,qEAAqE,CACtE;sBAED,MAAMG,UAAU,GAAGN,CAAC,CAACO,MAAM,CAACZ,GAAG;sBAC/B,MAAMa,YAAY,GAAGH,SAAS,CAACI,SAAS,CAACC,GAAG,IAAIJ,UAAU,CAACJ,QAAQ,CAACQ,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAE1F,IAAIJ,YAAY,GAAGH,SAAS,CAACjB,MAAM,GAAG,CAAC,EAAE;wBACvCY,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAGU,SAAS,CAACG,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACLR,CAAC,CAACO,MAAM,CAACZ,GAAG,GAAG,qEAAqE;oBACtF;kBACF;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF/D,OAAA;kBAAKiC,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlC,OAAA,CAACL,YAAY;oBAACsC,SAAS,EAAC;kBAAW;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN/D,OAAA;kBAAKiC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAC5BG,KAAK,CAAC2B,QAAQ,IAAI;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACL1B,KAAK,CAAC4B,SAAS,IAAI5B,KAAK,CAAC4B,SAAS,CAAC9B,MAAM,GAAG,CAAC,iBAC5CnC,OAAA;kBAAKiC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlC,OAAA,CAACH,YAAY;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN/D,OAAA;gBAAKiC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjClC,OAAA;kBAAIiC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEG,KAAK,CAACO;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9C/D,OAAA;kBAAKiC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlC,OAAA;oBAAMiC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAE1B,cAAc,CAAC6B,KAAK,CAAC6B,OAAO;kBAAC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtE/D,OAAA;oBAAMiC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC1BzB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEC,WAAW,GAAI,aAAY2B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM,EAAC,GAAI,SAAQ9B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM,EAAC,GACvG,QAAO9B,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAM;kBAAC;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/D,OAAA;kBAAKiC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACxBG,KAAK,CAAC+B,KAAK,iBAAIpE,OAAA;oBAAMiC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEG,KAAK,CAAC+B;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/D1B,KAAK,CAACgC,eAAe,IAAIhC,KAAK,CAACgC,eAAe,MAAMhC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAAC8B,KAAK,CAAC,iBAClFnE,OAAA;oBAAMiC,SAAS,EAAC,YAAY;oBAAAC,QAAA,GACzBxB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAED,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHC,WAAW,GAAI,aAAY2B,KAAK,CAACgC,eAAgB,EAAC,GAAI,SAAQhC,KAAK,CAACgC,eAAgB,EAAC,GACrF,QAAOhC,KAAK,CAACgC,eAAgB,EAAC;kBAAA;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL1D,iBAAiB,KAAKiC,KAAK,iBAC1BtC,OAAA;cAAKiC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GACjCG,KAAK,CAACiC,QAAQ,gBACbtE,OAAA;gBACEuE,GAAG,EAAGA,GAAG,IAAK5D,WAAW,CAAC4D,GAAG,CAAE;gBAC/BC,QAAQ;gBACRC,QAAQ;gBACRC,WAAW;gBACXC,OAAO,EAAC,UAAU;gBAClBC,KAAK,EAAC,MAAM;gBACZC,MAAM,EAAC,MAAM;gBACbC,MAAM,EAAEvE,eAAe,CAAC8B,KAAK,CAAE;gBAC/B0C,KAAK,EAAE;kBACLH,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,MAAM;kBACvBC,SAAS,EAAE;gBACb,CAAE;gBACFnC,OAAO,EAAGC,CAAC,IAAK;kBACdnC,aAAa,CAAE,yBAAwByB,KAAK,CAACO,KAAM,mCAAkC,CAAC;gBACxF,CAAE;gBACFsC,SAAS,EAAEA,CAAA,KAAM;kBACftE,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACFuE,WAAW,EAAC,WAAW;gBAAAjD,QAAA,gBAEvBlC,OAAA;kBAAQ0C,GAAG,EAAEL,KAAK,CAAC+C,cAAc,IAAI/C,KAAK,CAACiC,QAAS;kBAACe,IAAI,EAAC;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACvE1B,KAAK,CAAC4B,SAAS,IAAI5B,KAAK,CAAC4B,SAAS,CAAC9B,MAAM,GAAG,CAAC,IAAIE,KAAK,CAAC4B,SAAS,CAAC7B,GAAG,CAAC,CAACkD,QAAQ,EAAEC,QAAQ,kBACvFvF,OAAA;kBAEEwF,IAAI,EAAC,WAAW;kBAChB9C,GAAG,EAAE4C,QAAQ,CAAC7B,GAAI;kBAClBgC,OAAO,EAAEH,QAAQ,CAACI,QAAS;kBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;kBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIP,QAAQ,KAAK;gBAAE,GALxC,GAAED,QAAQ,CAACI,QAAS,IAAGH,QAAS,EAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMxC,CACF,CAAC,EAAC,8CAEL;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,GACN1B,KAAK,CAACW,OAAO,gBACfhD,OAAA;gBACE0C,GAAG,EAAG,iCAAgCL,KAAK,CAACW,OAAQ,mBAAmB;gBACvEJ,KAAK,EAAEP,KAAK,CAACO,KAAM;gBACnBmD,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACfjB,KAAK,EAAE;kBACLH,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE;gBACV;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,gBAEV/D,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpC/D,OAAA;kBAAAkC,QAAA,EAAI;gBAAiB;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1B/D,OAAA;kBAAAkC,QAAA,EAAIrB,UAAU,IAAI;gBAA4C;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN,eAGD/D,OAAA;gBAAKiC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/ClC,OAAA;kBACEiC,SAAS,EAAG,4BAA2BlB,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAC1E0B,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;kBAAAmB,QAAA,GACvD,yBACc,EAACjB,uBAAuB,CAAC,CAAC,CAACkB,MAAM,EAAC,GACjD;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/D,OAAA;kBAAQiC,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAE7C;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/D,OAAA;kBACEiC,SAAS,EAAC,oCAAoC;kBAC9CQ,OAAO,EAAEA,CAAA,KAAM3B,oBAAoB,CAAC,IAAI,CAAE;kBAAAoB,QAAA,EAC3C;gBAED;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGL,CAAChD,gBAAgB,IAAIW,QAAQ,KAAKrB,iBAAiB,KAAKiC,KAAK,iBAC5DtC,OAAA;gBAAKiC,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvClC,OAAA;kBAAKiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eACtClC,OAAA;oBAAAkC,QAAA,GAAOjB,uBAAuB,CAAC,CAAC,CAACkB,MAAM,EAAC,WAAS;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eAGN/D,OAAA;kBAAKiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpClC,OAAA;oBAAKiC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACpC,CAAAV,IAAI,aAAJA,IAAI,wBAAAe,UAAA,GAAJf,IAAI,CAAE0E,IAAI,cAAA3D,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAY4D,MAAM,CAAC,CAAC,CAAC,cAAA3D,iBAAA,uBAArBA,iBAAA,CAAuB4D,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACN/D,OAAA;oBAAK+E,KAAK,EAAE;sBAAEsB,IAAI,EAAE;oBAAE,CAAE;oBAAAnE,QAAA,gBACtBlC,OAAA;sBACEiC,SAAS,EAAC,6BAA6B;sBACvCqE,KAAK,EAAEpF,UAAW;sBAClBqF,QAAQ,EAAGxD,CAAC,IAAK5B,aAAa,CAAC4B,CAAC,CAACO,MAAM,CAACgD,KAAK,CAAE;sBAC/CE,WAAW,EAAE9F,WAAW,GAAG,sBAAsB,GAAG,kBAAmB;sBACvE+F,IAAI,EAAC,GAAG;sBACR1B,KAAK,EAAE;wBACL2B,SAAS,EAAE,MAAM;wBACjBC,MAAM,EAAE,MAAM;wBACdC,QAAQ,EAAE;sBACZ,CAAE;sBACFC,OAAO,EAAG9D,CAAC,IAAK;wBACdA,CAAC,CAACO,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAG,MAAM;wBAC9B9B,CAAC,CAACO,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAG9B,CAAC,CAACO,MAAM,CAACwD,YAAY,GAAG,IAAI;sBACtD;oBAAE;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACD7C,UAAU,CAAC6F,IAAI,CAAC,CAAC,iBAChB/G,OAAA;sBAAKiC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtClC,OAAA;wBACEiC,SAAS,EAAC,4BAA4B;wBACtCQ,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAAC,EAAE,CAAE;wBAAAe,QAAA,EAEhCxB,WAAW,GAAG,QAAQ,GAAG;sBAAQ;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACT/D,OAAA;wBACEiC,SAAS,EAAC,4BAA4B;wBACtCQ,OAAO,EAAErB,gBAAiB;wBAC1B4F,QAAQ,EAAE,CAAC9F,UAAU,CAAC6F,IAAI,CAAC,CAAE;wBAAA7E,QAAA,EAE5BxB,WAAW,GAAG,MAAM,GAAG;sBAAS;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/D,OAAA;kBAAKiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCjB,uBAAuB,CAAC,CAAC,CAACkB,MAAM,KAAK,CAAC,gBACrCnC,OAAA;oBAAK+E,KAAK,EAAE;sBAAEkC,SAAS,EAAE,QAAQ;sBAAEC,OAAO,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAU,CAAE;oBAAAjF,QAAA,gBACvElC,OAAA;sBAAK+E,KAAK,EAAE;wBAAEqC,QAAQ,EAAE,MAAM;wBAAEC,YAAY,EAAE;sBAAO,CAAE;sBAAAnF,QAAA,EAAC;oBAAE;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChE/D,OAAA;sBAAAkC,QAAA,EAAIxB,WAAW,GAAG,0DAA0D,GAAG;oBAAuD;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxI,CAAC,GAEN9C,uBAAuB,CAAC,CAAC,CAACmB,GAAG,CAAEkF,OAAO;oBAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;oBAAA,oBACpC1H,OAAA;sBAAqCiC,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9DlC,OAAA;wBAAKiC,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EACpCoF,OAAO,CAACK,MAAM,MAAAJ,eAAA,GAAID,OAAO,CAACM,MAAM,cAAAL,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBpB,MAAM,CAAC,CAAC,CAAC,cAAAqB,qBAAA,uBAAzBA,qBAAA,CAA2BpB,WAAW,CAAC,CAAC,KAAI;sBAAG;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACN/D,OAAA;wBAAKiC,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtClC,OAAA;0BAAKiC,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBACrClC,OAAA;4BAAMiC,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAEoF,OAAO,CAACM;0BAAM;4BAAAhE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAC/D,CAACuD,OAAO,CAACO,QAAQ,KAAK,OAAO,IAAIP,OAAO,CAACQ,OAAO,kBAC/C9H,OAAA,CAACF,UAAU;4BAACiF,KAAK,EAAE;8BAAEoC,KAAK,EAAE,SAAS;8BAAEC,QAAQ,EAAE,MAAM;8BAAEW,UAAU,EAAE;4BAAM,CAAE;4BAACnF,KAAK,EAAC;0BAAgB;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CACvG,eACD/D,OAAA;4BAAMiC,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EACnCX,aAAa,CAAC+F,OAAO,CAACU,SAAS,IAAIV,OAAO,CAACW,SAAS;0BAAC;4BAAArE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN/D,OAAA;0BAAKiC,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,EAClCoF,OAAO,CAACY;wBAAI;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACN/D,OAAA;0BAAKiC,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,gBACtClC,OAAA;4BACEyC,OAAO,EAAEA,CAAA,KAAMpB,iBAAiB,CAACiG,OAAO,CAACa,GAAG,IAAIb,OAAO,CAACc,EAAE,CAAE;4BAC5DnG,SAAS,EAAG,0BAAyB,CAAAwF,gBAAA,GAAAH,OAAO,CAACe,OAAO,cAAAZ,gBAAA,eAAfA,gBAAA,CAAiBxE,QAAQ,CAACzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;4BAAAjG,QAAA,gBAE3FlC,OAAA;8BAAAkC,QAAA,EAAO,CAAAwF,iBAAA,GAAAJ,OAAO,CAACe,OAAO,cAAAX,iBAAA,eAAfA,iBAAA,CAAiBzE,QAAQ,CAACzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,GAAG,CAAC,GAAG,IAAI,GAAG;4BAAI;8BAAAvE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAChEuD,OAAO,CAACgB,KAAK,GAAG,CAAC,iBAAItI,OAAA;8BAAAkC,QAAA,EAAOoF,OAAO,CAACgB;4BAAK;8BAAA1E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5C,CAAC,eACT/D,OAAA;4BAAQiC,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EACvCxB,WAAW,GAAG,MAAM,GAAG;0BAAO;4BAAAkD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,EACRuD,OAAO,CAAC9F,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,GAAG,kBACzBnI,OAAA;4BACEiC,SAAS,EAAC,wBAAwB;4BAClCQ,OAAO,EAAEA,CAAA,KAAM;8BACb,IAAIb,MAAM,CAAC2G,OAAO,CAAC7H,WAAW,GAAG,wCAAwC,GAAG,+CAA+C,CAAC,EAAE;gCAC5HY,mBAAmB,CAACgG,OAAO,CAACa,GAAG,IAAIb,OAAO,CAACc,EAAE,CAAC;8BAChD;4BACF,CAAE;4BAAAlG,QAAA,EAEDxB,WAAW,GAAG,MAAM,GAAG;0BAAQ;4BAAAkD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,GAzCEuD,OAAO,CAACa,GAAG,IAAIb,OAAO,CAACc,EAAE;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0C9B,CAAC;kBAAA,CACP;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAlQazB,KAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmQV,CAAC;MAAA,CAClB;IAAC,gBACF,CAAC,gBAEH/D,OAAA;MAAKiC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlC,OAAA,CAACJ,eAAe;QAACqC,SAAS,EAAC;MAAY;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1C/D,OAAA;QAAAkC,QAAA,EAAKxB,WAAW,GAAG,6BAA6B,GAAG;MAAiB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1E/D,OAAA;QAAAkC,QAAA,EAAIxB,WAAW,GAAG,kEAAkE,GAAG;MAA4D;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxJ/D,OAAA;QAAGiC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAExB,WAAW,GAAG,yCAAyC,GAAG;MAA6C;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpI;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CAxTItB,SAAS;AAAAqI,EAAA,GAATrI,SAAS;AA0Tf,eAAeA,SAAS;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}