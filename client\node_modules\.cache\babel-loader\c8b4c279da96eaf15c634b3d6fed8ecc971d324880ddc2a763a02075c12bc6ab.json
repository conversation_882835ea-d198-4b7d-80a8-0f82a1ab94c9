{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { TbMenu2, TbX, TbHome, TbBrain, TbBook, TbVideo, TbRobot, TbChartLine, TbTrophy, TbUser, TbMessageCircle, TbCreditCard, TbLogout, TbChevronRight, TbStar } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n  const navigationItems = [{\n    title: isKiswahili ? 'Kituo' : 'Hub',\n    description: isKiswahili ? 'Dashibodi kuu' : 'Main dashboard',\n    icon: TbHome,\n    path: '/user/hub',\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    title: isKiswahili ? 'Wasifu' : 'Profile',\n    description: isKiswahili ? 'Simamia akaunti' : 'Manage account',\n    icon: TbUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600'\n  }, {\n    title: isKiswahili ? 'Uanachama' : 'Subscription',\n    description: isKiswahili ? 'Simamia mpango wako' : 'Manage your plan',\n    icon: TbCreditCard,\n    path: '/subscription',\n    color: 'from-purple-500 to-purple-600'\n  }, {\n    title: isKiswahili ? 'Ondoka' : 'Logout',\n    description: isKiswahili ? 'Toka kwenye akaunti' : 'Sign out of account',\n    icon: TbLogout,\n    path: 'logout',\n    color: 'from-red-500 to-red-600'\n  }];\n  const handleNavigation = path => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n  const isActivePath = path => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      title: isOpen ? \"Close Menu\" : \"Open Menu\",\n      style: {\n        position: 'fixed',\n        zIndex: 40,\n        top: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '24px' : '28px',\n        left: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '16px' : '24px',\n        padding: window.innerWidth <= 480 ? '6px' : window.innerWidth <= 768 ? '8px' : '10px',\n        width: window.innerWidth <= 480 ? '32px' : window.innerWidth <= 768 ? '36px' : '40px',\n        height: window.innerWidth <= 480 ? '32px' : window.innerWidth <= 768 ? '36px' : '40px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'rgba(0, 0, 0, 0.9)',\n        border: '2px solid #ffffff',\n        borderRadius: '10px',\n        cursor: 'pointer',\n        boxShadow: '0 6px 20px rgba(0, 0, 0, 0.5), 0 3px 10px rgba(0, 0, 0, 0.4)',\n        backdropFilter: 'blur(15px)',\n        transition: 'all 0.3s ease',\n        transform: 'scale(1)'\n      },\n      onMouseEnter: e => {\n        e.target.style.transform = 'scale(1.05)';\n      },\n      onMouseLeave: e => {\n        e.target.style.transform = 'scale(1)';\n      },\n      children: isOpen ?\n      /*#__PURE__*/\n      // White X icon for close - Pure inline CSS\n      _jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px',\n          height: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            width: '100%',\n            height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n            top: '50%',\n            left: '0',\n            transform: 'translateY(-50%) rotate(45deg)',\n            backgroundColor: '#ffffff',\n            borderRadius: '2px',\n            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n            transition: 'all 0.2s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            width: '100%',\n            height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n            top: '50%',\n            left: '0',\n            transform: 'translateY(-50%) rotate(-45deg)',\n            backgroundColor: '#ffffff',\n            borderRadius: '2px',\n            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n            transition: 'all 0.2s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 3 thick white lines for hamburger menu - Pure inline CSS\n      _jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'space-between',\n          width: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px',\n          height: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '14px' : '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n            backgroundColor: '#ffffff',\n            borderRadius: '2px',\n            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n            transition: 'all 0.2s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n            backgroundColor: '#ffffff',\n            borderRadius: '2px',\n            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n            transition: 'all 0.2s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n            backgroundColor: '#ffffff',\n            borderRadius: '2px',\n            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n            transition: 'all 0.2s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(false),\n      className: \"sidebar-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-panel fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\",\n      style: {\n        width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n        maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\",\n        style: {\n          padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          className: \"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\",\n          style: {\n            top: window.innerWidth <= 768 ? '8px' : '12px',\n            right: window.innerWidth <= 768 ? '8px' : '12px',\n            padding: window.innerWidth <= 768 ? '6px' : '8px'\n          },\n          title: \"Close Menu\",\n          children: /*#__PURE__*/_jsxDEV(TbX, {\n            className: \"text-white\",\n            style: {\n              width: window.innerWidth <= 768 ? '16px' : '20px',\n              height: window.innerWidth <= 768 ? '16px' : '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          style: {\n            paddingRight: window.innerWidth <= 768 ? '32px' : '48px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"font-bold mb-2\",\n            style: {\n              fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n            },\n            children: \"Navigation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-200\",\n            style: {\n              fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n            },\n            children: \"Choose your destination\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        style: {\n          padding: window.innerWidth <= 768 ? '12px' : '16px',\n          gap: window.innerWidth <= 768 ? '8px' : '12px',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: navigationItems.map((item, index) => {\n          const IconComponent = item.icon;\n          const isActive = item.path !== 'logout' && isActivePath(item.path);\n          const isLogout = item.path === 'logout';\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"sidebar-nav-item\",\n            onClick: () => handleNavigation(item.path),\n            className: `w-full flex items-center justify-between rounded-xl transition-all duration-200 ${isActive ? 'bg-blue-50 border-2 border-blue-200 shadow-md' : isLogout ? 'hover:bg-red-50 border-2 border-transparent' : 'hover:bg-gray-50 border-2 border-transparent'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n              marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              style: {\n                gap: window.innerWidth <= 768 ? '8px' : '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`,\n                style: {\n                  width: window.innerWidth <= 768 ? '32px' : '40px',\n                  height: window.innerWidth <= 768 ? '32px' : '40px'\n                },\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                  className: \"text-white\",\n                  style: {\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `font-medium ${isActive ? 'text-blue-200' : 'text-white'}`,\n                  style: {\n                    fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                  },\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `${isActive ? 'text-blue-100' : 'text-gray-200'}`,\n                  style: {\n                    fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n              className: `${isActive ? 'text-blue-200' : isLogout ? 'text-red-200' : 'text-gray-300'}`,\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 21\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernSidebar, \"g4hbu0qzRY22X/Pfr9URFTLYUC8=\", false, function () {\n  return [useNavigate, useLocation, useSelector, useLanguage];\n});\n_c = ModernSidebar;\nexport default ModernSidebar;\n\n// Add CSS animations to replace Framer Motion\nconst sidebarStyles = `\n.sidebar-backdrop {\n  animation: backdropFadeIn 0.3s ease-out;\n}\n\n.sidebar-panel {\n  animation: sidebarSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 280px;\n}\n\n.sidebar-nav-item {\n  animation: navItemSlideIn 0.3s ease-out;\n  animation-fill-mode: both;\n}\n\n.sidebar-nav-item:nth-child(1) { animation-delay: 0.05s; }\n.sidebar-nav-item:nth-child(2) { animation-delay: 0.1s; }\n.sidebar-nav-item:nth-child(3) { animation-delay: 0.15s; }\n.sidebar-nav-item:nth-child(4) { animation-delay: 0.2s; }\n.sidebar-nav-item:nth-child(5) { animation-delay: 0.25s; }\n.sidebar-nav-item:nth-child(6) { animation-delay: 0.3s; }\n.sidebar-nav-item:nth-child(7) { animation-delay: 0.35s; }\n.sidebar-nav-item:nth-child(8) { animation-delay: 0.4s; }\n\n@keyframes backdropFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes sidebarSlideIn {\n  from {\n    transform: translateX(-400px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes navItemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .sidebar-panel {\n    width: 260px;\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('sidebar-styles');\n  if (!existingStyle) {\n    const styleSheet = document.createElement('style');\n    styleSheet.id = 'sidebar-styles';\n    styleSheet.textContent = sidebarStyles;\n    document.head.appendChild(styleSheet);\n  }\n}\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "useSelector", "useLanguage", "TbMenu2", "TbX", "TbHome", "TbBrain", "TbBook", "TbVideo", "TbRobot", "TbChartLine", "TbTrophy", "TbUser", "TbMessageCircle", "TbCreditCard", "TbLogout", "TbChevronRight", "TbStar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "_s", "isOpen", "setIsOpen", "navigate", "location", "user", "state", "t", "isKiswahili", "handleKeyDown", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "navigationItems", "title", "description", "icon", "path", "color", "handleNavigation", "handleLogout", "localStorage", "removeItem", "window", "href", "isActivePath", "pathname", "startsWith", "children", "onClick", "position", "zIndex", "top", "innerWidth", "left", "padding", "width", "height", "display", "alignItems", "justifyContent", "backgroundColor", "border", "borderRadius", "cursor", "boxShadow", "<PERSON><PERSON>ilter", "transition", "transform", "onMouseEnter", "e", "target", "onMouseLeave", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "className", "max<PERSON><PERSON><PERSON>", "right", "paddingRight", "fontSize", "gap", "map", "item", "index", "IconComponent", "isActive", "isLogout", "marginBottom", "_c", "sidebarStyles", "existingStyle", "getElementById", "styleSheet", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbVideo,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight,\n  TbStar\n} from 'react-icons/tb';\n\nconst ModernSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Kituo' : 'Hub',\n      description: isKiswahili ? 'Dashibodi kuu' : 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: isKiswahili ? 'Wasifu' : 'Profile',\n      description: isKiswahili ? 'Simamia akaunti' : 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600'\n    },\n    {\n      title: isKiswahili ? 'Uanachama' : 'Subscription',\n      description: isKiswahili ? 'Simamia mpango wako' : 'Manage your plan',\n      icon: TbCreditCard,\n      path: '/subscription',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: isKiswahili ? 'Ondoka' : 'Logout',\n      description: isKiswahili ? 'Toka kwenye akaunti' : 'Sign out of account',\n      icon: TbLogout,\n      path: 'logout',\n      color: 'from-red-500 to-red-600'\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Toggle Button - Responsive */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        title={isOpen ? \"Close Menu\" : \"Open Menu\"}\n        style={{\n          position: 'fixed',\n          zIndex: 40,\n          top: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '24px' : '28px',\n          left: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '16px' : '24px',\n          padding: window.innerWidth <= 480 ? '6px' : window.innerWidth <= 768 ? '8px' : '10px',\n          width: window.innerWidth <= 480 ? '32px' : window.innerWidth <= 768 ? '36px' : '40px',\n          height: window.innerWidth <= 480 ? '32px' : window.innerWidth <= 768 ? '36px' : '40px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: 'rgba(0, 0, 0, 0.9)',\n          border: '2px solid #ffffff',\n          borderRadius: '10px',\n          cursor: 'pointer',\n          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.5), 0 3px 10px rgba(0, 0, 0, 0.4)',\n          backdropFilter: 'blur(15px)',\n          transition: 'all 0.3s ease',\n          transform: 'scale(1)',\n        }}\n        onMouseEnter={(e) => {\n          e.target.style.transform = 'scale(1.05)';\n        }}\n        onMouseLeave={(e) => {\n          e.target.style.transform = 'scale(1)';\n        }}\n      >\n        {isOpen ? (\n          // White X icon for close - Pure inline CSS\n          <div\n            style={{\n              position: 'relative',\n              width: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px',\n              height: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px'\n            }}\n          >\n            <div\n              style={{\n                position: 'absolute',\n                width: '100%',\n                height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                top: '50%',\n                left: '0',\n                transform: 'translateY(-50%) rotate(45deg)',\n                backgroundColor: '#ffffff',\n                borderRadius: '2px',\n                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n                transition: 'all 0.2s ease'\n              }}\n            />\n            <div\n              style={{\n                position: 'absolute',\n                width: '100%',\n                height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                top: '50%',\n                left: '0',\n                transform: 'translateY(-50%) rotate(-45deg)',\n                backgroundColor: '#ffffff',\n                borderRadius: '2px',\n                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n                transition: 'all 0.2s ease'\n              }}\n            />\n          </div>\n        ) : (\n          // 3 thick white lines for hamburger menu - Pure inline CSS\n          <div\n            style={{\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'space-between',\n              width: window.innerWidth <= 480 ? '16px' : window.innerWidth <= 768 ? '18px' : '20px',\n              height: window.innerWidth <= 480 ? '12px' : window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n          >\n            <div\n              style={{\n                width: '100%',\n                height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                backgroundColor: '#ffffff',\n                borderRadius: '2px',\n                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n                transition: 'all 0.2s ease'\n              }}\n            />\n            <div\n              style={{\n                width: '100%',\n                height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                backgroundColor: '#ffffff',\n                borderRadius: '2px',\n                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n                transition: 'all 0.2s ease'\n              }}\n            />\n            <div\n              style={{\n                width: '100%',\n                height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                backgroundColor: '#ffffff',\n                borderRadius: '2px',\n                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.5)',\n                transition: 'all 0.2s ease'\n              }}\n            />\n          </div>\n        )}\n      </button>\n\n      {/* Backdrop */}\n      {isOpen && (\n        <div\n          onClick={() => setIsOpen(false)}\n          className=\"sidebar-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n        />\n      )}\n\n      {/* Sidebar - Responsive */}\n      {isOpen && (\n        <div\n          className=\"sidebar-panel fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\"\n            style={{\n              width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n              maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n            }}\n          >\n            {/* Header - Responsive */}\n            <div\n              className=\"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n              }}\n            >\n              {/* Close Button */}\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\"\n                style={{\n                  top: window.innerWidth <= 768 ? '8px' : '12px',\n                  right: window.innerWidth <= 768 ? '8px' : '12px',\n                  padding: window.innerWidth <= 768 ? '6px' : '8px'\n                }}\n                title=\"Close Menu\"\n              >\n                <TbX\n                  className=\"text-white\"\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n\n              <div\n                className=\"text-center\"\n                style={{\n                  paddingRight: window.innerWidth <= 768 ? '32px' : '48px'\n                }}\n              >\n                <h1\n                  className=\"font-bold mb-2\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n                  }}\n                >\n                  Navigation\n                </h1>\n                <p\n                  className=\"text-blue-200\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                  }}\n                >\n                  Choose your destination\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation - Responsive */}\n            <div\n              className=\"flex-1 overflow-y-auto\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px' : '16px',\n                gap: window.innerWidth <= 768 ? '8px' : '12px',\n                display: 'flex',\n                flexDirection: 'column'\n              }}\n            >\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = item.path !== 'logout' && isActivePath(item.path);\n                const isLogout = item.path === 'logout';\n\n                return (\n                  <button\n                    key={item.path}\n                    className=\"sidebar-nav-item\"\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : isLogout\n                        ? 'hover:bg-red-50 border-2 border-transparent'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                    style={{\n                      padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n                      marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n                    }}\n                  >\n                    <div\n                      className=\"flex items-center\"\n                      style={{\n                        gap: window.innerWidth <= 768 ? '8px' : '12px'\n                      }}\n                    >\n                      <div\n                        className={`rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}\n                        style={{\n                          width: window.innerWidth <= 768 ? '32px' : '40px',\n                          height: window.innerWidth <= 768 ? '32px' : '40px'\n                        }}\n                      >\n                        <IconComponent\n                          className=\"text-white\"\n                          style={{\n                            width: window.innerWidth <= 768 ? '16px' : '20px',\n                            height: window.innerWidth <= 768 ? '16px' : '20px'\n                          }}\n                        />\n                      </div>\n                      <div className=\"text-left flex-1\">\n                        <p\n                          className={`font-medium ${\n                            isActive\n                              ? 'text-blue-200'\n                              : 'text-white'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                          }}\n                        >\n                          {item.title}\n                        </p>\n                        <p\n                          className={`${\n                            isActive\n                              ? 'text-blue-100'\n                              : 'text-gray-200'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                          }}\n                        >\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight\n                      className={`${\n                        isActive\n                          ? 'text-blue-200'\n                          : isLogout\n                          ? 'text-red-200'\n                          : 'text-gray-300'\n                      }`}\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n        )}\n    </>\n  );\n};\n\nexport default ModernSidebar;\n\n// Add CSS animations to replace Framer Motion\nconst sidebarStyles = `\n.sidebar-backdrop {\n  animation: backdropFadeIn 0.3s ease-out;\n}\n\n.sidebar-panel {\n  animation: sidebarSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 280px;\n}\n\n.sidebar-nav-item {\n  animation: navItemSlideIn 0.3s ease-out;\n  animation-fill-mode: both;\n}\n\n.sidebar-nav-item:nth-child(1) { animation-delay: 0.05s; }\n.sidebar-nav-item:nth-child(2) { animation-delay: 0.1s; }\n.sidebar-nav-item:nth-child(3) { animation-delay: 0.15s; }\n.sidebar-nav-item:nth-child(4) { animation-delay: 0.2s; }\n.sidebar-nav-item:nth-child(5) { animation-delay: 0.25s; }\n.sidebar-nav-item:nth-child(6) { animation-delay: 0.3s; }\n.sidebar-nav-item:nth-child(7) { animation-delay: 0.35s; }\n.sidebar-nav-item:nth-child(8) { animation-delay: 0.4s; }\n\n@keyframes backdropFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes sidebarSlideIn {\n  from {\n    transform: translateX(-400px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes navItemSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .sidebar-panel {\n    width: 260px;\n  }\n}\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('sidebar-styles');\n  if (!existingStyle) {\n    const styleSheet = document.createElement('style');\n    styleSheet.id = 'sidebar-styles';\n    styleSheet.textContent = sidebarStyles;\n    document.head.appendChild(styleSheet);\n  }\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SACEC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM6B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAG7B,WAAW,CAAC,CAAC;;EAExC;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIV,MAAM,EAAE;QACpCC,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVW,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;MACnD;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;MACtDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EAEZ,MAAMiB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAEX,WAAW,GAAG,OAAO,GAAG,KAAK;IACpCY,WAAW,EAAEZ,WAAW,GAAG,eAAe,GAAG,gBAAgB;IAC7Da,IAAI,EAAEvC,MAAM;IACZwC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAEX,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCY,WAAW,EAAEZ,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;IAC/Da,IAAI,EAAEhC,MAAM;IACZiC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAEX,WAAW,GAAG,WAAW,GAAG,cAAc;IACjDY,WAAW,EAAEZ,WAAW,GAAG,qBAAqB,GAAG,kBAAkB;IACrEa,IAAI,EAAE9B,YAAY;IAClB+B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAEX,WAAW,GAAG,QAAQ,GAAG,QAAQ;IACxCY,WAAW,EAAEZ,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEa,IAAI,EAAE7B,QAAQ;IACd8B,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAIF,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBG,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLtB,QAAQ,CAACmB,IAAI,CAAC;IAChB;IACApB,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/B;IACAC,MAAM,CAACxB,QAAQ,CAACyB,IAAI,GAAG,GAAG;EAC5B,CAAC;EAED,MAAMC,YAAY,GAAIR,IAAI,IAAK;IAC7B,OAAOlB,QAAQ,CAAC2B,QAAQ,KAAKT,IAAI,IAAIlB,QAAQ,CAAC2B,QAAQ,CAACC,UAAU,CAACV,IAAI,CAAC;EACzE,CAAC;EAED,oBACE1B,OAAA,CAAAE,SAAA;IAAAmC,QAAA,gBAEErC,OAAA;MACEsC,OAAO,EAAEA,CAAA,KAAMhC,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCkB,KAAK,EAAElB,MAAM,GAAG,YAAY,GAAG,WAAY;MAC3Cc,KAAK,EAAE;QACLoB,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,EAAE;QACVC,GAAG,EAAET,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;QACnFC,IAAI,EAAEX,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;QACpFE,OAAO,EAAEZ,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;QACrFG,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;QACrFI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;QACtFK,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,oBAAoB;QACrCC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,8DAA8D;QACzEC,cAAc,EAAE,YAAY;QAC5BC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MACFC,YAAY,EAAGC,CAAC,IAAK;QACnBA,CAAC,CAACC,MAAM,CAACzC,KAAK,CAACsC,SAAS,GAAG,aAAa;MAC1C,CAAE;MACFI,YAAY,EAAGF,CAAC,IAAK;QACnBA,CAAC,CAACC,MAAM,CAACzC,KAAK,CAACsC,SAAS,GAAG,UAAU;MACvC,CAAE;MAAApB,QAAA,EAEDhC,MAAM;MAAA;MACL;MACAL,OAAA;QACEmB,KAAK,EAAE;UACLoB,QAAQ,EAAE,UAAU;UACpBM,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACrFI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAClF,CAAE;QAAAL,QAAA,gBAEFrC,OAAA;UACEmB,KAAK,EAAE;YACLoB,QAAQ,EAAE,UAAU;YACpBM,KAAK,EAAE,MAAM;YACbC,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;YACrFD,GAAG,EAAE,KAAK;YACVE,IAAI,EAAE,GAAG;YACTc,SAAS,EAAE,gCAAgC;YAC3CP,eAAe,EAAE,SAAS;YAC1BE,YAAY,EAAE,KAAK;YACnBE,SAAS,EAAE,8BAA8B;YACzCE,UAAU,EAAE;UACd;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjE,OAAA;UACEmB,KAAK,EAAE;YACLoB,QAAQ,EAAE,UAAU;YACpBM,KAAK,EAAE,MAAM;YACbC,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;YACrFD,GAAG,EAAE,KAAK;YACVE,IAAI,EAAE,GAAG;YACTc,SAAS,EAAE,iCAAiC;YAC5CP,eAAe,EAAE,SAAS;YAC1BE,YAAY,EAAE,KAAK;YACnBE,SAAS,EAAE,8BAA8B;YACzCE,UAAU,EAAE;UACd;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;MAAA;MAEN;MACAjE,OAAA;QACEmB,KAAK,EAAE;UACL4B,OAAO,EAAE,MAAM;UACfmB,aAAa,EAAE,QAAQ;UACvBjB,cAAc,EAAE,eAAe;UAC/BJ,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACrFI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAClF,CAAE;QAAAL,QAAA,gBAEFrC,OAAA;UACEmB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;YACrFQ,eAAe,EAAE,SAAS;YAC1BE,YAAY,EAAE,KAAK;YACnBE,SAAS,EAAE,8BAA8B;YACzCE,UAAU,EAAE;UACd;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjE,OAAA;UACEmB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;YACrFQ,eAAe,EAAE,SAAS;YAC1BE,YAAY,EAAE,KAAK;YACnBE,SAAS,EAAE,8BAA8B;YACzCE,UAAU,EAAE;UACd;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFjE,OAAA;UACEmB,KAAK,EAAE;YACL0B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGV,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;YACrFQ,eAAe,EAAE,SAAS;YAC1BE,YAAY,EAAE,KAAK;YACnBE,SAAS,EAAE,8BAA8B;YACzCE,UAAU,EAAE;UACd;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGR5D,MAAM,iBACLL,OAAA;MACEsC,OAAO,EAAEA,CAAA,KAAMhC,SAAS,CAAC,KAAK,CAAE;MAChC6D,SAAS,EAAC;IAAkE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CACF,EAGA5D,MAAM,iBACLL,OAAA;MACEmE,SAAS,EAAC,gFAAgF;MACxFhD,KAAK,EAAE;QACL0B,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGV,MAAM,CAACU,UAAU,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO;QACxF0B,QAAQ,EAAEpC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;MACjD,CAAE;MAAAL,QAAA,gBAGFrC,OAAA;QACEmE,SAAS,EAAC,gEAAgE;QAC1EhD,KAAK,EAAE;UACLyB,OAAO,EAAEZ,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG;QACpD,CAAE;QAAAL,QAAA,gBAGFrC,OAAA;UACEsC,OAAO,EAAEA,CAAA,KAAMhC,SAAS,CAAC,KAAK,CAAE;UAChC6D,SAAS,EAAC,kFAAkF;UAC5FhD,KAAK,EAAE;YACLsB,GAAG,EAAET,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;YAC9C2B,KAAK,EAAErC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;YAChDE,OAAO,EAAEZ,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;UAC9C,CAAE;UACFnB,KAAK,EAAC,YAAY;UAAAc,QAAA,eAElBrC,OAAA,CAACf,GAAG;YACFkF,SAAS,EAAC,YAAY;YACtBhD,KAAK,EAAE;cACL0B,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC9C;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAETjE,OAAA;UACEmE,SAAS,EAAC,aAAa;UACvBhD,KAAK,EAAE;YACLmD,YAAY,EAAEtC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UACpD,CAAE;UAAAL,QAAA,gBAEFrC,OAAA;YACEmE,SAAS,EAAC,gBAAgB;YAC1BhD,KAAK,EAAE;cACLoD,QAAQ,EAAEvC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAChD,CAAE;YAAAL,QAAA,EACH;UAED;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjE,OAAA;YACEmE,SAAS,EAAC,eAAe;YACzBhD,KAAK,EAAE;cACLoD,QAAQ,EAAEvC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAChD,CAAE;YAAAL,QAAA,EACH;UAED;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QACEmE,SAAS,EAAC,wBAAwB;QAClChD,KAAK,EAAE;UACLyB,OAAO,EAAEZ,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACnD8B,GAAG,EAAExC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;UAC9CK,OAAO,EAAE,MAAM;UACfmB,aAAa,EAAE;QACjB,CAAE;QAAA7B,QAAA,EAEDf,eAAe,CAACmD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACpC,MAAMC,aAAa,GAAGF,IAAI,CAACjD,IAAI;UAC/B,MAAMoD,QAAQ,GAAGH,IAAI,CAAChD,IAAI,KAAK,QAAQ,IAAIQ,YAAY,CAACwC,IAAI,CAAChD,IAAI,CAAC;UAClE,MAAMoD,QAAQ,GAAGJ,IAAI,CAAChD,IAAI,KAAK,QAAQ;UAEvC,oBACE1B,OAAA;YAEEmE,SAAS,EAAC,kBAAkB;YAC5B7B,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAAC8C,IAAI,CAAChD,IAAI,CAAE;YAC3CyC,SAAS,EAAG,mFACVU,QAAQ,GACJ,+CAA+C,GAC/CC,QAAQ,GACR,6CAA6C,GAC7C,8CACL,EAAE;YACH3D,KAAK,EAAE;cACLyB,OAAO,EAAEZ,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,UAAU,GAAG,WAAW;cAC5DqC,YAAY,EAAE/C,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YACnD,CAAE;YAAAL,QAAA,gBAEFrC,OAAA;cACEmE,SAAS,EAAC,mBAAmB;cAC7BhD,KAAK,EAAE;gBACLqD,GAAG,EAAExC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;cAC1C,CAAE;cAAAL,QAAA,gBAEFrC,OAAA;gBACEmE,SAAS,EAAG,+BAA8BO,IAAI,CAAC/C,KAAM,mCAAmC;gBACxFR,KAAK,EAAE;kBACL0B,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAL,QAAA,eAEFrC,OAAA,CAAC4E,aAAa;kBACZT,SAAS,EAAC,YAAY;kBACtBhD,KAAK,EAAE;oBACL0B,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;oBACjDI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAC9C;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjE,OAAA;gBAAKmE,SAAS,EAAC,kBAAkB;gBAAA9B,QAAA,gBAC/BrC,OAAA;kBACEmE,SAAS,EAAG,eACVU,QAAQ,GACJ,eAAe,GACf,YACL,EAAE;kBACH1D,KAAK,EAAE;oBACLoD,QAAQ,EAAEvC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAChD,CAAE;kBAAAL,QAAA,EAEDqC,IAAI,CAACnD;gBAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACJjE,OAAA;kBACEmE,SAAS,EAAG,GACVU,QAAQ,GACJ,eAAe,GACf,eACL,EAAE;kBACH1D,KAAK,EAAE;oBACLoD,QAAQ,EAAEvC,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAChD,CAAE;kBAAAL,QAAA,EAEDqC,IAAI,CAAClD;gBAAW;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjE,OAAA,CAACH,cAAc;cACbsE,SAAS,EAAG,GACVU,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;cACH3D,KAAK,EAAE;gBACL0B,KAAK,EAAEb,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDI,MAAM,EAAEd,MAAM,CAACU,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3EGS,IAAI,CAAChD,IAAI;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4ER,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACH,CAAC;AAEP,CAAC;AAAC7D,EAAA,CA9WID,aAAa;EAAA,QAEAvB,WAAW,EACXC,WAAW,EACXC,WAAW,EACDC,WAAW;AAAA;AAAAiG,EAAA,GALlC7E,aAAa;AAgXnB,eAAeA,aAAa;;AAE5B;AACA,MAAM8E,aAAa,GAAI;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOjE,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMkE,aAAa,GAAGlE,QAAQ,CAACmE,cAAc,CAAC,gBAAgB,CAAC;EAC/D,IAAI,CAACD,aAAa,EAAE;IAClB,MAAME,UAAU,GAAGpE,QAAQ,CAACqE,aAAa,CAAC,OAAO,CAAC;IAClDD,UAAU,CAACE,EAAE,GAAG,gBAAgB;IAChCF,UAAU,CAACG,WAAW,GAAGN,aAAa;IACtCjE,QAAQ,CAACwE,IAAI,CAACC,WAAW,CAACL,UAAU,CAAC;EACvC;AACF;AAAC,IAAAJ,EAAA;AAAAU,YAAA,CAAAV,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}