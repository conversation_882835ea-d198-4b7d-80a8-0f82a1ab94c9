{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport BrainwaveHeader from '../../../components/common/BrainwaveHeader';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\"Karibu tena\", \"Habari za asubuhi\", \"Umefika salama\", \"Karibu sana\", \"Hujambo\", \"Habari za leo\", \"Karibu kwenye mafunzo\", \"Umekuja vizuri\", \"Karibu darasani\", \"Habari za masomo\"] : [\"Welcome back\", \"Great to see you\", \"Hello there\", \"Good to have you here\", \"Nice to see you again\", \"Ready to learn\", \"Let's get started\", \"Time to shine\", \"Here we go\", \"Ready for success\"];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome(prev => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700'\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 w-1/4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSidebarOpen(!sidebarOpen),\n              className: \"group relative\",\n              style: {\n                padding: window.innerWidth <= 480 ? '8px' : window.innerWidth <= 768 ? '10px' : '12px',\n                width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%)',\n                border: '2px solid rgba(59, 130, 246, 0.3)',\n                borderRadius: '16px',\n                cursor: 'pointer',\n                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)',\n                transition: 'all 0.3s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'scale(1.05)';\n                e.currentTarget.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.3), 0 3px 12px rgba(0, 0, 0, 0.15)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'scale(1)';\n                e.currentTarget.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)';\n              },\n              title: \"Open Menu\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'space-between',\n                  width: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '20px' : '22px',\n                  height: window.innerWidth <= 480 ? '14px' : window.innerWidth <= 768 ? '16px' : '18px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                    borderRadius: '2px',\n                    boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                    borderRadius: '2px',\n                    boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '100%',\n                    height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                    borderRadius: '2px',\n                    boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                    transition: 'all 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center space-x-2 sm:space-x-3\",\n              style: {\n                maxWidth: '800px',\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none flex items-center\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rounded-full overflow-hidden border-2 border-white/20 relative inline-block\",\n                    style: {\n                      background: '#f0f0f0',\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                      width: window.innerWidth <= 480 ? '24px' : '32px',\n                      height: window.innerWidth <= 480 ? '24px' : '32px',\n                      marginLeft: '8px',\n                      verticalAlign: 'middle'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: \"/favicon.png\",\n                      alt: \"Brainwave Logo\",\n                      className: \"w-full h-full object-cover\",\n                      style: {\n                        objectFit: 'cover'\n                      },\n                      onError: e => {\n                        e.target.style.display = 'none';\n                        e.target.nextSibling.style.display = 'flex';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                      style: {\n                        display: 'none',\n                        fontSize: '12px'\n                      },\n                      children: \"\\uD83E\\uDDE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 w-1/4\",\n            children: user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [welcomeMessages[currentWelcome], \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"0OdLb4oU/m0icSuins7JDh5vSaE=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "BrainwaveHeader", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "currentWelcome", "setCurrentWelcome", "sidebarOpen", "setSidebarOpen", "inspiringQuotes", "welcomeMessages", "interval", "setInterval", "prev", "length", "clearInterval", "welcomeInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "style", "padding", "window", "innerWidth", "width", "height", "display", "alignItems", "justifyContent", "background", "border", "borderRadius", "cursor", "boxShadow", "<PERSON><PERSON>ilter", "transition", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "flexDirection", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "duration", "delay", "max<PERSON><PERSON><PERSON>", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "fontWeight", "backgroundColor", "marginLeft", "verticalAlign", "src", "alt", "objectFit", "onError", "target", "nextS<PERSON>ling", "fontSize", "ProfilePicture", "size", "showOnlineStatus", "name", "level", "class", "firstName", "marginRight", "marginTop", "map", "item", "index", "IconComponent", "tabIndex", "role", "onKeyDown", "key", "touchAction", "ModernSidebar", "isOpen", "setIsOpen", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport BrainwaveHeader from '../../../components/common/BrainwaveHeader';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\n    \"Karibu tena\",\n    \"Habari za asubuhi\",\n    \"Umefika salama\",\n    \"Karibu sana\",\n    \"Hujambo\",\n    \"Habari za leo\",\n    \"Karibu kwenye mafunzo\",\n    \"Umekuja vizuri\",\n    \"Karibu darasani\",\n    \"Habari za masomo\"\n  ] : [\n    \"Welcome back\",\n    \"Great to see you\",\n    \"Hello there\",\n    \"Good to have you here\",\n    \"Nice to see you again\",\n    \"Ready to learn\",\n    \"Let's get started\",\n    \"Time to shine\",\n    \"Here we go\",\n    \"Ready for success\"\n  ];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome((prev) => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      {/* Header with User Profile and Notification Bell - Same as Home */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\"\n      >\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Left section - Menu Button */}\n            <div className=\"flex items-center space-x-2 w-1/4\">\n              <button\n                onClick={() => setSidebarOpen(!sidebarOpen)}\n                className=\"group relative\"\n                style={{\n                  padding: window.innerWidth <= 480 ? '8px' : window.innerWidth <= 768 ? '10px' : '12px',\n                  width: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                  height: window.innerWidth <= 480 ? '40px' : window.innerWidth <= 768 ? '44px' : '48px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%)',\n                  border: '2px solid rgba(59, 130, 246, 0.3)',\n                  borderRadius: '16px',\n                  cursor: 'pointer',\n                  boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  transition: 'all 0.3s ease',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'scale(1.05)';\n                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.3), 0 3px 12px rgba(0, 0, 0, 0.15)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'scale(1)';\n                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)';\n                }}\n                title=\"Open Menu\"\n              >\n                {/* Modern Menu Icon */}\n                <div\n                  style={{\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'space-between',\n                    width: window.innerWidth <= 480 ? '18px' : window.innerWidth <= 768 ? '20px' : '22px',\n                    height: window.innerWidth <= 480 ? '14px' : window.innerWidth <= 768 ? '16px' : '18px'\n                  }}\n                >\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                      borderRadius: '2px',\n                      boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                      borderRadius: '2px',\n                      boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                  <div\n                    style={{\n                      width: '100%',\n                      height: window.innerWidth <= 480 ? '3px' : window.innerWidth <= 768 ? '3.5px' : '4px',\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981)',\n                      borderRadius: '2px',\n                      boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  />\n                </div>\n              </button>\n            </div>\n\n            {/* Center Section - Brainwave Title + Logo Aligned with Welcome Message */}\n            <div className=\"flex-1 flex justify-center\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center space-x-2 sm:space-x-3\"\n                style={{\n                  maxWidth: '800px',\n                  width: '100%'\n                }}\n              >\n                {/* Amazing Animated Brainwave Text with Logo */}\n                <div className=\"relative brainwave-container flex items-center\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none flex items-center\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects (no space) */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Official Logo - Horizontally next to \"wave\" */}\n                    <div\n                      className=\"rounded-full overflow-hidden border-2 border-white/20 relative inline-block\"\n                      style={{\n                        background: '#f0f0f0',\n                        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                        width: window.innerWidth <= 480 ? '24px' : '32px',\n                        height: window.innerWidth <= 480 ? '24px' : '32px',\n                        marginLeft: '8px',\n                        verticalAlign: 'middle'\n                      }}\n                    >\n                      <img\n                        src=\"/favicon.png\"\n                        alt=\"Brainwave Logo\"\n                        className=\"w-full h-full object-cover\"\n                        style={{ objectFit: 'cover' }}\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'flex';\n                        }}\n                      />\n                      <div\n                        className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\n                        style={{\n                          display: 'none',\n                          fontSize: '12px'\n                        }}\n                      >\n                        🧠\n                      </div>\n                    </div>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n\n            {/* Right Section - User Profile */}\n            <div className=\"flex items-center justify-end space-x-2 w-1/4\">\n              {user && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                  className=\"flex items-center space-x-2 group\"\n                >\n                  {/* Profile Picture with Online Status */}\n                  <ProfilePicture\n                    user={user}\n                    size=\"sm\"\n                    showOnlineStatus={true}\n                    style={{\n                      width: '32px',\n                      height: '32px'\n                    }}\n                  />\n\n                  {/* User Name and Class */}\n                  <div className=\"hidden sm:block text-right\">\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\n                      {user?.name || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {welcomeMessages[currentWelcome]}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n\n      {/* Modern Sidebar */}\n      <ModernSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGzB,WAAW,CAAC,CAAC;EACxC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAKrD;EACA,MAAMsC,eAAe,GAAGP,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA,MAAMQ,eAAe,GAAGR,WAAW,GAAG,CACpC,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,CACnB,GAAG,CACF,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,mBAAmB,CACpB;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMuC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCR,eAAe,CAAES,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIJ,eAAe,CAACK,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,eAAe,CAACK,MAAM,CAAC,CAAC;;EAE5B;EACA1C,SAAS,CAAC,MAAM;IACd,MAAM4C,eAAe,GAAGJ,WAAW,CAAC,MAAM;MACxCN,iBAAiB,CAAEO,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACN,eAAe,CAACI,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA3C,OAAO,CAAC4C,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAtB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAMuB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAEpB,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDqB,WAAW,EAAErB,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEsB,IAAI,EAAE5C,gBAAgB;IACtB6C,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEpB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3DqB,WAAW,EAAErB,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClFsB,IAAI,EAAE3C,MAAM;IACZ4C,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEpB,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDqB,WAAW,EAAErB,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjFsB,IAAI,EAAE/B,OAAO;IACbgC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEpB,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCqB,WAAW,EAAErB,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EsB,IAAI,EAAE1C,WAAW;IACjB2C,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEpB,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpDqB,WAAW,EAAErB,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClEsB,IAAI,EAAEpC,QAAQ;IACdqC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEpB,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvCqB,WAAW,EAAErB,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEsB,IAAI,EAAEnC,MAAM;IACZoC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAExC,UAAU;IAChByC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BlC,OAAA,CAACpB,MAAM,CAACuD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5KlC,OAAA;QAAKiC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDlC,OAAA;UAAKiC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7ElC,OAAA;YAAKiC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDlC,OAAA;cACEwC,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CqB,SAAS,EAAC,gBAAgB;cAC1BQ,KAAK,EAAE;gBACLC,OAAO,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACtFC,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACrFE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACtFG,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,UAAU,EAAE,mFAAmF;gBAC/FC,MAAM,EAAE,mCAAmC;gBAC3CC,YAAY,EAAE,MAAM;gBACpBC,MAAM,EAAE,SAAS;gBACjBC,SAAS,EAAE,kEAAkE;gBAC7EC,cAAc,EAAE,YAAY;gBAC5BC,UAAU,EAAE;cACd,CAAE;cACFC,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACmB,SAAS,GAAG,aAAa;gBAC/CF,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACa,SAAS,GAAG,oEAAoE;cACxG,CAAE;cACFO,YAAY,EAAGH,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACmB,SAAS,GAAG,UAAU;gBAC5CF,CAAC,CAACC,aAAa,CAAClB,KAAK,CAACa,SAAS,GAAG,kEAAkE;cACtG,CAAE;cACF3B,KAAK,EAAC,WAAW;cAAAO,QAAA,eAGjBlC,OAAA;gBACEyC,KAAK,EAAE;kBACLM,OAAO,EAAE,MAAM;kBACfe,aAAa,EAAE,QAAQ;kBACvBb,cAAc,EAAE,eAAe;kBAC/BJ,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACrFE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAClF,CAAE;gBAAAV,QAAA,gBAEFlC,OAAA;kBACEyC,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;oBACrFM,UAAU,EAAE,0CAA0C;oBACtDE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,mCAAmC;oBAC9CE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFlE,OAAA;kBACEyC,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;oBACrFM,UAAU,EAAE,0CAA0C;oBACtDE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,mCAAmC;oBAC9CE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFlE,OAAA;kBACEyC,KAAK,EAAE;oBACLI,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,KAAK,GAAGD,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK;oBACrFM,UAAU,EAAE,0CAA0C;oBACtDE,YAAY,EAAE,KAAK;oBACnBE,SAAS,EAAE,mCAAmC;oBAC9CE,UAAU,EAAE;kBACd;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlE,OAAA;YAAKiC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzClC,OAAA,CAACpB,MAAM,CAACuF,GAAG;cACT/B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAE8B,KAAK,EAAE;cAAI,CAAE;cACpC7B,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAE8B,KAAK,EAAE;cAAE,CAAE;cAClCZ,UAAU,EAAE;gBAAEa,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CrC,SAAS,EAAC,wEAAwE;cAClFQ,KAAK,EAAE;gBACL8B,QAAQ,EAAE,OAAO;gBACjB1B,KAAK,EAAE;cACT,CAAE;cAAAX,QAAA,gBAGFlC,OAAA;gBAAKiC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,gBAC7DlC,OAAA;kBAAIiC,SAAS,EAAC,kHAAkH;kBAC5HQ,KAAK,EAAE;oBACL+B,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAAvC,QAAA,gBAEJlC,OAAA,CAACpB,MAAM,CAAC8F,IAAI;oBACVzC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEqC,CAAC,EAAE,CAAC,EAAE;sBAAEP,KAAK,EAAE;oBAAI,CAAE;oBAC5C7B,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVqC,CAAC,EAAE,CAAC;sBACJP,KAAK,EAAE,CAAC;sBACRQ,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFpB,UAAU,EAAE;sBACVa,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVM,UAAU,EAAE;wBACVP,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVZ,KAAK,EAAE,GAAG;sBACVa,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBzB,UAAU,EAAE;wBAAEa,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACF5B,KAAK,EAAE;sBACLV,KAAK,EAAE,SAAS;sBAChBmD,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,GACH,OAGC,eACAlC,OAAA,CAACpB,MAAM,CAACuF,GAAG;sBACTlC,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB8B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBe,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF3B,UAAU,EAAE;wBACVa,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACF7B,KAAK,EAAE;wBACL0C,eAAe,EAAE,SAAS;wBAC1B7B,SAAS,EAAE;sBACb;oBAAE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdlE,OAAA,CAACpB,MAAM,CAAC8F,IAAI;oBACVzC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEqC,CAAC,EAAE,EAAE;sBAAEP,KAAK,EAAE;oBAAI,CAAE;oBAC3C7B,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVqC,CAAC,EAAE,CAAC;sBACJP,KAAK,EAAE,CAAC;sBACR/B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnBuC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFpB,UAAU,EAAE;sBACVa,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVjC,CAAC,EAAE;wBACDgC,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVP,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVZ,KAAK,EAAE,GAAG;sBACVa,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBzB,UAAU,EAAE;wBAAEa,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACF5B,KAAK,EAAE;sBACLV,KAAK,EAAE,SAAS;sBAChBmD,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,GACH,MAGC,eACAlC,OAAA,CAACpB,MAAM,CAACuF,GAAG;sBACTlC,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBqC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACdtC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnB8C,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF3B,UAAU,EAAE;wBACVa,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACF7B,KAAK,EAAE;wBACL0C,eAAe,EAAE,SAAS;wBAC1B7B,SAAS,EAAE;sBACb;oBAAE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdlE,OAAA;oBACEiC,SAAS,EAAC,6EAA6E;oBACvFQ,KAAK,EAAE;sBACLS,UAAU,EAAE,SAAS;sBACrBI,SAAS,EAAE,4BAA4B;sBACvCT,KAAK,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;sBACjDE,MAAM,EAAEH,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;sBAClDwC,UAAU,EAAE,KAAK;sBACjBC,aAAa,EAAE;oBACjB,CAAE;oBAAAnD,QAAA,gBAEFlC,OAAA;sBACEsF,GAAG,EAAC,cAAc;sBAClBC,GAAG,EAAC,gBAAgB;sBACpBtD,SAAS,EAAC,4BAA4B;sBACtCQ,KAAK,EAAE;wBAAE+C,SAAS,EAAE;sBAAQ,CAAE;sBAC9BC,OAAO,EAAG/B,CAAC,IAAK;wBACdA,CAAC,CAACgC,MAAM,CAACjD,KAAK,CAACM,OAAO,GAAG,MAAM;wBAC/BW,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAClD,KAAK,CAACM,OAAO,GAAG,MAAM;sBAC7C;oBAAE;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFlE,OAAA;sBACEiC,SAAS,EAAC,gHAAgH;sBAC1HQ,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACf6C,QAAQ,EAAE;sBACZ,CAAE;sBAAA1D,QAAA,EACH;oBAED;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGLlE,OAAA,CAACpB,MAAM,CAACuF,GAAG;kBACTlC,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAES,KAAK,EAAE,CAAC;oBAAEP,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPM,KAAK,EAAE,MAAM;oBACbP,OAAO,EAAE,CAAC;oBACVgB,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFE,UAAU,EAAE;oBACVa,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVhB,SAAS,EAAE;sBACTe,QAAQ,EAAE,CAAC;sBACXQ,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFtC,KAAK,EAAE;oBACLS,UAAU,EAAE,mDAAmD;oBAC/DI,SAAS,EAAE;kBACb;gBAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNlE,OAAA;gBAAKiC,SAAS,EAAC;cAAyK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNlE,OAAA;YAAKiC,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC3D9B,IAAI,iBACHJ,OAAA,CAACpB,MAAM,CAACuF,GAAG;cACT/B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAE8B,KAAK,EAAE;cAAI,CAAE;cACpC7B,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAE8B,KAAK,EAAE;cAAE,CAAE;cAClCZ,UAAU,EAAE;gBAAEa,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CrC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7ClC,OAAA,CAAC6F,cAAc;gBACbzF,IAAI,EAAEA,IAAK;gBACX0F,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvBtD,KAAK,EAAE;kBACLI,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFlE,OAAA;gBAAKiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClC,OAAA;kBAAKiC,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,EACnH,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,IAAI,KAAI;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNlE,OAAA;kBAAKiC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAC5F,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,KAAK,MAAK,SAAS,GAAI,SAAQ7F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F,KAAM,EAAC,GAAG9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8F;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBlE,OAAA;MAAKiC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlC,OAAA;QAAKiC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlC,OAAA;UAAIiC,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBnB,eAAe,CAACL,cAAc,CAAC,EAAC,IAAE,EAAC,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,SAAS,MAAI/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4F,IAAI,MAAKzF,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACLlE,OAAA;UAAGiC,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxB3B,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJlE,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA,CAACN,MAAM;YAAC+C,KAAK,EAAE;cAAEV,KAAK,EAAE,SAAS;cAAEqE,WAAW,EAAE;YAAS;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAACpD,eAAe,CAACN,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAAC+C,KAAK,EAAE;cAAEV,KAAK,EAAE,SAAS;cAAEqD,UAAU,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DlE,OAAA;YAAKyC,KAAK,EAAE;cAAEmD,QAAQ,EAAE,UAAU;cAAE7D,KAAK,EAAE,SAAS;cAAEsE,SAAS,EAAE;YAAS,CAAE;YAAAnE,QAAA,EAAC;UAE7E;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMNlE,OAAA;QAAKiC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjClC,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAAC4E,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC1E,IAAI;YAC/B,oBACE7B,OAAA,CAACpB,MAAM,CAACuF,GAAG;cAET/B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BmB,UAAU,EAAE;gBAAEa,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEkC,KAAK,GAAG;cAAI,CAAE;cAClDvE,SAAS,EAAG,kBAAiBsE,IAAI,CAACvE,UAAW,IAAGuE,IAAI,CAACxE,KAAM,EAAE;cAC7DS,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAACoG,IAAI,CAACzE,IAAI,CAAE;cACnC4E,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGlD,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACmD,GAAG,KAAK,OAAO,IAAInD,CAAC,CAACmD,GAAG,KAAK,GAAG,EAAE;kBACtC1G,QAAQ,CAACoG,IAAI,CAACzE,IAAI,CAAC;gBACrB;cACF,CAAE;cACFW,KAAK,EAAE;gBACLY,MAAM,EAAE,SAAS;gBACjByD,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAA5E,QAAA,gBAIFlC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BlC,OAAA,CAACyG,aAAa;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENlE,OAAA;gBAAIiC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BqE,IAAI,CAAC5E;cAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELlE,OAAA;gBAAGiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCqE,IAAI,CAAC3E;cAAW;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GA9BCqC,IAAI,CAAC5E,KAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlE,OAAA,CAACpB,MAAM,CAACuF,GAAG;UACT/B,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBkB,UAAU,EAAE;YAAEa,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CrC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjClC,OAAA;YAAKiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjClC,OAAA,CAACR,eAAe;cAACyC,SAAS,EAAC;YAAuC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrElE,OAAA;cAAAkC,QAAA,EAAM;YAAkC;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/ClE,OAAA,CAACL,QAAQ;cAACsC,SAAS,EAAC;YAAuC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC,eAGNlE,OAAA,CAAC+G,aAAa;MAACC,MAAM,EAAEpG,WAAY;MAACqG,SAAS,EAAEpG;IAAe;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9D,CAAC;AAEV,CAAC;AAAChE,EAAA,CAnjBID,GAAG;EAAA,QACUvB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAAoI,EAAA,GAHlCjH,GAAG;AAqjBT,eAAeA,GAAG;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}