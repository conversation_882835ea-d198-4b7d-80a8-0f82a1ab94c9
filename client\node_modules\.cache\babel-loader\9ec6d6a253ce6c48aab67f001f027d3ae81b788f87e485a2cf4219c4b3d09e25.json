{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n\n    // Check for recent payment success and redirect if needed\n    const paymentSuccess = localStorage.getItem('paymentSuccess');\n    if (paymentSuccess) {\n      try {\n        const successData = JSON.parse(paymentSuccess);\n        const timeDiff = Date.now() - successData.timestamp;\n\n        // If payment was successful within last 2 minutes and user hasn't been redirected\n        if (timeDiff < 120000 && !successData.redirected) {\n          console.log('🔄 Recent payment success detected, redirecting to hub...');\n\n          // Mark as redirected\n          localStorage.setItem('paymentSuccess', JSON.stringify({\n            ...successData,\n            redirected: true\n          }));\n\n          // Show success message and redirect\n          message.success({\n            content: '🎉 Payment successful! Redirecting to Hub...',\n            duration: 3,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n          setTimeout(() => {\n            window.location.href = '/user/hub';\n          }, 2000);\n        }\n      } catch (error) {\n        console.error('Error parsing payment success data:', error);\n        localStorage.removeItem('paymentSuccess');\n      }\n    }\n  }, []);\n\n  // Enable background scrolling when modals are open for better UX\n  useEffect(() => {\n    // Always allow background scrolling - remove any scroll restrictions\n    document.body.style.overflow = '';\n    document.body.style.position = '';\n    document.body.style.width = '';\n    document.body.style.height = '';\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.height = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n      } else {\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n    } catch (error) {\n      // No active subscription found\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async selectedPlan => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n  const handlePlanSelect = async plan => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      setShowUpgradeRestriction(true);\n      return;\n    }\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        var _response$data;\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh'\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.order_id) || 'demo_order';\n        checkPaymentConfirmation(orderIdToCheck);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async orderId => {\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n\n          // Enhanced payment success detection with multiple conditions\n          const isPaymentSuccessful = statusResponse && (\n          // Condition 1: Subscription activated\n          statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active' ||\n          // Condition 2: Standard completion\n          statusResponse.status === 'completed' && statusResponse.success === true ||\n          // Condition 3: Demo mode success\n          statusResponse.demo === true && statusResponse.success === true ||\n          // Condition 4: ZenoPay webhook success\n          statusResponse.zenopay_status === 'COMPLETED' ||\n          // Condition 5: Direct success flag\n          statusResponse.success === true && statusResponse.status === 'completed');\n          if (isPaymentSuccessful) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success INSTANTLY - no delay\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Enhanced auto-navigation with longer countdown and persistence\n            setAutoNavigateCountdown(10); // Increased to 10 seconds\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  // Force navigation to hub with delay for better UX\n                  setShowSuccessModal(false);\n                  setTimeout(() => {\n                    window.location.href = '/user/hub';\n                  }, 500);\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n            // Store success state in localStorage for persistence\n            localStorage.setItem('paymentSuccess', JSON.stringify({\n              timestamp: Date.now(),\n              orderId: orderId,\n              status: 'completed',\n              redirected: false\n            }));\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('standard')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"currency\",\n                      children: \"TZS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 25\n                    }, this), (_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price\",\n                      children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"discount-badge\",\n                      children: [Math.round((plan.actualPrice - plan.discountedPrice) / plan.actualPrice * 100), \"% OFF\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 685,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"duration-highlight\",\n                    children: plan.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 23\n                  }, this), \" month\", plan.duration > 1 ? 's' : '', \" access\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading === plan._id,\n                style: {\n                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '1rem 1.5rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  width: '100%',\n                  opacity: paymentLoading === plan._id ? 0.6 : 1\n                },\n                onMouseEnter: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 21\n                }, this), paymentLoading === plan._id ? 'Processing...' : subscriptionStatus === 'active' ? 'Click to Upgrade' : subscriptionStatus === 'expired' ? 'Click to Renew' : 'Click to Pay']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        onClick: e => {\n          // Allow clicking through overlay but prevent closing modal accidentally\n          if (e.target === e.currentTarget) {\n            // Don't close modal, just allow background interaction\n            e.stopPropagation();\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container processing\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: handleCloseProcessingModal,\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header processing\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processing-icon\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"payment-icon\",\n                width: \"32\",\n                height: \"32\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10 16H6\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14 16H12.5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 10L22 10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Secure transaction in progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-indicator processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"status-text\",\n                children: paymentStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instruction-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\",\n                    fill: \"currentColor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Check Your Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-number\",\n                children: user === null || user === void 0 ? void 0 : user.phoneNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-steps\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"1. You'll receive an SMS with payment instructions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"2. Follow the SMS steps to confirm payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"3. Complete the mobile money transaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this), showTryAgain && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"try-again-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Taking longer than expected?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"try-again-btn\",\n                onClick: handleTryAgain,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M4 12a8 8 0 018-8V2.5\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 4L9 7L12 10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 23\n                }, this), \"Try Again\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        onClick: e => {\n          // Allow clicking through overlay but keep modal open\n          if (e.target === e.currentTarget) {\n            e.stopPropagation();\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container success\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: () => {\n              setAutoNavigateCountdown(null);\n              setShowSuccessModal(false);\n            },\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header success\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"48\",\n                height: \"48\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  fill: \"#22c55e\",\n                  fillOpacity: \"0.2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 9L10.5 14.5L8 12\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [autoNavigateCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"countdown-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"countdown-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"3\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Redirecting to Hub in \", autoNavigateCountdown, \" seconds...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-summary-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Subscription Activated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount Paid\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row status\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status-badge\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12L11 14L15 10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 951,\n                      columnNumber: 25\n                    }, this), \"Active\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"features-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDE80 Premium Features Unlocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"features-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 968,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Unlimited Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 974,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 975,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Assistant\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Study Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Progress Tracking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Learning Videos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1002,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1001,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Forum Access\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"primary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"9,22 9,12 15,12 15,22\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 21\n                }, this), \"Continue to Hub \", autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"secondary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                },\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UpgradeRestrictionModal, {\n        visible: showUpgradeRestriction,\n        onClose: () => setShowUpgradeRestriction(false),\n        currentPlan: plans.find(p => p._id === (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.activePlan)) || (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.plan),\n        subscription: subscriptionData,\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubscriptionExpiredModal, {\n        visible: showExpiredModal,\n        onClose: () => setShowExpiredModal(false),\n        onRenew: handleRenewSubscription,\n        subscription: subscriptionData,\n        user: user,\n        plans: plans\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 538,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"nf8ujwg+lplmCuJ8ED3F2zkScnI=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "UpgradeRestrictionModal", "SubscriptionExpiredModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "paymentSuccess", "localStorage", "getItem", "successData", "JSON", "parse", "timeDiff", "Date", "now", "timestamp", "redirected", "console", "log", "setItem", "stringify", "success", "content", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "window", "location", "href", "error", "removeItem", "document", "body", "overflow", "position", "width", "height", "detectScrollableContent", "modalContents", "querySelectorAll", "for<PERSON>ach", "scrollHeight", "clientHeight", "classList", "add", "remove", "addEventListener", "removeEventListener", "isSubscriptionExpired", "response", "data", "length", "Array", "isArray", "info", "warning", "endDate", "today", "setHours", "handleRenewSubscription", "handlePlanSelect", "handleCloseProcessingModal", "handleTryAgain", "plan", "phoneNumber", "test", "_user$name", "Promise", "resolve", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "_response$data", "orderIdToCheck", "order_id", "checkPaymentConfirmation", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "isPaymentSuccessful", "demo", "zenopay_status", "countdownInterval", "setInterval", "prev", "clearInterval", "includes", "hidden", "getSubscriptionStatus", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "toLocaleString", "round", "slice", "feature", "index", "disabled", "background", "color", "border", "borderRadius", "padding", "cursor", "display", "alignItems", "justifyContent", "gap", "onMouseEnter", "e", "target", "transform", "boxShadow", "onMouseLeave", "currentTarget", "stopPropagation", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fillOpacity", "cx", "cy", "r", "points", "visible", "onClose", "currentPlan", "find", "p", "onRenew", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n\n    // Check for recent payment success and redirect if needed\n    const paymentSuccess = localStorage.getItem('paymentSuccess');\n    if (paymentSuccess) {\n      try {\n        const successData = JSON.parse(paymentSuccess);\n        const timeDiff = Date.now() - successData.timestamp;\n\n        // If payment was successful within last 2 minutes and user hasn't been redirected\n        if (timeDiff < 120000 && !successData.redirected) {\n          console.log('🔄 Recent payment success detected, redirecting to hub...');\n\n          // Mark as redirected\n          localStorage.setItem('paymentSuccess', JSON.stringify({\n            ...successData,\n            redirected: true\n          }));\n\n          // Show success message and redirect\n          message.success({\n            content: '🎉 Payment successful! Redirecting to Hub...',\n            duration: 3,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          setTimeout(() => {\n            window.location.href = '/user/hub';\n          }, 2000);\n        }\n      } catch (error) {\n        console.error('Error parsing payment success data:', error);\n        localStorage.removeItem('paymentSuccess');\n      }\n    }\n  }, []);\n\n  // Enable background scrolling when modals are open for better UX\n  useEffect(() => {\n    // Always allow background scrolling - remove any scroll restrictions\n    document.body.style.overflow = '';\n    document.body.style.position = '';\n    document.body.style.width = '';\n    document.body.style.height = '';\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.height = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n      } else {\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n    } catch (error) {\n      // No active subscription found\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n\n\n  const handlePlanSelect = async (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n\n          // Enhanced payment success detection with multiple conditions\n          const isPaymentSuccessful = statusResponse && (\n            // Condition 1: Subscription activated\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            // Condition 2: Standard completion\n            (statusResponse.status === 'completed' && statusResponse.success === true) ||\n            // Condition 3: Demo mode success\n            (statusResponse.demo === true && statusResponse.success === true) ||\n            // Condition 4: ZenoPay webhook success\n            (statusResponse.zenopay_status === 'COMPLETED') ||\n            // Condition 5: Direct success flag\n            (statusResponse.success === true && statusResponse.status === 'completed')\n          );\n\n          if (isPaymentSuccessful) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success INSTANTLY - no delay\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Enhanced auto-navigation with longer countdown and persistence\n            setAutoNavigateCountdown(10); // Increased to 10 seconds\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  // Force navigation to hub with delay for better UX\n                  setShowSuccessModal(false);\n                  setTimeout(() => {\n                    window.location.href = '/user/hub';\n                  }, 500);\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n            // Store success state in localStorage for persistence\n            localStorage.setItem('paymentSuccess', JSON.stringify({\n              timestamp: Date.now(),\n              orderId: orderId,\n              status: 'completed',\n              redirected: false\n            }));\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Professional Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => {\n              // Allow clicking through overlay but prevent closing modal accidentally\n              if (e.target === e.currentTarget) {\n                // Don't close modal, just allow background interaction\n                e.stopPropagation();\n              }\n            }}\n          >\n            <div className=\"payment-modal-container processing\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={handleCloseProcessingModal}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header processing\">\n                <div className=\"processing-icon\">\n                  <div className=\"spinner\"></div>\n                  <svg className=\"payment-icon\" width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                    <path d=\"M10 16H6\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M14 16H12.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M2 10L22 10\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                  </svg>\n                </div>\n                <h2>Processing Payment</h2>\n                <p>Secure transaction in progress</p>\n              </div>\n\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Status */}\n                <div className=\"status-card\">\n                  <div className=\"status-indicator processing\"></div>\n                  <p className=\"status-text\">{paymentStatus}</p>\n                </div>\n\n                {/* Plan Info */}\n                <div className=\"plan-info-card\">\n                  <h3>{selectedPlan?.title}</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Amount</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div className=\"instruction-card\">\n                  <div className=\"instruction-header\">\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\" fill=\"currentColor\"/>\n                    </svg>\n                    <span>Check Your Phone</span>\n                  </div>\n                  <div className=\"phone-number\">{user?.phoneNumber}</div>\n                  <div className=\"instruction-steps\">\n                    <div className=\"step\">1. You'll receive an SMS with payment instructions</div>\n                    <div className=\"step\">2. Follow the SMS steps to confirm payment</div>\n                    <div className=\"step\">3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                {/* Try Again */}\n                {showTryAgain && (\n                  <div className=\"try-again-card\">\n                    <p>Taking longer than expected?</p>\n                    <button className=\"try-again-btn\" onClick={handleTryAgain}>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M4 12a8 8 0 018-8V2.5\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <path d=\"M12 4L9 7L12 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                      Try Again\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Professional Success Modal */}\n        {showSuccessModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => {\n              // Allow clicking through overlay but keep modal open\n              if (e.target === e.currentTarget) {\n                e.stopPropagation();\n              }\n            }}\n          >\n            <div className=\"payment-modal-container success\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={() => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                }}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header success\">\n                <div className=\"success-icon\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" fill=\"#22c55e\" fillOpacity=\"0.2\"/>\n                    <path d=\"M16 9L10.5 14.5L8 12\" stroke=\"#22c55e\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" stroke=\"#22c55e\" strokeWidth=\"2\"/>\n                  </svg>\n                </div>\n                <h2>Payment Successful!</h2>\n                <p>Welcome to {selectedPlan?.title}!</p>\n              </div>\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div className=\"countdown-card\">\n                    <div className=\"countdown-icon\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    </div>\n                    <p>Redirecting to Hub in {autoNavigateCountdown} seconds...</p>\n                  </div>\n                )}\n\n                {/* Plan Summary */}\n                <div className=\"plan-summary-card\">\n                  <h3>Subscription Activated</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Plan</span>\n                      <strong>{selectedPlan?.title}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Amount Paid</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row status\">\n                      <span>Status</span>\n                      <div className=\"status-badge\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                        Active\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked */}\n                <div className=\"features-card\">\n                  <h3>🚀 Premium Features Unlocked</h3>\n                  <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Unlimited Quizzes</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>AI Assistant</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Study Materials</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Progress Tracking</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Learning Videos</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Forum Access</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"modal-actions\">\n                  <button\n                    className=\"primary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                  >\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      <polyline points=\"9,22 9,12 15,12 15,22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    </svg>\n                    Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    className=\"secondary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                    }}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,uBAAuB,MAAM,qEAAqE;AACzG,OAAOC,wBAAwB,MAAM,uEAAuE;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAGrE,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM;IAAEoD;EAAK,CAAC,GAAGlD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGpD,WAAW,CAAEmD,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGrD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMsD,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDhE,SAAS,CAAC,MAAM;IACdiE,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;;IAE1B;IACA,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,IAAIF,cAAc,EAAE;MAClB,IAAI;QACF,MAAMG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAC9C,MAAMM,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,WAAW,CAACM,SAAS;;QAEnD;QACA,IAAIH,QAAQ,GAAG,MAAM,IAAI,CAACH,WAAW,CAACO,UAAU,EAAE;UAChDC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;UAExE;UACAX,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAET,IAAI,CAACU,SAAS,CAAC;YACpD,GAAGX,WAAW;YACdO,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;;UAEH;UACAzE,OAAO,CAAC8E,OAAO,CAAC;YACdC,OAAO,EAAE,8CAA8C;YACvDpB,QAAQ,EAAE,CAAC;YACXqB,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;UAEFC,UAAU,CAAC,MAAM;YACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;UACpC,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3DxB,YAAY,CAACyB,UAAU,CAAC,gBAAgB,CAAC;MAC3C;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7F,SAAS,CAAC,MAAM;IACd;IACA8F,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACY,QAAQ,GAAG,EAAE;IACjCF,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACa,QAAQ,GAAG,EAAE;IACjCH,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACc,KAAK,GAAG,EAAE;IAC9BJ,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACe,MAAM,GAAG,EAAE;;IAE/B;IACA,OAAO,MAAM;MACXL,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACY,QAAQ,GAAG,EAAE;MACjCF,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACa,QAAQ,GAAG,EAAE;MACjCH,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACc,KAAK,GAAG,EAAE;MAC9BJ,QAAQ,CAACC,IAAI,CAACX,KAAK,CAACe,MAAM,GAAG,EAAE;IACjC,CAAC;EACH,CAAC,EAAE,CAAClE,mBAAmB,EAAEE,gBAAgB,CAAC,CAAC;;EAE3C;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoG,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMC,aAAa,GAAGP,QAAQ,CAACQ,gBAAgB,CAAC,gBAAgB,CAAC;MACjED,aAAa,CAACE,OAAO,CAACpB,OAAO,IAAI;QAC/B,IAAIA,OAAO,CAACqB,YAAY,GAAGrB,OAAO,CAACsB,YAAY,EAAE;UAC/CtB,OAAO,CAACuB,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACrC,CAAC,MAAM;UACLxB,OAAO,CAACuB,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAI3E,mBAAmB,IAAIE,gBAAgB,EAAE;MAC3C;MACAqD,UAAU,CAACY,uBAAuB,EAAE,GAAG,CAAC;;MAExC;MACAX,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAET,uBAAuB,CAAC;MAE1D,OAAO,MAAM;QACXX,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAEV,uBAAuB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACnE,mBAAmB,EAAEE,gBAAgB,CAAC,CAAC;;EAE3C;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIqD,gBAAgB,IAAI0D,qBAAqB,CAAC,CAAC,EAAE;MAE/CnE,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLA,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkF,QAAQ,GAAG,MAAMrG,QAAQ,CAAC,CAAC;MAEjC,IAAIqG,QAAQ,CAAC9B,OAAO,IAAI8B,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjEtF,QAAQ,CAACoF,QAAQ,CAACC,IAAI,CAAC;MACzB,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;QACzD;QACAtF,QAAQ,CAACoF,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLpF,QAAQ,CAAC4B,WAAW,CAAC;QACrBpD,OAAO,CAACiH,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MAErDhE,QAAQ,CAAC4B,WAAW,CAAC;MACrBpD,OAAO,CAACkH,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMnG,kBAAkB,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAO+E,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;;EAED;EACA,MAAMmB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC1D,gBAAgB,EAAE,OAAO,IAAI;;IAElC;IACA,IAAI,CAACA,gBAAgB,CAACkE,OAAO,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAIlE,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE,OAAO,IAAI;;IAE1D;IACA,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErD;IACA,MAAMuD,OAAO,GAAG,IAAI7C,IAAI,CAACrB,gBAAgB,CAACkE,OAAO,CAAC;IAClD,MAAMC,KAAK,GAAG,IAAI9C,IAAI,CAAC,CAAC;IACxB8C,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5BF,OAAO,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9B,OAAOF,OAAO,GAAGC,KAAK;EACxB,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAG,MAAOrF,YAAY,IAAK;IACtDO,mBAAmB,CAAC,KAAK,CAAC;IAC1B,MAAM+E,gBAAgB,CAACtF,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMuF,0BAA0B,GAAGA,CAAA,KAAM;IACvC1F,sBAAsB,CAAC,KAAK,CAAC;IAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBgB,eAAe,CAAC,KAAK,CAAC;IACtBF,sBAAsB,CAAC,IAAI,CAAC;IAC5BN,gBAAgB,CAAC,EAAE,CAAC;IACpBpC,OAAO,CAACiH,IAAI,CAAC,uDAAuD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxF,YAAY,EAAE;MAChBW,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC,IAAI,CAAC;MAC5B6E,gBAAgB,CAACtF,YAAY,CAAC;IAChC;EACF,CAAC;EAID,MAAMsF,gBAAgB,GAAG,MAAOG,IAAI,IAAK;IACvC;IACA,IAAIzE,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,QAAQ,IAAIX,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE;MAEzGG,yBAAyB,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACS,IAAI,CAAC4E,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC7E,IAAI,CAAC4E,WAAW,CAAC,EAAE;MACjE3H,OAAO,CAACwF,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAqC,UAAA;MAEF;MACA3F,eAAe,CAACwF,IAAI,CAAC;MACrB9F,iBAAiB,CAAC8F,IAAI,CAACrE,GAAG,CAAC;MAC3BvB,sBAAsB,CAAC,IAAI,CAAC;MAC5Bc,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC4B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAClCnC,gBAAgB,CAAC,sCAAsC,CAAC;;MAIxD;MACA,MAAM,IAAI0F,OAAO,CAACC,OAAO,IAAI3C,UAAU,CAAC2C,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA3C,UAAU,CAAC,MAAM;QACfxC,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;MAET,MAAMoF,WAAW,GAAG;QAClBN,IAAI,EAAEA,IAAI;QACVO,MAAM,EAAElF,IAAI,CAACM,GAAG;QAChB6E,SAAS,EAAEnF,IAAI,CAAC4E,WAAW;QAC3BQ,SAAS,EAAEpF,IAAI,CAACqF,KAAK,IAAK,IAAAP,UAAA,GAAE9E,IAAI,CAACsF,IAAI,cAAAR,UAAA,uBAATA,UAAA,CAAWS,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDnG,gBAAgB,CAAC,0CAA0C,CAAC;MAC5D,MAAMwE,QAAQ,GAAG,MAAMpG,UAAU,CAACwH,WAAW,CAAC;MAE9C,IAAIpB,QAAQ,CAAC9B,OAAO,EAAE;QAAA,IAAA0D,cAAA;QACpBpG,gBAAgB,CAAC,wDAAwD,CAAC;;QAI1E;QACApC,OAAO,CAAC8E,OAAO,CAAC;UACdC,OAAO,EAAG,8CAA6ChC,IAAI,CAAC4E,WAAY,sCAAqC;UAC7GhE,QAAQ,EAAE,CAAC;UACXqB,KAAK,EAAE;YACLC,SAAS,EAAE;UACb;QACF,CAAC,CAAC;;QAEF;QACA,MAAMwD,cAAc,GAAG7B,QAAQ,CAAC8B,QAAQ,MAAAF,cAAA,GAAI5B,QAAQ,CAACC,IAAI,cAAA2B,cAAA,uBAAbA,cAAA,CAAeE,QAAQ,KAAI,YAAY;QACnFC,wBAAwB,CAACF,cAAc,CAAC;MAE1C,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAChC,QAAQ,CAAC5G,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOwF,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC1D,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACwF,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAACxF,OAAO,CAAC;MACjD4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAM+G,wBAAwB,GAAG,MAAOE,OAAO,IAAK;IAClD,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACF3G,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAI4G,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;MAEzB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QAEV,IAAI;UACF,MAAMG,cAAc,GAAG,MAAM1I,kBAAkB,CAAC;YAAEoI;UAAQ,CAAC,CAAC;;UAE5D;UACA,MAAMO,mBAAmB,GAAGD,cAAc;UACxC;UACCA,cAAc,CAAChH,aAAa,KAAK,MAAM,IAAIgH,cAAc,CAACvF,MAAM,KAAK,QAAQ;UAC9E;UACCuF,cAAc,CAACvF,MAAM,KAAK,WAAW,IAAIuF,cAAc,CAACrE,OAAO,KAAK,IAAK;UAC1E;UACCqE,cAAc,CAACE,IAAI,KAAK,IAAI,IAAIF,cAAc,CAACrE,OAAO,KAAK,IAAK;UACjE;UACCqE,cAAc,CAACG,cAAc,KAAK,WAAY;UAC/C;UACCH,cAAc,CAACrE,OAAO,KAAK,IAAI,IAAIqE,cAAc,CAACvF,MAAM,KAAK,WAAY,CAC3E;UAED,IAAIwF,mBAAmB,EAAE;YACvB;YACAN,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA3G,gBAAgB,CAAC,uDAAuD,CAAC;;YAEzE;YACAN,sBAAsB,CAAC,KAAK,CAAC;YAC7BE,mBAAmB,CAAC,IAAI,CAAC;YACzBJ,iBAAiB,CAAC,IAAI,CAAC;;YAEvB;YACAkC,wBAAwB,CAAC,CAAC;;YAE1B;YACA9D,OAAO,CAAC8E,OAAO,CAAC;cACdC,OAAO,EAAE,sDAAsD;cAC/DpB,QAAQ,EAAE,CAAC;cACXqB,KAAK,EAAE;gBACLC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;;YAEF;YACApC,wBAAwB,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAMyG,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1C1G,wBAAwB,CAAC2G,IAAI,IAAI;gBAC/B,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChC;kBACAvH,mBAAmB,CAAC,KAAK,CAAC;kBAC1BoD,UAAU,CAAC,MAAM;oBACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;kBACpC,CAAC,EAAE,GAAG,CAAC;kBACP,OAAO,IAAI;gBACb;gBACA,OAAOkE,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;;YAER;YACAzF,YAAY,CAACY,OAAO,CAAC,gBAAgB,EAAET,IAAI,CAACU,SAAS,CAAC;cACpDL,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC;cACrBsE,OAAO,EAAEA,OAAO;cAChBjF,MAAM,EAAE,WAAW;cACnBa,UAAU,EAAE;YACd,CAAC,CAAC,CAAC;UAEL,CAAC,MAAM,IAAIuE,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA3G,gBAAgB,CAAC,8EAA8E,CAAC;YAEhGgD,UAAU,CAAC,MAAM;cACftD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;cACzB5B,OAAO,CAACkH,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACA9E,gBAAgB,CAAC,0EAA0E,CAAC;YAC5FgD,UAAU,CAAC8D,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAO1D,KAAK,EAAE;UACdd,OAAO,CAACc,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;UAEnD;UACA,IAAIA,KAAK,CAACxF,OAAO,IAAIwF,KAAK,CAACxF,OAAO,CAAC2J,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDjF,OAAO,CAACc,KAAK,CAAC,2CAA2C,CAAC;YAC1DsD,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC;YAC1E;YACAjH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACwF,KAAK,CAAC,6HAA6H,CAAC;YAC5I;UACF;UAEA,IAAIA,KAAK,CAACxF,OAAO,IAAIwF,KAAK,CAACxF,OAAO,CAAC2J,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDjF,OAAO,CAACc,KAAK,CAAC,oDAAoD,CAAC;YACnEsD,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC;YAC1E;YACAjH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACwF,KAAK,CAAC,6CAA6C,CAAC;YAC5D;UACF;UAEA,IAAIwD,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACAjH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACwF,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAJ,UAAU,CAAC8D,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAACrD,QAAQ,CAACkE,MAAM,IAAId,SAAS,EAAE;UACjC1G,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACAgD,UAAU,CAAC,MAAM8D,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAEDxD,QAAQ,CAACe,gBAAgB,CAAC,kBAAkB,EAAEsC,sBAAsB,CAAC;;MAErE;MACA3D,UAAU,CAAC8D,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdsD,SAAS,GAAG,KAAK,CAAC,CAAC;MACnB,IAAIC,sBAAsB,EAAE;QAC1BrD,QAAQ,CAACgB,mBAAmB,CAAC,kBAAkB,EAAEqC,sBAAsB,CAAC,CAAC,CAAC;MAC5E;;MACAjH,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACwF,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAACxF,OAAO,CAAC;MAC9D4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMiI,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5G,gBAAgB,IAAIA,gBAAgB,CAACd,aAAa,KAAK,MAAM,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMuD,OAAO,GAAG,IAAI7C,IAAI,CAACrB,gBAAgB,CAACkE,OAAO,CAAC;MAClD,MAAM5C,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAI6C,OAAO,GAAG5C,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+G,kBAAkB,MAAK,SAAS,IAAK7G,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMmG,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAI1F,IAAI,CAAC0F,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACpH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkE,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAI7C,IAAI,CAACrB,gBAAgB,CAACkE,OAAO,CAAC;IAClD,MAAM5C,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMgG,QAAQ,GAAGnD,OAAO,GAAG5C,GAAG;IAC9B,MAAMgG,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;EAElD,oBACE9I,OAAA;IAAK4J,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC7J,OAAA;MAAK4J,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC7J,OAAA,CAAChB,MAAM,CAAC8K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEvH,QAAQ,EAAE;QAAI,CAAE;QAC9BgH,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B7J,OAAA;UAAI4J,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB7J,OAAA,CAACd,OAAO;YAAC0K,SAAS,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvK,OAAA;UAAG4J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGbvK,OAAA,CAAChB,MAAM,CAAC8K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEvH,QAAQ,EAAE,GAAG;UAAE4H,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC7J,OAAA;UAAI4J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDxB,kBAAkB,KAAK,QAAQ,iBAC9B/I,OAAA;UAAK4J,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC7J,OAAA;YAAK4J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7J,OAAA,CAACZ,aAAa;cAACwK,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDvK,OAAA;cAAM4J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNvK,OAAA;YAAK4J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7J,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7J,OAAA,CAACd,OAAO;gBAAC0K,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCvK,OAAA;gBAAA6J,QAAA,GAAM,QAAM,EAAC,CAAA3H,gBAAgB,aAAhBA,gBAAgB,wBAAA7B,qBAAA,GAAhB6B,gBAAgB,CAAEuI,UAAU,cAAApK,qBAAA,uBAA5BA,qBAAA,CAA8BkC,KAAK,KAAI,cAAc;cAAA;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNvK,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7J,OAAA,CAACb,aAAa;gBAACyK,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCvK,OAAA;gBAAA6J,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC9G,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkE,OAAO,CAAC;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNvK,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7J,OAAA,CAACZ,aAAa;gBAACwK,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCvK,OAAA;gBAAA6J,QAAA,GAAM,kBAAgB,EAACP,gBAAgB,CAAC,CAAC;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxB,kBAAkB,KAAK,SAAS,iBAC/B/I,OAAA;UAAK4J,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC7J,OAAA;YAAK4J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7J,OAAA,CAACX,aAAa;cAACuK,SAAS,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDvK,OAAA;cAAM4J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNvK,OAAA;YAAK4J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7J,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7J,OAAA,CAACb,aAAa;gBAACyK,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCvK,OAAA;gBAAA6J,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC9G,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkE,OAAO,CAAC;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNvK,OAAA;cAAG4J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxB,kBAAkB,KAAK,MAAM,iBAC5B/I,OAAA;UAAK4J,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7J,OAAA;YAAK4J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7J,OAAA,CAACT,MAAM;cAACqK,SAAS,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCvK,OAAA;cAAM4J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNvK,OAAA;YAAK4J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC7J,OAAA;cAAG4J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbvK,OAAA,CAAChB,MAAM,CAAC8K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEvH,QAAQ,EAAE,GAAG;UAAE4H,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B7J,OAAA;UAAI4J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bd,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eAGLvK,OAAA;UAAG4J,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5Bd,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEH7J,OAAO,gBACNV,OAAA;UAAK4J,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7J,OAAA;YAAK4J,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BvK,OAAA;YAAA6J,QAAA,EAAG;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJ/J,KAAK,CAACuF,MAAM,KAAK,CAAC,gBACpB/F,OAAA;UAAK4J,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7J,OAAA;YAAK4J,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCvK,OAAA;YAAA6J,QAAA,EAAI;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BvK,OAAA;YAAA6J,QAAA,EAAG;UAA6E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFvK,OAAA;YAAQ4J,SAAS,EAAC,aAAa;YAACc,OAAO,EAAE5H,UAAW;YAAA+G,QAAA,EAAC;UAErD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENvK,OAAA;UAAK4J,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBrJ,KAAK,CAACmK,GAAG,CAAEhE,IAAI;YAAA,IAAAiE,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACd/K,OAAA,CAAChB,MAAM,CAAC8K,GAAG;cAETkB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BrB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB7J,OAAA;gBAAK4J,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7J,OAAA;kBAAI4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAElD,IAAI,CAACpE;gBAAK;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAAjE,IAAI,CAACpE,KAAK,cAAAqI,WAAA,uBAAVA,WAAA,CAAYpD,WAAW,CAAC,CAAC,CAACoB,QAAQ,CAAC,UAAU,CAAC,kBAC7C5I,OAAA;kBAAM4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENvK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7J,OAAA;kBAAK4J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7J,OAAA;oBAAK4J,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7J,OAAA;sBAAM4J,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAG;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAAAM,qBAAA,GACpClE,IAAI,CAACjE,eAAe,cAAAmI,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACL5D,IAAI,CAAClE,WAAW,GAAGkE,IAAI,CAACjE,eAAe,iBACtC1C,OAAA,CAAAE,SAAA;oBAAA2J,QAAA,gBACE7J,OAAA;sBAAM4J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,IAAAiB,iBAAA,GAAEnE,IAAI,CAAClE,WAAW,cAAAqI,iBAAA,uBAAhBA,iBAAA,CAAkBK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFvK,OAAA;sBAAM4J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC7BJ,IAAI,CAAC2B,KAAK,CAAE,CAACzE,IAAI,CAAClE,WAAW,GAAGkE,IAAI,CAACjE,eAAe,IAAIiE,IAAI,CAAClE,WAAW,GAAI,GAAG,CAAC,EAAC,OACpF;oBAAA;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACP,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7J,OAAA;oBAAM4J,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAElD,IAAI,CAAC/D;kBAAQ;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,UAAM,EAAC5D,IAAI,CAAC/D,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,SACjG;gBAAA;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvK,OAAA;gBAAK4J,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAkB,cAAA,GAC3BpE,IAAI,CAACnE,QAAQ,cAAAuI,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CvL,OAAA;kBAAiB4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC7J,OAAA,CAACZ,aAAa;oBAACwK,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CvK,OAAA;oBAAA6J,QAAA,EAAOyB;kBAAO;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvK,OAAA;gBACE4J,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAMlE,gBAAgB,CAACG,IAAI,CAAE;gBACtC6E,QAAQ,EAAE5K,cAAc,KAAK+F,IAAI,CAACrE,GAAI;gBACtC2B,KAAK,EAAE;kBACLwH,UAAU,EAAE,2CAA2C;kBACvDC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdC,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,aAAa;kBACtB1H,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjB0H,MAAM,EAAElL,cAAc,KAAK+F,IAAI,CAACrE,GAAG,GAAG,aAAa,GAAG,SAAS;kBAC/D6H,UAAU,EAAE,eAAe;kBAC3B4B,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBC,GAAG,EAAE,QAAQ;kBACbnH,KAAK,EAAE,MAAM;kBACbiF,OAAO,EAAEpJ,cAAc,KAAK+F,IAAI,CAACrE,GAAG,GAAG,GAAG,GAAG;gBAC/C,CAAE;gBACF6J,YAAY,EAAGC,CAAC,IAAK;kBACnB,IAAIxL,cAAc,KAAK+F,IAAI,CAACrE,GAAG,EAAE;oBAC/B8J,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACwH,UAAU,GAAG,2CAA2C;oBACvEW,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACqI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACsI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBACFC,YAAY,EAAGJ,CAAC,IAAK;kBACnB,IAAIxL,cAAc,KAAK+F,IAAI,CAACrE,GAAG,EAAE;oBAC/B8J,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACwH,UAAU,GAAG,2CAA2C;oBACvEW,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACqI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACpI,KAAK,CAACsI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBAAA1C,QAAA,gBAEF7J,OAAA,CAACV,YAAY;kBAACsK,SAAS,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpC3J,cAAc,KAAK+F,IAAI,CAACrE,GAAG,GACxB,eAAe,GACfyG,kBAAkB,KAAK,QAAQ,GAC7B,kBAAkB,GAClBA,kBAAkB,KAAK,SAAS,GAC9B,gBAAgB,GAChB,cAAc;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CAAC;YAAA,GAtFJ5D,IAAI,CAACrE,GAAG;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAACvI,IAAI,CAAC4E,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC7E,IAAI,CAAC4E,WAAW,CAAC,kBAC7D5G,OAAA,CAAChB,MAAM,CAAC8K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEvH,QAAQ,EAAE,GAAG;UAAE4H,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB7J,OAAA;UAAK4J,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7J,OAAA,CAACX,aAAa;YAACuK,SAAS,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CvK,OAAA;YAAA6J,QAAA,gBACE7J,OAAA;cAAA6J,QAAA,EAAI;YAAqB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BvK,OAAA;cAAA6J,QAAA,EAAG;YAAuE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9EvK,OAAA;cACE4J,SAAS,EAAC,kBAAkB;cAC5Bc,OAAO,EAAEA,CAAA,KAAMpG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAqF,QAAA,EAClD;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGAzJ,mBAAmB,iBAClBd,OAAA;QACE4J,SAAS,EAAC,uBAAuB;QACjCc,OAAO,EAAG0B,CAAC,IAAK;UACd;UACA,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACK,aAAa,EAAE;YAChC;YACAL,CAAC,CAACM,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QAAA7C,QAAA,eAEF7J,OAAA;UAAK4J,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAEjD7J,OAAA;YACE4J,SAAS,EAAC,iBAAiB;YAC3Bc,OAAO,EAAEjE,0BAA2B;YACpC,cAAW,aAAa;YAAAoD,QAAA,eAExB7J,OAAA;cAAK+E,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAAC2H,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAA/C,QAAA,eACzD7J,OAAA;gBAAM6M,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGTvK,OAAA;YAAK4J,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC7J,OAAA;cAAK4J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7J,OAAA;gBAAK4J,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BvK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAC7E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAAC2H,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAA/C,QAAA,gBAClF7J,OAAA;kBAAM6M,CAAC,EAAC,wOAAwO;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAK;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1RvK,OAAA;kBAAM6M,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAClFvK,OAAA;kBAAM6M,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrFvK,OAAA;kBAAM6M,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvK,OAAA;cAAA6J,QAAA,EAAI;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BvK,OAAA;cAAA6J,QAAA,EAAG;YAA8B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAGNvK,OAAA;YAAK4J,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5B7J,OAAA;cAAK4J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7J,OAAA;gBAAK4J,SAAS,EAAC;cAA6B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDvK,OAAA;gBAAG4J,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEzI;cAAa;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGNvK,OAAA;cAAK4J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7J,OAAA;gBAAA6J,QAAA,EAAK3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;cAAK;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BvK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7J,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBvK,OAAA;oBAAA6J,QAAA,GAAS3I,YAAY,aAAZA,YAAY,wBAAAZ,qBAAA,GAAZY,YAAY,CAAEwB,eAAe,cAAApC,qBAAA,uBAA7BA,qBAAA,CAA+B6K,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAQ;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBvK,OAAA;oBAAA6J,QAAA,GAAS3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvK,OAAA;cAAK4J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B7J,OAAA;gBAAK4J,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC7J,OAAA;kBAAK+E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAAC2H,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAA/C,QAAA,eACzD7J,OAAA;oBAAM6M,CAAC,EAAC,8kBAA8kB;oBAACD,IAAI,EAAC;kBAAc;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzmB,CAAC,eACNvK,OAAA;kBAAA6J,QAAA,EAAM;gBAAgB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNvK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE7H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E;cAAW;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDvK,OAAA;gBAAK4J,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC7J,OAAA;kBAAK4J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9EvK,OAAA;kBAAK4J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtEvK,OAAA;kBAAK4J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAwC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL3I,YAAY,iBACX5B,OAAA;cAAK4J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7J,OAAA;gBAAA6J,QAAA,EAAG;cAA4B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnCvK,OAAA;gBAAQ4J,SAAS,EAAC,eAAe;gBAACc,OAAO,EAAEhE,cAAe;gBAAAmD,QAAA,gBACxD7J,OAAA;kBAAK+E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAAC2H,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAA/C,QAAA,gBACzD7J,OAAA;oBAAM6M,CAAC,EAAC,uBAAuB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7FvK,OAAA;oBAAM6M,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3G,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAKAvJ,gBAAgB,iBACfhB,OAAA;QACE4J,SAAS,EAAC,uBAAuB;QACjCc,OAAO,EAAG0B,CAAC,IAAK;UACd;UACA,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACK,aAAa,EAAE;YAChCL,CAAC,CAACM,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QAAA7C,QAAA,eAEF7J,OAAA;UAAK4J,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAE9C7J,OAAA;YACE4J,SAAS,EAAC,iBAAiB;YAC3Bc,OAAO,EAAEA,CAAA,KAAM;cACb3I,wBAAwB,CAAC,IAAI,CAAC;cAC9Bd,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACF,cAAW,aAAa;YAAA4I,QAAA,eAExB7J,OAAA;cAAK+E,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAAC2H,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAA/C,QAAA,eACzD7J,OAAA;gBAAM6M,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGTvK,OAAA;YAAK4J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7J,OAAA;cAAK4J,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B7J,OAAA;gBAAK+E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAAC2H,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAA/C,QAAA,gBACzD7J,OAAA;kBAAM6M,CAAC,EAAC,mHAAmH;kBAACD,IAAI,EAAC,SAAS;kBAACM,WAAW,EAAC;gBAAK;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9JvK,OAAA;kBAAM6M,CAAC,EAAC,sBAAsB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9GvK,OAAA;kBAAM6M,CAAC,EAAC,mHAAmH;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvK,OAAA;cAAA6J,QAAA,EAAI;YAAmB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BvK,OAAA;cAAA6J,QAAA,GAAG,aAAW,EAAC3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB,KAAK,EAAC,GAAC;YAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAENvK,OAAA;YAAK4J,SAAS,EAAC,eAAe;YAAAC,QAAA,GAE3B/H,qBAAqB,iBACpB9B,OAAA;cAAK4J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7J,OAAA;gBAAK4J,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B7J,OAAA;kBAAK+E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAAC2H,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAA/C,QAAA,gBACzD7J,OAAA;oBAAM6M,CAAC,EAAC,0HAA0H;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChMvK,OAAA;oBAAQmN,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACP,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvK,OAAA;gBAAA6J,QAAA,GAAG,wBAAsB,EAAC/H,qBAAqB,EAAC,aAAW;cAAA;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACN,eAGDvK,OAAA;cAAK4J,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7J,OAAA;gBAAA6J,QAAA,EAAI;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BvK,OAAA;gBAAK4J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7J,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBvK,OAAA;oBAAA6J,QAAA,EAAS3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;kBAAK;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAQ;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBvK,OAAA;oBAAA6J,QAAA,GAAS3I,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAW;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxBvK,OAAA;oBAAA6J,QAAA,GAAS3I,YAAY,aAAZA,YAAY,wBAAAX,sBAAA,GAAZW,YAAY,CAAEwB,eAAe,cAAAnC,sBAAA,uBAA7BA,sBAAA,CAA+B4K,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC7J,OAAA;oBAAA6J,QAAA,EAAM;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBvK,OAAA;oBAAK4J,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B7J,OAAA;sBAAK+E,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAAC2H,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAA/C,QAAA,gBACzD7J,OAAA;wBAAM6M,CAAC,EAAC,mBAAmB;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChHvK,OAAA;wBAAQmN,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACP,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,UAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvK,OAAA;cAAK4J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7J,OAAA;gBAAA6J,QAAA,EAAI;cAA4B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrCvK,OAAA;gBAAK4J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7J,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAiB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAe;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAiB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAe;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNvK,OAAA;kBAAK4J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7J,OAAA;oBAAK+E,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAAC2H,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAA/C,QAAA,gBACzD7J,OAAA;sBAAM6M,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHvK,OAAA;sBAAQmN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNvK,OAAA;oBAAA6J,QAAA,EAAM;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvK,OAAA;cAAK4J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7J,OAAA;gBACE4J,SAAS,EAAC,aAAa;gBACvBc,OAAO,EAAEA,CAAA,KAAM;kBACb3I,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;kBAC1BqD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBAAAqF,QAAA,gBAEF7J,OAAA;kBAAK+E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAAC2H,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAA/C,QAAA,gBACzD7J,OAAA;oBAAM6M,CAAC,EAAC,8KAA8K;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC9NvK,OAAA;oBAAUsN,MAAM,EAAC,uBAAuB;oBAACR,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,oBACU,EAACzI,qBAAqB,GAAI,IAAGA,qBAAsB,IAAG,GAAG,EAAE;cAAA;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACTvK,OAAA;gBACE4J,SAAS,EAAC,eAAe;gBACzBc,OAAO,EAAEA,CAAA,KAAM;kBACb3I,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;gBAC5B,CAAE;gBAAA4I,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAKDvK,OAAA,CAACH,uBAAuB;QACtB0N,OAAO,EAAEjM,sBAAuB;QAChCkM,OAAO,EAAEA,CAAA,KAAMjM,yBAAyB,CAAC,KAAK,CAAE;QAChDkM,WAAW,EAAEjN,KAAK,CAACkN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrL,GAAG,MAAKJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuI,UAAU,EAAC,KAAIvI,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyE,IAAI,CAAC;QAC/FxE,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA;MAAK;QAAAoI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGFvK,OAAA,CAACF,wBAAwB;QACvByN,OAAO,EAAE/L,gBAAiB;QAC1BgM,OAAO,EAAEA,CAAA,KAAM/L,mBAAmB,CAAC,KAAK,CAAE;QAC1CmM,OAAO,EAAErH,uBAAwB;QACjCpE,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA,IAAK;QACXxB,KAAK,EAAEA;MAAM;QAAA4J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnK,EAAA,CA3hCID,YAAY;EAAA,QAeCrB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAA8O,EAAA,GAjBxB1N,YAAY;AA6hClB,eAAeA,YAAY;AAAC,IAAA0N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}