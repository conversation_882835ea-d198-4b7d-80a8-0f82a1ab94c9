{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\"Karibu tena\", \"Habari za asubuhi\", \"Umefika salama\", \"Karibu sana\", \"Hujambo\", \"Habari za leo\", \"Karibu kwenye mafunzo\", \"Umekuja vizuri\", \"Karibu darasani\", \"Habari za masomo\"] : [\"Welcome back\", \"Great to see you\", \"Hello there\", \"Good to have you here\", \"Nice to see you again\", \"Ready to learn\", \"Let's get started\", \"Time to shine\", \"Here we go\", \"Ready for success\"];\n\n  // Rotate quotes every 4 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700'\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 w-1/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center space-x-2 sm:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: window.innerWidth <= 480 ? '24px' : '32px',\n                  height: window.innerWidth <= 480 ? '18px' : '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: window.innerWidth <= 480 ? '24px' : '32px',\n                  height: window.innerWidth <= 480 ? '24px' : '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 w-1/4\",\n            children: user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [isKiswahili ? 'Karibu' : 'Welcome', \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"UdA95gBZ4fu+PZrPZpvCNNZovfI=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "ProfilePicture", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "currentWelcome", "setCurrentWelcome", "inspiringQuotes", "welcomeMessages", "interval", "setInterval", "prev", "length", "clearInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "header", "initial", "y", "opacity", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "window", "innerWidth", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "size", "showOnlineStatus", "name", "level", "class", "firstName", "marginRight", "marginLeft", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "key", "cursor", "touchAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\n    \"Karibu tena\",\n    \"Habari za asubuhi\",\n    \"Umefika salama\",\n    \"Karibu sana\",\n    \"Hujambo\",\n    \"Habari za leo\",\n    \"Karibu kwenye mafunzo\",\n    \"Umekuja vizuri\",\n    \"Karibu darasani\",\n    \"Habari za masomo\"\n  ] : [\n    \"Welcome back\",\n    \"Great to see you\",\n    \"Hello there\",\n    \"Good to have you here\",\n    \"Nice to see you again\",\n    \"Ready to learn\",\n    \"Let's get started\",\n    \"Time to shine\",\n    \"Here we go\",\n    \"Ready for success\"\n  ];\n\n  // Rotate quotes every 4 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      {/* Header with User Profile and Notification Bell - Same as Home */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\"\n      >\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Left section - Empty for hamburger menu alignment */}\n            <div className=\"flex items-center space-x-2 w-1/4\">\n            </div>\n\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\n            <div className=\"flex-1 flex justify-center\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center space-x-2 sm:space-x-3\"\n              >\n                {/* Tanzania Flag - Using actual flag image */}\n                <div\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\n                  style={{\n                    width: window.innerWidth <= 480 ? '24px' : '32px',\n                    height: window.innerWidth <= 480 ? '18px' : '24px'\n                  }}\n                >\n                  <img\n                    src=\"https://flagcdn.com/w40/tz.png\"\n                    alt=\"Tanzania Flag\"\n                    className=\"w-full h-full object-cover\"\n                    style={{ objectFit: 'cover' }}\n                    onError={(e) => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }}\n                  />\n                </div>\n\n                {/* Amazing Animated Brainwave Text */}\n                <div className=\"relative brainwave-container\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects (no space) */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Official Logo - Small like profile */}\n                <div\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                  style={{\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: window.innerWidth <= 480 ? '24px' : '32px',\n                    height: window.innerWidth <= 480 ? '24px' : '32px'\n                  }}\n                >\n                  <img\n                    src=\"/favicon.png\"\n                    alt=\"Brainwave Logo\"\n                    className=\"w-full h-full object-cover\"\n                    style={{ objectFit: 'cover' }}\n                    onError={(e) => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }}\n                  />\n                  <div\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\n                    style={{\n                      display: 'none',\n                      fontSize: '12px'\n                    }}\n                  >\n                    🧠\n                  </div>\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n\n            {/* Right Section - User Profile */}\n            <div className=\"flex items-center justify-end space-x-2 w-1/4\">\n              {user && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                  className=\"flex items-center space-x-2 group\"\n                >\n                  {/* Profile Picture with Online Status */}\n                  <ProfilePicture\n                    user={user}\n                    size=\"sm\"\n                    showOnlineStatus={true}\n                    style={{\n                      width: '32px',\n                      height: '32px'\n                    }}\n                  />\n\n                  {/* User Name and Class */}\n                  <div className=\"hidden sm:block text-right\">\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\n                      {user?.name || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {isKiswahili ? 'Karibu' : 'Welcome'}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGzB,WAAW,CAAC,CAAC;EACxC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAKvD;EACA,MAAMoC,eAAe,GAAGL,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA,MAAMM,eAAe,GAAGN,WAAW,GAAG,CACpC,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,CACnB,GAAG,CACF,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,mBAAmB,CACpB;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMqC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCN,eAAe,CAAEO,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIJ,eAAe,CAACK,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,eAAe,CAACK,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAxC,OAAO,CAACyC,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACAnB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAMoB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAEjB,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDkB,WAAW,EAAElB,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEmB,IAAI,EAAEzC,gBAAgB;IACtB0C,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEjB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3DkB,WAAW,EAAElB,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClFmB,IAAI,EAAExC,MAAM;IACZyC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEjB,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDkB,WAAW,EAAElB,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjFmB,IAAI,EAAE5B,OAAO;IACb6B,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEjB,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCkB,WAAW,EAAElB,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EmB,IAAI,EAAEvC,WAAW;IACjBwC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEjB,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpDkB,WAAW,EAAElB,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClEmB,IAAI,EAAEjC,QAAQ;IACdkC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAEjB,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvCkB,WAAW,EAAElB,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEmB,IAAI,EAAEhC,MAAM;IACZiC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAErC,UAAU;IAChBsC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACE7B,OAAA;IAAK8B,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B/B,OAAA,CAACpB,MAAM,CAACoD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K/B,OAAA;QAAK8B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD/B,OAAA;UAAK8B,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7E/B,OAAA;YAAK8B,SAAS,EAAC;UAAmC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAGNxC,OAAA;YAAK8B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC/B,OAAA,CAACpB,MAAM,CAAC6D,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCN,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,wEAAwE;cAAAC,QAAA,gBAGlF/B,OAAA;gBACE8B,SAAS,EAAC,wEAAwE;gBAClFgB,KAAK,EAAE;kBACLC,KAAK,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDC,MAAM,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAlB,QAAA,eAEF/B,OAAA;kBACEmD,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBtB,SAAS,EAAC,4BAA4B;kBACtCgB,KAAK,EAAE;oBAAEO,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGC,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,8GAA8G;oBAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;sBACvB;sBACAF,CAAC,CAACC,MAAM,CAACV,KAAK,CAACY,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxC,OAAA;gBAAK8B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C/B,OAAA;kBAAI8B,SAAS,EAAC,gGAAgG;kBAC1GgB,KAAK,EAAE;oBACLe,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA/B,QAAA,gBAEJ/B,OAAA,CAACpB,MAAM,CAACmF,IAAI;oBACVjC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE6B,CAAC,EAAE,CAAC,EAAE;sBAAEtB,KAAK,EAAE;oBAAI,CAAE;oBAC5CN,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV6B,CAAC,EAAE,CAAC;sBACJtB,KAAK,EAAE,CAAC;sBACRuB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFtB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVoB,UAAU,EAAE;wBACVrB,QAAQ,EAAE,CAAC;wBACXsB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACV3B,KAAK,EAAE,GAAG;sBACV4B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrB3B,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLlB,KAAK,EAAE,SAAS;sBAChB2C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlC,QAAA,GACH,OAGC,eACA/B,OAAA,CAACpB,MAAM,CAAC6D,GAAG;sBACTX,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBO,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB8B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF7B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbsB,MAAM,EAAEC,QAAQ;wBAChBtB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACL0B,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdxC,OAAA,CAACpB,MAAM,CAACmF,IAAI;oBACVjC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE6B,CAAC,EAAE,EAAE;sBAAEtB,KAAK,EAAE;oBAAI,CAAE;oBAC3CN,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV6B,CAAC,EAAE,CAAC;sBACJtB,KAAK,EAAE,CAAC;sBACRR,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB+B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFtB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVX,CAAC,EAAE;wBACDU,QAAQ,EAAE,CAAC;wBACXsB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVrB,QAAQ,EAAE,GAAG;wBACbsB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACV3B,KAAK,EAAE,GAAG;sBACV4B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrB3B,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLlB,KAAK,EAAE,SAAS;sBAChB2C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAAlC,QAAA,GACH,MAGC,eACA/B,OAAA,CAACpB,MAAM,CAAC6D,GAAG;sBACTX,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB6B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd9B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBsC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF7B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXsB,MAAM,EAAEC,QAAQ;wBAChBtB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACL0B,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLxC,OAAA,CAACpB,MAAM,CAAC6D,GAAG;kBACTX,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEc,KAAK,EAAE,CAAC;oBAAEZ,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPW,KAAK,EAAE,MAAM;oBACbZ,OAAO,EAAE,CAAC;oBACVsC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF9B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACV4B,SAAS,EAAE;sBACT7B,QAAQ,EAAE,CAAC;sBACXsB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFtB,KAAK,EAAE;oBACL4B,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxC,OAAA;gBACE8B,SAAS,EAAC,gEAAgE;gBAC1EgB,KAAK,EAAE;kBACL4B,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvC1B,KAAK,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDC,MAAM,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C,CAAE;gBAAAlB,QAAA,gBAEF/B,OAAA;kBACEmD,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBtB,SAAS,EAAC,4BAA4B;kBACtCgB,KAAK,EAAE;oBAAEO,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACV,KAAK,CAACY,OAAO,GAAG,MAAM;oBAC/BH,CAAC,CAACC,MAAM,CAACmB,WAAW,CAAC7B,KAAK,CAACY,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxC,OAAA;kBACE8B,SAAS,EAAC,gHAAgH;kBAC1HgB,KAAK,EAAE;oBACLY,OAAO,EAAE,MAAM;oBACfkB,QAAQ,EAAE;kBACZ,CAAE;kBAAA7C,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxC,OAAA;gBAAK8B,SAAS,EAAC;cAAyK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNxC,OAAA;YAAK8B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC3D3B,IAAI,iBACHJ,OAAA,CAACpB,MAAM,CAAC6D,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCN,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7C/B,OAAA,CAACjB,cAAc;gBACbqB,IAAI,EAAEA,IAAK;gBACXyE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvBhC,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE;gBACV;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFxC,OAAA;gBAAK8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC/B,OAAA;kBAAK8B,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,EACnH,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,IAAI,KAAI;gBAAM;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNxC,OAAA;kBAAK8B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAC5F,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,KAAK,MAAK,SAAS,GAAI,SAAQ5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,KAAM,EAAC,GAAG7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E;gBAAK;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBxC,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/B,OAAA;QAAK8B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/B,OAAA;UAAI8B,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBxB,WAAW,GAAG,QAAQ,GAAG,SAAS,EAAC,IAAE,EAAC,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8E,SAAS,MAAI9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,IAAI,MAAKxE,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eACLxC,OAAA;UAAG8B,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBxB,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJxC,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA,CAACN,MAAM;YAACoD,KAAK,EAAE;cAAElB,KAAK,EAAE,SAAS;cAAEuD,WAAW,EAAE;YAAS;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC5B,eAAe,CAACJ,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAACoD,KAAK,EAAE;cAAElB,KAAK,EAAE,SAAS;cAAEwD,UAAU,EAAE;YAAS;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DxC,OAAA;YAAK8C,KAAK,EAAE;cAAE8B,QAAQ,EAAE,UAAU;cAAEhD,KAAK,EAAE,SAAS;cAAEyD,SAAS,EAAE;YAAS,CAAE;YAAAtD,QAAA,EAAC;UAE7E;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMNxC,OAAA;QAAK8B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/B,OAAA;UAAK8B,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAAC+D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC7D,IAAI;YAC/B,oBACE1B,OAAA,CAACpB,MAAM,CAAC6D,GAAG;cAETR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE2C,KAAK,GAAG;cAAI,CAAE;cAClD1D,SAAS,EAAG,kBAAiByD,IAAI,CAAC1D,UAAW,IAAG0D,IAAI,CAAC3D,KAAM,EAAE;cAC7D8D,OAAO,EAAEA,CAAA,KAAMvF,QAAQ,CAACoF,IAAI,CAAC5D,IAAI,CAAE;cACnCgE,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGtC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACuC,GAAG,KAAK,OAAO,IAAIvC,CAAC,CAACuC,GAAG,KAAK,GAAG,EAAE;kBACtC3F,QAAQ,CAACoF,IAAI,CAAC5D,IAAI,CAAC;gBACrB;cACF,CAAE;cACFmB,KAAK,EAAE;gBACLiD,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAAjE,QAAA,gBAIF/B,OAAA;gBAAK8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B/B,OAAA,CAACyF,aAAa;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENxC,OAAA;gBAAI8B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BwD,IAAI,CAAC/D;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELxC,OAAA;gBAAG8B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCwD,IAAI,CAAC9D;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GA9BC+C,IAAI,CAAC/D,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAACpB,MAAM,CAAC6D,GAAG;UACTR,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBQ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cf,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjC/B,OAAA;YAAK8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/B,OAAA,CAACR,eAAe;cAACsC,SAAS,EAAC;YAAuC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrExC,OAAA;cAAA+B,QAAA,EAAM;YAAkC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CxC,OAAA,CAACL,QAAQ;cAACmC,SAAS,EAAC;YAAuC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CApfID,GAAG;EAAA,QACUvB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAAmH,EAAA,GAHlChG,GAAG;AAsfT,eAAeA,GAAG;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}