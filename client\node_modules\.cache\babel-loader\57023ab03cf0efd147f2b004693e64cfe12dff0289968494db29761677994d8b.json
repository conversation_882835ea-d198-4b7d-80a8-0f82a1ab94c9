{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowRight, TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbStar, TbLogin, TbMenu2, TbX } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Refs for smooth scrolling\n  const featuresRef = useRef(null);\n  const coursesRef = useRef(null);\n  const successStoriesRef = useRef(null);\n  const contactRef = useRef(null);\n  const scrollToSection = ref => {\n    var _ref$current;\n    (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        message.error(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      message.error(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(featuresRef),\n              className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(coursesRef),\n              className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(successStoriesRef),\n              className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium shadow-lg hover:shadow-xl\",\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\",\n            children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"md:hidden py-4 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(featuresRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(coursesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Courses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(successStoriesRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Success Stories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                scrollToSection(contactRef);\n                setIsMobileMenuOpen(false);\n              },\n              className: \"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-gray-700 hover:text-blue-600 transition-colors font-medium\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium text-center\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"text-center lg:text-left\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.3\n              },\n              className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\",\n              style: {\n                fontFamily: \"'SF Pro Display', 'Inter', sans-serif\"\n              },\n              children: [\"\\uD83C\\uDF93 Study smarter,\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600\",\n                children: \"not harder.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.4\n              },\n              className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n              children: \"Access high-quality study materials, quizzes, and academic guidance\\u2014anywhere, anytime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.5\n              },\n              className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"bg-blue-600 text-white px-8 py-4 rounded-2xl hover:bg-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\",\n                children: \"Get Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(coursesRef),\n                className: \"bg-white text-gray-900 px-8 py-4 rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\",\n                children: \"Explore Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 shadow-2xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-32 h-32 mx-auto mb-6 bg-blue-600 rounded-full flex items-center justify-center shadow-xl\",\n                  children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-16 h-16 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-900 mb-4\",\n                  children: \"Real students. Real success.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Join thousands of students achieving their academic goals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 10000,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-6 h-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Study Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-6 h-6 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                end: 50,\n                duration: 2000\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), \"+\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Courses Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"99%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 font-medium\",\n              children: \"Success Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: featuresRef,\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Brainwave?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Experience the future of education with our comprehensive learning platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-8 h-8 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Quality Materials\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Access comprehensive study materials designed by education experts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-green-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-8 h-8 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Interactive Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Test your knowledge with engaging quizzes and track your progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-2xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-8 h-8 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n              children: \"Expert Guidance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-center\",\n              children: \"Get personalized academic guidance from experienced educators\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: successStoriesRef,\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Success Stories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Hear from students who transformed their academic journey with Brainwave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"Amina Hassan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Form 4 Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Brainwave helped me improve my grades significantly. The study materials are excellent!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"J\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"John Mwalimu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"Form 6 Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"The interactive quizzes made learning fun and helped me understand complex topics easily.\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.3\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-gray-50 rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold\",\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-gray-900\",\n                  children: \"Maria Kimaro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm\",\n                  children: \"University Student\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: \"\\\"Thanks to Brainwave, I passed my national exams with flying colors and got into university!\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactRef,\n      className: \"py-20 bg-gray-900 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"max-w-2xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                required: true,\n                rows: 6,\n                className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\",\n                placeholder: \"Your message...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\",\n                children: loading ? 'Sending...' : 'Send Message'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-black text-white py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-8 h-8 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold\",\n              children: \"Brainwave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 mb-6\",\n            children: \"Empowering students across Tanzania with quality education\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-8 text-sm text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"hover:text-white transition-colors\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"hover:text-white transition-colors\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactRef),\n              className: \"hover:text-white transition-colors\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8 pt-8 border-t border-gray-800 text-center text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 Brainwave. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this), \"); }; export default Home;\", /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          style: {\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            backgroundSize: '60px 60px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 1.2,\n                delay: 0.2,\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15\n              },\n              className: \"relative mb-2 sm:mb-3 md:mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 -m-8 rounded-3xl\",\n                animate: {\n                  background: ['radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)', 'radial-gradient(ellipse 120% 80% at 60% 40%, rgba(139, 92, 246, 0.04), rgba(59, 130, 246, 0.03), transparent)', 'radial-gradient(ellipse 120% 80% at 40% 60%, rgba(16, 185, 129, 0.03), rgba(139, 92, 246, 0.02), transparent)', 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)']\n                },\n                transition: {\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this), [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-1 h-1 rounded-full\",\n                style: {\n                  background: i % 2 === 0 ? '#3b82f6' : '#10b981',\n                  left: `${20 + i * 15}%`,\n                  top: `${10 + i * 10}%`\n                },\n                animate: {\n                  y: [0, -20, 0],\n                  x: [0, 10, -10, 0],\n                  opacity: [0.3, 1, 0.3],\n                  scale: [0.5, 1.2, 0.5]\n                },\n                transition: {\n                  duration: 4 + i * 0.5,\n                  repeat: Infinity,\n                  delay: i * 0.8,\n                  ease: \"easeInOut\"\n                }\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this)), /*#__PURE__*/_jsxDEV(motion.h1, {\n                className: \"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\",\n                style: {\n                  fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                  letterSpacing: '-0.02em',\n                  lineHeight: '1.1'\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block mr-4\",\n                  initial: {\n                    opacity: 0,\n                    x: -80,\n                    scale: 0.7,\n                    rotateY: -45\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    rotateY: 0,\n                    textShadow: [\"0 0 30px rgba(59, 130, 246, 0.6)\", \"0 0 60px rgba(59, 130, 246, 0.9)\", \"0 0 30px rgba(59, 130, 246, 0.6)\"]\n                  },\n                  transition: {\n                    duration: 1.8,\n                    delay: 0.4,\n                    type: \"spring\",\n                    stiffness: 80,\n                    damping: 12,\n                    textShadow: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.15,\n                    rotate: [0, -2, 2, 0],\n                    y: [-5, 0],\n                    transition: {\n                      duration: 0.6,\n                      type: \"spring\",\n                      stiffness: 300\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontWeight: '900',\n                    textShadow: '0 0 30px rgba(59, 130, 246, 0.6)',\n                    filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3))'\n                  },\n                  children: [\"Study\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -top-3 -right-3 w-4 h-4 rounded-full\",\n                    animate: {\n                      scale: [0.6, 1.4, 0.6],\n                      opacity: [0.4, 1, 0.4],\n                      rotate: [0, 360],\n                      background: ['linear-gradient(45deg, #3b82f6, #1d4ed8)', 'linear-gradient(45deg, #8b5cf6, #7c3aed)', 'linear-gradient(45deg, #3b82f6, #1d4ed8)']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      delay: 1,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',\n                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -bottom-2 -left-2 w-3 h-3 rounded-full\",\n                    animate: {\n                      scale: [0.8, 1.6, 0.8],\n                      opacity: [0.5, 1, 0.5],\n                      x: [0, 8, 0],\n                      y: [0, -6, 0],\n                      rotate: [0, -180, -360]\n                    },\n                    transition: {\n                      duration: 3.5,\n                      repeat: Infinity,\n                      delay: 0.7,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(135deg, #60a5fa, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(96, 165, 250, 0.9)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute inset-0 rounded-lg\",\n                    animate: {\n                      background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)']\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity,\n                      delay: 2,\n                      ease: \"easeInOut\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block\",\n                  initial: {\n                    opacity: 0,\n                    x: 80,\n                    scale: 0.7,\n                    rotateY: 45\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    rotateY: 0,\n                    y: [0, -4, 0, 4, 0],\n                    textShadow: [\"0 0 30px rgba(16, 185, 129, 0.6)\", \"0 0 60px rgba(16, 185, 129, 0.9)\", \"0 0 30px rgba(16, 185, 129, 0.6)\"]\n                  },\n                  transition: {\n                    duration: 1.8,\n                    delay: 0.8,\n                    type: \"spring\",\n                    stiffness: 80,\n                    damping: 12,\n                    y: {\n                      duration: 5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    textShadow: {\n                      duration: 4.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.15,\n                    rotate: [0, 2, -2, 0],\n                    y: [-8, 0],\n                    transition: {\n                      duration: 0.6,\n                      type: \"spring\",\n                      stiffness: 300\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontWeight: '900',\n                    textShadow: '0 0 30px rgba(16, 185, 129, 0.6)',\n                    filter: 'drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3))'\n                  },\n                  children: [\"Smarter\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute top-0 left-0 w-3 h-3 rounded-full\",\n                    animate: {\n                      opacity: [0, 1, 0.8, 1, 0],\n                      x: [0, 120, 240, 360, 240, 120, 0],\n                      y: [0, -15, 0, 15, 30, 15, 0],\n                      scale: [0.5, 1.2, 0.8, 1.5, 0.5],\n                      background: ['linear-gradient(45deg, #10b981, #34d399)', 'linear-gradient(45deg, #34d399, #6ee7b7)', 'linear-gradient(45deg, #6ee7b7, #10b981)', 'linear-gradient(45deg, #10b981, #34d399)']\n                    },\n                    transition: {\n                      duration: 6,\n                      repeat: Infinity,\n                      delay: 2.5,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(45deg, #10b981, #34d399)',\n                      boxShadow: '0 0 20px rgba(16, 185, 129, 0.8)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 21\n                  }, this), [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute w-1.5 h-1.5 rounded-full\",\n                    animate: {\n                      opacity: [0, 0.8, 0],\n                      x: [0, 80 + i * 20, 160 + i * 40, 240 + i * 20, 160 + i * 40, 80 + i * 20, 0],\n                      y: [0, -8 - i * 2, 0, 8 + i * 2, 16 + i * 4, 8 + i * 2, 0],\n                      scale: [0.3, 1, 0.6, 1.2, 0.3]\n                    },\n                    transition: {\n                      duration: 6,\n                      repeat: Infinity,\n                      delay: 3 + i * 0.3,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: `linear-gradient(45deg, #34d399, #6ee7b7)`,\n                      boxShadow: '0 0 10px rgba(52, 211, 153, 0.6)'\n                    }\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 23\n                  }, this)), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute inset-0 rounded-lg\",\n                    animate: {\n                      background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      delay: 3.5,\n                      ease: \"easeInOut\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\",\n                initial: {\n                  width: 0,\n                  opacity: 0\n                },\n                animate: {\n                  width: '80%',\n                  opacity: 1,\n                  boxShadow: ['0 0 20px rgba(16, 185, 129, 0.5)', '0 0 40px rgba(59, 130, 246, 0.8)', '0 0 20px rgba(16, 185, 129, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  delay: 1.5,\n                  boxShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                style: {\n                  background: 'linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6, #3b82f6)',\n                  boxShadow: '0 0 30px rgba(16, 185, 129, 0.6)'\n                },\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute inset-0 rounded-full\",\n                  animate: {\n                    background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'],\n                    x: ['-100%', '100%']\n                  },\n                  transition: {\n                    background: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    x: {\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 2\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 17\n              }, this), [...Array(4)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute top-1/2 left-1/2 rounded-full\",\n                style: {\n                  width: `${16 + i * 4}px`,\n                  height: `${16 + i * 4}px`,\n                  background: i % 2 === 0 ? 'linear-gradient(45deg, #3b82f6, #8b5cf6)' : 'linear-gradient(45deg, #10b981, #34d399)',\n                  boxShadow: i % 2 === 0 ? '0 0 20px rgba(59, 130, 246, 0.8)' : '0 0 20px rgba(16, 185, 129, 0.8)',\n                  transform: 'translate(-50%, -50%)'\n                },\n                animate: {\n                  rotate: i % 2 === 0 ? 360 : -360,\n                  x: [0, Math.cos(i * Math.PI / 2) * (120 + i * 20), 0, -Math.cos(i * Math.PI / 2) * (120 + i * 20), 0],\n                  y: [0, -Math.sin(i * Math.PI / 2) * (60 + i * 15), -(120 + i * 20), -Math.sin(i * Math.PI / 2) * (60 + i * 15), 0],\n                  opacity: [0.4, 1, 0.6, 1, 0.4],\n                  scale: [0.8, 1.2, 1, 1.3, 0.8]\n                },\n                transition: {\n                  duration: 10 + i * 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: i * 0.5\n                }\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 19\n              }, this)), [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-2 h-2 rounded-full\",\n                style: {\n                  background: `linear-gradient(45deg, ${['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]}, #ffffff)`,\n                  left: `${10 + i * 12}%`,\n                  top: `${15 + i % 3 * 25}%`,\n                  boxShadow: `0 0 10px ${['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]}`\n                },\n                animate: {\n                  y: [0, -30, 0],\n                  x: [0, Math.sin(i) * 20, 0],\n                  opacity: [0, 1, 0],\n                  scale: [0.3, 1, 0.3],\n                  rotate: [0, 180, 360]\n                },\n                transition: {\n                  duration: 3 + i * 0.3,\n                  repeat: Infinity,\n                  delay: i * 0.4,\n                  ease: \"easeInOut\"\n                }\n              }, `sparkle-${i}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.8\n              },\n              className: \"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\",\n              style: {\n                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\n                boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\n                border: '2px solid #FFD700'\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\",\n                animate: {\n                  x: ['-100%', '100%']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5 mr-2 text-orange-800 relative z-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-orange-900 relative z-10 font-black\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                animate: {\n                  boxShadow: ['0 0 20px rgba(255, 215, 0, 0.5)', '0 0 40px rgba(255, 215, 0, 0.8)', '0 0 20px rgba(255, 215, 0, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.9\n              },\n              className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\",\n              children: !user ? /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/register\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUserPlus, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1118,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Register Now\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1119,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1117,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbLogin, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1131,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Login\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1132,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1130,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/user/hub\",\n                className: \"w-full sm:w-auto\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1146,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm sm:text-base\",\n                      children: \"Go to Hub\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1147,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1099,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"grid grid-cols-4 gap-1 sm:gap-2 md:gap-3 pt-3 sm:pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-blue-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"10K+\",\n                    duration: 3000,\n                    delay: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1163,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-purple-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"500+\",\n                    duration: 3500,\n                    delay: 700\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-green-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"95%\",\n                    duration: 4000,\n                    delay: 900\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1175,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-orange-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"50K+\",\n                    duration: 4500,\n                    delay: 1100\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1181,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Study Materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative mt-6 xs:mt-8 sm:mt-10 lg:mt-0 order-1 lg:order-2 px-3 sm:px-4 lg:px-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Image1,\n                  alt: \"Students Learning\",\n                  className: \"w-full h-auto rounded-xl sm:rounded-2xl\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                className: \"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-5, 5, -5]\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                className: \"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl mx-3 sm:mx-4 md:mx-6 lg:mx-8 mt-6 sm:mt-8 md:mt-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8\",\n            children: [{\n              number: \"15K+\",\n              text: \"Active Students\",\n              icon: TbUsers,\n              color: \"from-blue-500 to-blue-600\"\n            }, {\n              number: \"500+\",\n              text: \"Expert Teachers\",\n              icon: TbSchool,\n              color: \"from-green-500 to-green-600\"\n            }, {\n              number: \"1000+\",\n              text: \"Video Lessons\",\n              icon: TbBook,\n              color: \"from-purple-500 to-purple-600\"\n            }, {\n              number: \"98%\",\n              text: \"Success Rate\",\n              icon: TbTrophy,\n              color: \"from-orange-500 to-orange-600\"\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30,\n                scale: 0.9\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              whileHover: {\n                scale: 1.05,\n                y: -5\n              },\n              className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n                children: stat.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1255,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"bg-gray-50 py-8 sm:py-12 md:py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Students Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Hear from thousands of students who have transformed their learning journey with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\",\n          children: [{\n            name: \"Amina Hassan\",\n            class: \"Form 4\",\n            rating: 5,\n            text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\n            avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-pink-400 to-purple-500\"\n          }, {\n            name: \"John Mwalimu\",\n            class: \"Class 7\",\n            rating: 5,\n            text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\n            avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-blue-400 to-indigo-500\"\n          }, {\n            name: \"Fatuma Said\",\n            class: \"Form 2\",\n            rating: 5,\n            text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\n            avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-green-400 to-teal-500\"\n          }].map((review, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-gray-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1334,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [...Array(review.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    scale: 0,\n                    rotate: -180\n                  },\n                  whileInView: {\n                    scale: 1,\n                    rotate: 0\n                  },\n                  transition: {\n                    duration: 0.3,\n                    delay: i * 0.1\n                  },\n                  viewport: {\n                    once: true\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 text-yellow-500 drop-shadow-sm\",\n                    style: {\n                      fill: '#FFD700',\n                      color: '#FFD700'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1348,\n                    columnNumber: 25\n                  }, this)\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400 font-medium\",\n                children: \"Verified Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1352,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-6 leading-relaxed font-medium relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -top-2 -left-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"relative z-10\",\n                children: review.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -bottom-4 -right-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative mr-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`,\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: review.avatar,\n                    alt: review.name,\n                    className: \"w-full h-full rounded-full object-cover border-2 border-white\",\n                    onError: e => {\n                      // Fallback to initials if image fails to load\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1365,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\",\n                    style: {\n                      display: 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600 font-semibold text-sm\",\n                      children: review.name.split(' ').map(n => n[0]).join('')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1377,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900 text-lg\",\n                    children: review.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-blue-500\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1393,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: review.class\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1398,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1399,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Verified Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1400,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1320,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"bg-white py-8 sm:py-12 md:py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: [\"Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"+255 655 285 549\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 139\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1422,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4 sm:space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Your Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1437,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your full name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your email\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                rows: 5,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                placeholder: \"Tell us how we can help you...\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1469,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"tel:+255655285549\",\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCDE Call Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1498,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1511,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCAC WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1513,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1479,\n              columnNumber: 15\n            }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n              children: responseMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1411,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1410,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gray-900 text-white py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"BRAIN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-green-400\",\n              children: \"WAVE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm leading-relaxed max-w-2xl mx-auto\",\n            children: \"Tanzania's premier educational platform empowering students to achieve academic excellence through innovative learning solutions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1536,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-400\",\n                  children: \"\\xA9 2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-white\",\n                  children: \"Brainwave.zone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1547,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-blue-400 font-medium\",\n                children: \"Study Smarter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Kigamboni - Dar es Salaam, Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1550,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-6 text-sm text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-center text-xs text-gray-500\",\n            children: \"Empowering Tanzanian students since 2022 \\u2022 All rights reserved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1560,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1529,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1528,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0,\n        rotate: -180\n      },\n      animate: {\n        scale: 1,\n        rotate: 0\n      },\n      transition: {\n        duration: 0.5,\n        delay: 1\n      },\n      className: \"fixed bottom-6 right-6 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.a, {\n        href: \"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\",\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"group flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-white\",\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-8 h-8 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\",\n          children: [\"Chat with us on WhatsApp\", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1595,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1576,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1570,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"g7KKyuBBBvcIXU/tEQbEtuqSJCU=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useNavigate", "motion", "TbArrowRight", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbStar", "<PERSON>b<PERSON><PERSON><PERSON>", "TbMenu2", "TbX", "message", "useSelector", "contactUs", "Animated<PERSON>ounter", "jsxDEV", "_jsxDEV", "Home", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "user", "state", "navigate", "featuresRef", "coursesRef", "successStoriesRef", "contactRef", "scrollToSection", "ref", "_ref$current", "current", "scrollIntoView", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "nav", "initial", "y", "opacity", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "to", "div", "x", "transition", "duration", "delay", "h1", "style", "fontFamily", "p", "whileInView", "viewport", "once", "end", "onSubmit", "type", "onChange", "required", "placeholder", "rows", "disabled", "backgroundImage", "backgroundSize", "scale", "stiffness", "damping", "background", "repeat", "Infinity", "ease", "Array", "map", "_", "i", "left", "top", "letterSpacing", "lineHeight", "span", "rotateY", "textShadow", "whileHover", "rotate", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "fontWeight", "filter", "boxShadow", "width", "height", "transform", "Math", "cos", "PI", "sin", "border", "Fragment", "button", "whileTap", "TbUserPlus", "TbArrowBigRightLinesFilled", "src", "Image1", "alt", "number", "text", "icon", "color", "stat", "index", "reviewsSectionRef", "class", "rating", "avatar", "bgColor", "review", "fill", "viewBox", "d", "onError", "display", "nextS<PERSON>ling", "split", "n", "join", "contactUsRef", "a", "href", "rel", "responseMessage", "includes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowRight,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbLogin,\r\n  TbMenu2,\r\n  TbX\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\r\n\r\n\r\nconst Home = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Refs for smooth scrolling\r\n  const featuresRef = useRef(null);\r\n  const coursesRef = useRef(null);\r\n  const successStoriesRef = useRef(null);\r\n  const contactRef = useRef(null);\r\n\r\n  const scrollToSection = (ref) => {\r\n    ref.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        message.error(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\r\n      {/* Apple-Inspired Navbar */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Logo */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <TbBrain className=\"w-8 h-8 text-blue-600\" />\r\n              <span className=\"text-xl font-bold text-gray-900\">Brainwave</span>\r\n            </div>\r\n\r\n            {/* Desktop Menu */}\r\n            <div className=\"hidden md:flex items-center space-x-8\">\r\n              <button\r\n                onClick={() => scrollToSection(featuresRef)}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n              >\r\n                Features\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(coursesRef)}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n              >\r\n                Courses\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(successStoriesRef)}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n              >\r\n                Success Stories\r\n              </button>\r\n              <button\r\n                onClick={() => scrollToSection(contactRef)}\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n              >\r\n                Contact\r\n              </button>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n              >\r\n                Login\r\n              </Link>\r\n              <Link\r\n                to=\"/register\"\r\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium shadow-lg hover:shadow-xl\"\r\n              >\r\n                Get Started\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n              className=\"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n            >\r\n              {isMobileMenuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile Menu */}\r\n          {isMobileMenuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"md:hidden py-4 border-t border-gray-200\"\r\n            >\r\n              <div className=\"flex flex-col space-y-4\">\r\n                <button\r\n                  onClick={() => { scrollToSection(featuresRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n                >\r\n                  Features\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(coursesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n                >\r\n                  Courses\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(successStoriesRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n                >\r\n                  Success Stories\r\n                </button>\r\n                <button\r\n                  onClick={() => { scrollToSection(contactRef); setIsMobileMenuOpen(false); }}\r\n                  className=\"text-left text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n                >\r\n                  Contact\r\n                </button>\r\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\"\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors font-medium text-center\"\r\n                  >\r\n                    Get Started\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n      {/* Hero Section */}\r\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Column - Text Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"text-center lg:text-left\"\r\n            >\r\n              {/* Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6\"\r\n              >\r\n                <TbTrophy className=\"w-4 h-4\" />\r\n                <span>#1 Educational Platform in Tanzania</span>\r\n              </motion.div>\r\n\r\n              {/* Main Heading */}\r\n              <motion.h1\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.3 }}\r\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\"\r\n                style={{ fontFamily: \"'SF Pro Display', 'Inter', sans-serif\" }}\r\n              >\r\n                🎓 Study smarter,{' '}\r\n                <span className=\"text-blue-600\">not harder.</span>\r\n              </motion.h1>\r\n\r\n              {/* Subtitle */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.4 }}\r\n                className=\"text-xl text-gray-600 mb-8 leading-relaxed\"\r\n              >\r\n                Access high-quality study materials, quizzes, and academic guidance—anywhere, anytime.\r\n              </motion.p>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.5 }}\r\n                className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\r\n              >\r\n                <Link\r\n                  to=\"/register\"\r\n                  className=\"bg-blue-600 text-white px-8 py-4 rounded-2xl hover:bg-blue-700 transition-all duration-300 font-semibold text-lg shadow-xl hover:shadow-2xl hover:scale-105\"\r\n                >\r\n                  Get Started\r\n                </Link>\r\n                <button\r\n                  onClick={() => scrollToSection(coursesRef)}\r\n                  className=\"bg-white text-gray-900 px-8 py-4 rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl hover:scale-105\"\r\n                >\r\n                  Explore Courses\r\n                </button>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Column - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative\"\r\n            >\r\n              <div className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 shadow-2xl\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"w-32 h-32 mx-auto mb-6 bg-blue-600 rounded-full flex items-center justify-center shadow-xl\">\r\n                    <TbBrain className=\"w-16 h-16 text-white\" />\r\n                  </div>\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Real students. Real success.</h3>\r\n                  <p className=\"text-gray-600\">Join thousands of students achieving their academic goals</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Quick Stats Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\"\r\n          >\r\n            {/* Stat 1 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                <TbUsers className=\"w-6 h-6 text-blue-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={10000} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Students</p>\r\n            </div>\r\n\r\n            {/* Stat 2 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center\">\r\n                <TbBook className=\"w-6 h-6 text-green-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Study Materials</p>\r\n            </div>\r\n\r\n            {/* Stat 3 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center\">\r\n                <TbSchool className=\"w-6 h-6 text-purple-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n                <AnimatedCounter end={50} duration={2000} />+\r\n              </div>\r\n              <p className=\"text-gray-600 font-medium\">Courses Available</p>\r\n            </div>\r\n\r\n            {/* Stat 4 */}\r\n            <div className=\"text-center bg-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow\">\r\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center\">\r\n                <TbTrophy className=\"w-6 h-6 text-yellow-600\" />\r\n              </div>\r\n              <div className=\"text-3xl font-bold text-gray-900 mb-2\">99%</div>\r\n              <p className=\"text-gray-600 font-medium\">Success Rate</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n      {/* Features Section */}\r\n      <section ref={featuresRef} className=\"py-20 bg-gray-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Why Choose Brainwave?</h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Experience the future of education with our comprehensive learning platform\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {/* Feature 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-2xl flex items-center justify-center\">\r\n                <TbBook className=\"w-8 h-8 text-blue-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Quality Materials</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Access comprehensive study materials designed by education experts\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-green-100 rounded-2xl flex items-center justify-center\">\r\n                <TbTrophy className=\"w-8 h-8 text-green-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Interactive Quizzes</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Test your knowledge with engaging quizzes and track your progress\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Feature 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\r\n            >\r\n              <div className=\"w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-2xl flex items-center justify-center\">\r\n                <TbStar className=\"w-8 h-8 text-purple-600\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">Expert Guidance</h3>\r\n              <p className=\"text-gray-600 text-center\">\r\n                Get personalized academic guidance from experienced educators\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Success Stories Section */}\r\n      <section ref={successStoriesRef} className=\"py-20 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Success Stories</h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Hear from students who transformed their academic journey with Brainwave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {/* Testimonial 1 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  A\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">Amina Hassan</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Form 4 Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Brainwave helped me improve my grades significantly. The study materials are excellent!\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 2 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  J\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">John Mwalimu</h4>\r\n                  <p className=\"text-gray-600 text-sm\">Form 6 Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"The interactive quizzes made learning fun and helped me understand complex topics easily.\"\r\n              </p>\r\n            </motion.div>\r\n\r\n            {/* Testimonial 3 */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-gray-50 rounded-2xl p-6 shadow-lg\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                  M\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <h4 className=\"font-bold text-gray-900\">Maria Kimaro</h4>\r\n                  <p className=\"text-gray-600 text-sm\">University Student</p>\r\n                </div>\r\n              </div>\r\n              <p className=\"text-gray-700 italic\">\r\n                \"Thanks to Brainwave, I passed my national exams with flying colors and got into university!\"\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactRef} className=\"py-20 bg-gray-900 text-white\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl font-bold mb-4\">Get in Touch</h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"max-w-2xl mx-auto\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              <div className=\"grid md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"Your name\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                    placeholder=\"<EMAIL>\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium mb-2\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                  rows={6}\r\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white\"\r\n                  placeholder=\"Your message...\"\r\n                />\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-lg shadow-xl hover:shadow-2xl disabled:opacity-50\"\r\n                >\r\n                  {loading ? 'Sending...' : 'Send Message'}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-black text-white py-12\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center\">\r\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\r\n              <TbBrain className=\"w-8 h-8 text-blue-500\" />\r\n              <span className=\"text-2xl font-bold\">Brainwave</span>\r\n            </div>\r\n            <p className=\"text-gray-400 mb-6\">\r\n              Empowering students across Tanzania with quality education\r\n            </p>\r\n            <div className=\"flex justify-center space-x-8 text-sm text-gray-400\">\r\n              <Link to=\"/login\" className=\"hover:text-white transition-colors\">Login</Link>\r\n              <Link to=\"/register\" className=\"hover:text-white transition-colors\">Register</Link>\r\n              <button onClick={() => scrollToSection(contactRef)} className=\"hover:text-white transition-colors\">Contact</button>\r\n            </div>\r\n            <div className=\"mt-8 pt-8 border-t border-gray-800 text-center text-gray-500\">\r\n              <p>&copy; 2024 Brainwave. All rights reserved.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n\r\n      {/* PROFESSIONAL HERO SECTION */}\r\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-5\">\r\n          <div className=\"absolute inset-0\" style={{\r\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\r\n            backgroundSize: '60px 60px'\r\n          }}></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\">\r\n\r\n\r\n            {/* Left Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\"\r\n            >\r\n              {/* Premium Animated Study Smarter Text */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{\r\n                  duration: 1.2,\r\n                  delay: 0.2,\r\n                  type: \"spring\",\r\n                  stiffness: 100,\r\n                  damping: 15\r\n                }}\r\n                className=\"relative mb-2 sm:mb-3 md:mb-4\"\r\n              >\r\n                {/* Premium Background Effects */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 -m-8 rounded-3xl\"\r\n                  animate={{\r\n                    background: [\r\n                      'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 60% 40%, rgba(139, 92, 246, 0.04), rgba(59, 130, 246, 0.03), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 40% 60%, rgba(16, 185, 129, 0.03), rgba(139, 92, 246, 0.02), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 8,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                />\r\n\r\n                {/* Floating Particles */}\r\n                {[...Array(6)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    className=\"absolute w-1 h-1 rounded-full\"\r\n                    style={{\r\n                      background: i % 2 === 0 ? '#3b82f6' : '#10b981',\r\n                      left: `${20 + i * 15}%`,\r\n                      top: `${10 + i * 10}%`\r\n                    }}\r\n                    animate={{\r\n                      y: [0, -20, 0],\r\n                      x: [0, 10, -10, 0],\r\n                      opacity: [0.3, 1, 0.3],\r\n                      scale: [0.5, 1.2, 0.5]\r\n                    }}\r\n                    transition={{\r\n                      duration: 4 + i * 0.5,\r\n                      repeat: Infinity,\r\n                      delay: i * 0.8,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                ))}\r\n\r\n                <motion.h1\r\n                  className=\"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\"\r\n                  style={{\r\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                    letterSpacing: '-0.02em',\r\n                    lineHeight: '1.1'\r\n                  }}\r\n                >\r\n                  {/* Study - with premium effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block mr-4\"\r\n                    initial={{ opacity: 0, x: -80, scale: 0.7, rotateY: -45 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      rotateY: 0,\r\n                      textShadow: [\r\n                        \"0 0 30px rgba(59, 130, 246, 0.6)\",\r\n                        \"0 0 60px rgba(59, 130, 246, 0.9)\",\r\n                        \"0 0 30px rgba(59, 130, 246, 0.6)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.8,\r\n                      delay: 0.4,\r\n                      type: \"spring\",\r\n                      stiffness: 80,\r\n                      damping: 12,\r\n                      textShadow: {\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.15,\r\n                      rotate: [0, -2, 2, 0],\r\n                      y: [-5, 0],\r\n                      transition: {\r\n                        duration: 0.6,\r\n                        type: \"spring\",\r\n                        stiffness: 300\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)',\r\n                      WebkitBackgroundClip: 'text',\r\n                      WebkitTextFillColor: 'transparent',\r\n                      backgroundClip: 'text',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 30px rgba(59, 130, 246, 0.6)',\r\n                      filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3))'\r\n                    }}\r\n                  >\r\n                    Study\r\n\r\n                    {/* Premium floating elements around Study */}\r\n                    <motion.div\r\n                      className=\"absolute -top-3 -right-3 w-4 h-4 rounded-full\"\r\n                      animate={{\r\n                        scale: [0.6, 1.4, 0.6],\r\n                        opacity: [0.4, 1, 0.4],\r\n                        rotate: [0, 360],\r\n                        background: [\r\n                          'linear-gradient(45deg, #3b82f6, #1d4ed8)',\r\n                          'linear-gradient(45deg, #8b5cf6, #7c3aed)',\r\n                          'linear-gradient(45deg, #3b82f6, #1d4ed8)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        delay: 1,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',\r\n                        boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)'\r\n                      }}\r\n                    />\r\n\r\n                    <motion.div\r\n                      className=\"absolute -bottom-2 -left-2 w-3 h-3 rounded-full\"\r\n                      animate={{\r\n                        scale: [0.8, 1.6, 0.8],\r\n                        opacity: [0.5, 1, 0.5],\r\n                        x: [0, 8, 0],\r\n                        y: [0, -6, 0],\r\n                        rotate: [0, -180, -360]\r\n                      }}\r\n                      transition={{\r\n                        duration: 3.5,\r\n                        repeat: Infinity,\r\n                        delay: 0.7,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(135deg, #60a5fa, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(96, 165, 250, 0.9)'\r\n                      }}\r\n                    />\r\n\r\n                    {/* Premium light streak effect */}\r\n                    <motion.div\r\n                      className=\"absolute inset-0 rounded-lg\"\r\n                      animate={{\r\n                        background: [\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 3,\r\n                        repeat: Infinity,\r\n                        delay: 2,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n\r\n                  {/* Smarter - with premium flowing effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block\"\r\n                    initial={{ opacity: 0, x: 80, scale: 0.7, rotateY: 45 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      rotateY: 0,\r\n                      y: [0, -4, 0, 4, 0],\r\n                      textShadow: [\r\n                        \"0 0 30px rgba(16, 185, 129, 0.6)\",\r\n                        \"0 0 60px rgba(16, 185, 129, 0.9)\",\r\n                        \"0 0 30px rgba(16, 185, 129, 0.6)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.8,\r\n                      delay: 0.8,\r\n                      type: \"spring\",\r\n                      stiffness: 80,\r\n                      damping: 12,\r\n                      y: {\r\n                        duration: 5,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      },\r\n                      textShadow: {\r\n                        duration: 4.5,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.15,\r\n                      rotate: [0, 2, -2, 0],\r\n                      y: [-8, 0],\r\n                      transition: {\r\n                        duration: 0.6,\r\n                        type: \"spring\",\r\n                        stiffness: 300\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\r\n                      WebkitBackgroundClip: 'text',\r\n                      WebkitTextFillColor: 'transparent',\r\n                      backgroundClip: 'text',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 30px rgba(16, 185, 129, 0.6)',\r\n                      filter: 'drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3))'\r\n                    }}\r\n                  >\r\n                    Smarter\r\n\r\n                    {/* Premium animated orbital line around Smarter */}\r\n                    <motion.div\r\n                      className=\"absolute top-0 left-0 w-3 h-3 rounded-full\"\r\n                      animate={{\r\n                        opacity: [0, 1, 0.8, 1, 0],\r\n                        x: [0, 120, 240, 360, 240, 120, 0],\r\n                        y: [0, -15, 0, 15, 30, 15, 0],\r\n                        scale: [0.5, 1.2, 0.8, 1.5, 0.5],\r\n                        background: [\r\n                          'linear-gradient(45deg, #10b981, #34d399)',\r\n                          'linear-gradient(45deg, #34d399, #6ee7b7)',\r\n                          'linear-gradient(45deg, #6ee7b7, #10b981)',\r\n                          'linear-gradient(45deg, #10b981, #34d399)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 6,\r\n                        repeat: Infinity,\r\n                        delay: 2.5,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(45deg, #10b981, #34d399)',\r\n                        boxShadow: '0 0 20px rgba(16, 185, 129, 0.8)'\r\n                      }}\r\n                    />\r\n\r\n                    {/* Premium trailing particles */}\r\n                    {[...Array(3)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        className=\"absolute w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 0.8, 0],\r\n                          x: [0, 80 + i * 20, 160 + i * 40, 240 + i * 20, 160 + i * 40, 80 + i * 20, 0],\r\n                          y: [0, -8 - i * 2, 0, 8 + i * 2, 16 + i * 4, 8 + i * 2, 0],\r\n                          scale: [0.3, 1, 0.6, 1.2, 0.3]\r\n                        }}\r\n                        transition={{\r\n                          duration: 6,\r\n                          repeat: Infinity,\r\n                          delay: 3 + i * 0.3,\r\n                          ease: \"easeInOut\"\r\n                        }}\r\n                        style={{\r\n                          background: `linear-gradient(45deg, #34d399, #6ee7b7)`,\r\n                          boxShadow: '0 0 10px rgba(52, 211, 153, 0.6)'\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                    {/* Premium light sweep effect */}\r\n                    <motion.div\r\n                      className=\"absolute inset-0 rounded-lg\"\r\n                      animate={{\r\n                        background: [\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        delay: 3.5,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n                </motion.h1>\r\n\r\n                {/* Glowing underline effect */}\r\n                <motion.div\r\n                  className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\"\r\n                  initial={{ width: 0, opacity: 0 }}\r\n                  animate={{\r\n                    width: '80%',\r\n                    opacity: 1,\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)',\r\n                      '0 0 40px rgba(59, 130, 246, 0.8)',\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    delay: 1.5,\r\n                    boxShadow: {\r\n                      duration: 3,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }\r\n                  }}\r\n                  style={{\r\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6, #3b82f6)',\r\n                    boxShadow: '0 0 30px rgba(16, 185, 129, 0.6)'\r\n                  }}\r\n                >\r\n                  {/* Moving light effect inside underline */}\r\n                  <motion.div\r\n                    className=\"absolute inset-0 rounded-full\"\r\n                    animate={{\r\n                      background: [\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                      ],\r\n                      x: ['-100%', '100%']\r\n                    }}\r\n                    transition={{\r\n                      background: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      },\r\n                      x: {\r\n                        duration: 3,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\",\r\n                        delay: 2\r\n                      }\r\n                    }}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Premium orbiting elements around the text */}\r\n                {[...Array(4)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    className=\"absolute top-1/2 left-1/2 rounded-full\"\r\n                    style={{\r\n                      width: `${16 + i * 4}px`,\r\n                      height: `${16 + i * 4}px`,\r\n                      background: i % 2 === 0\r\n                        ? 'linear-gradient(45deg, #3b82f6, #8b5cf6)'\r\n                        : 'linear-gradient(45deg, #10b981, #34d399)',\r\n                      boxShadow: i % 2 === 0\r\n                        ? '0 0 20px rgba(59, 130, 246, 0.8)'\r\n                        : '0 0 20px rgba(16, 185, 129, 0.8)',\r\n                      transform: 'translate(-50%, -50%)'\r\n                    }}\r\n                    animate={{\r\n                      rotate: i % 2 === 0 ? 360 : -360,\r\n                      x: [\r\n                        0,\r\n                        Math.cos((i * Math.PI) / 2) * (120 + i * 20),\r\n                        0,\r\n                        -Math.cos((i * Math.PI) / 2) * (120 + i * 20),\r\n                        0\r\n                      ],\r\n                      y: [\r\n                        0,\r\n                        -Math.sin((i * Math.PI) / 2) * (60 + i * 15),\r\n                        -(120 + i * 20),\r\n                        -Math.sin((i * Math.PI) / 2) * (60 + i * 15),\r\n                        0\r\n                      ],\r\n                      opacity: [0.4, 1, 0.6, 1, 0.4],\r\n                      scale: [0.8, 1.2, 1, 1.3, 0.8]\r\n                    }}\r\n                    transition={{\r\n                      duration: 10 + i * 2,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\",\r\n                      delay: i * 0.5\r\n                    }}\r\n                  />\r\n                ))}\r\n\r\n                {/* Premium floating sparkles */}\r\n                {[...Array(8)].map((_, i) => (\r\n                  <motion.div\r\n                    key={`sparkle-${i}`}\r\n                    className=\"absolute w-2 h-2 rounded-full\"\r\n                    style={{\r\n                      background: `linear-gradient(45deg, ${\r\n                        ['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]\r\n                      }, #ffffff)`,\r\n                      left: `${10 + i * 12}%`,\r\n                      top: `${15 + (i % 3) * 25}%`,\r\n                      boxShadow: `0 0 10px ${\r\n                        ['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]\r\n                      }`\r\n                    }}\r\n                    animate={{\r\n                      y: [0, -30, 0],\r\n                      x: [0, Math.sin(i) * 20, 0],\r\n                      opacity: [0, 1, 0],\r\n                      scale: [0.3, 1, 0.3],\r\n                      rotate: [0, 180, 360]\r\n                    }}\r\n                    transition={{\r\n                      duration: 3 + i * 0.3,\r\n                      repeat: Infinity,\r\n                      delay: i * 0.4,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                ))}\r\n              </motion.div>\r\n\r\n              {/* Highlighted Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.8 }}\r\n                className=\"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\"\r\n                style={{\r\n                  background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\r\n                  boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\r\n                  border: '2px solid #FFD700'\r\n                }}\r\n              >\r\n                {/* Animated background shimmer */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\"\r\n                  animate={{\r\n                    x: ['-100%', '100%']\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\"\r\n                  }}\r\n                />\r\n\r\n                <TbSchool className=\"w-5 h-5 mr-2 text-orange-800 relative z-10\" />\r\n                <span className=\"text-orange-900 relative z-10 font-black\">\r\n                  #1 Educational Platform in Tanzania\r\n                </span>\r\n\r\n                {/* Glowing border effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 rounded-full\"\r\n                  animate={{\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)',\r\n                      '0 0 40px rgba(255, 215, 0, 0.8)',\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Description */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </motion.p>\r\n\r\n              {/* Action Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.9 }}\r\n                className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                {!user ? (\r\n                  <React.Fragment>\r\n\r\n\r\n                    {/* Register and Login Buttons - Super Responsive */}\r\n                    <div className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\">\r\n                      <Link to=\"/register\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbUserPlus className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Register Now</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n\r\n                      <Link to=\"/login\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbLogin className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Login</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n                    </div>\r\n                  </React.Fragment>\r\n                ) : (\r\n                  <Link to=\"/user/hub\" className=\"w-full sm:w-auto\">\r\n                    <motion.button\r\n                      className=\"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbArrowBigRightLinesFilled className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                        <span className=\"text-sm sm:text-base\">Go to Hub</span>\r\n                      </div>\r\n                    </motion.button>\r\n                  </Link>\r\n                )}\r\n              </motion.div>\r\n\r\n              {/* Stats */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"grid grid-cols-4 gap-1 sm:gap-2 md:gap-3 pt-3 sm:pt-4\"\r\n              >\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-blue-600\">\r\n                    <AnimatedCounter end=\"10K+\" duration={3000} delay={500} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Students</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-purple-600\">\r\n                    <AnimatedCounter end=\"500+\" duration={3500} delay={700} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Courses</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-green-600\">\r\n                    <AnimatedCounter end=\"95%\" duration={4000} delay={900} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Success Rate</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-orange-600\">\r\n                    <AnimatedCounter end=\"50K+\" duration={4500} delay={1100} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Study Materials</div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Content - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative mt-6 xs:mt-8 sm:mt-10 lg:mt-0 order-1 lg:order-2 px-3 sm:px-4 lg:px-0\"\r\n            >\r\n              <div className=\"relative\">\r\n                {/* Main Image */}\r\n                <div className=\"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\">\r\n                  <img\r\n                    src={Image1}\r\n                    alt=\"Students Learning\"\r\n                    className=\"w-full h-auto rounded-xl sm:rounded-2xl\"\r\n                    loading=\"lazy\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\r\n                  className=\"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbTrophy className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\r\n                  className=\"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbBook className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [-5, 5, -5] }}\r\n                  transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 }}\r\n                  className=\"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\"\r\n                >\r\n                  <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\" />\r\n                </motion.div>\r\n\r\n                {/* Background Decoration */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"></div>\r\n              </div>\r\n            </motion.div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Section - PART OF HERO SECTION */}\r\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl mx-3 sm:mx-4 md:mx-6 lg:mx-8 mt-6 sm:mt-8 md:mt-10\">\r\n          <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8\"\r\n            >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n        </div>\r\n      </section>\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"bg-gray-50 py-8 sm:py-12 md:py-16\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              What Our Students Say\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Hear from thousands of students who have transformed their learning journey with BrainWave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\">\r\n            {[\r\n              {\r\n                name: \"Amina Hassan\",\r\n                class: \"Form 4\",\r\n                rating: 5,\r\n                text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\r\n                avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-pink-400 to-purple-500\"\r\n              },\r\n              {\r\n                name: \"John Mwalimu\",\r\n                class: \"Class 7\",\r\n                rating: 5,\r\n                text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\r\n                avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-blue-400 to-indigo-500\"\r\n              },\r\n              {\r\n                name: \"Fatuma Said\",\r\n                class: \"Form 2\",\r\n                rating: 5,\r\n                text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\r\n                avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-green-400 to-teal-500\"\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\"\r\n              >\r\n                {/* Premium Background Gradient */}\r\n                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`}></div>\r\n\r\n                {/* Floating Quote Icon */}\r\n                <div className=\"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\">\r\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"/>\r\n                  </svg>\r\n                </div>\r\n                {/* Premium Star Rating */}\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    {[...Array(review.rating)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        initial={{ scale: 0, rotate: -180 }}\r\n                        whileInView={{ scale: 1, rotate: 0 }}\r\n                        transition={{ duration: 0.3, delay: i * 0.1 }}\r\n                        viewport={{ once: true }}\r\n                      >\r\n                        <TbStar className=\"w-5 h-5 text-yellow-500 drop-shadow-sm\" style={{ fill: '#FFD700', color: '#FFD700' }} />\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400 font-medium\">Verified Review</div>\r\n                </div>\r\n                {/* Premium Review Text */}\r\n                <p className=\"text-gray-700 mb-6 leading-relaxed font-medium relative\">\r\n                  <span className=\"text-2xl text-blue-200 absolute -top-2 -left-1\">\"</span>\r\n                  <span className=\"relative z-10\">{review.text}</span>\r\n                  <span className=\"text-2xl text-blue-200 absolute -bottom-4 -right-1\">\"</span>\r\n                </p>\r\n                {/* Premium Profile Section */}\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"relative mr-4\">\r\n                    {/* Profile Picture with Premium Border */}\r\n                    <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`}>\r\n                      <img\r\n                        src={review.avatar}\r\n                        alt={review.name}\r\n                        className=\"w-full h-full rounded-full object-cover border-2 border-white\"\r\n                        onError={(e) => {\r\n                          // Fallback to initials if image fails to load\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                      {/* Fallback initials */}\r\n                      <div className=\"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\" style={{display: 'none'}}>\r\n                        <span className=\"text-gray-600 font-semibold text-sm\">\r\n                          {review.name.split(' ').map(n => n[0]).join('')}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Online Status Indicator */}\r\n                    <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"></div>\r\n                  </div>\r\n\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <h4 className=\"font-bold text-gray-900 text-lg\">{review.name}</h4>\r\n                      {/* Verified Badge */}\r\n                      <div className=\"flex items-center\">\r\n                        <svg className=\"w-4 h-4 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                        </svg>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-1\">\r\n                      <span className=\"text-sm font-medium text-gray-600\">{review.class}</span>\r\n                      <span className=\"text-xs text-gray-400\">•</span>\r\n                      <span className=\"text-xs text-gray-500\">Verified Student</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"bg-white py-8 sm:py-12 md:py-16\">\r\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Get in Touch\r\n            </h2>\r\n            <p className=\"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at <strong>+255 655 285 549</strong>.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\r\n              <div className=\"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Your Name\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your full name\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Email Address\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your email\"\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Message\r\n                </label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  rows={5}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"Tell us how we can help you...\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"flex flex-col sm:flex-row gap-4 items-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n\r\n                {/* Call Now Button */}\r\n                <motion.a\r\n                  href=\"tel:+255655285549\"\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z\"/>\r\n                  </svg>\r\n                  <span>📞 Call Now</span>\r\n                </motion.a>\r\n\r\n                {/* WhatsApp Text Button */}\r\n                <motion.a\r\n                  href=\"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n                  </svg>\r\n                  <span>💬 WhatsApp</span>\r\n                </motion.a>\r\n              </div>\r\n\r\n              {responseMessage && (\r\n                <div className={`p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>\r\n                  {responseMessage}\r\n                </div>\r\n              )}\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Professional Copyright Footer */}\r\n      <footer className=\"bg-gray-900 text-white py-8\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          {/* Company Info */}\r\n          <div className=\"text-center mb-6\">\r\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\r\n              <span className=\"text-2xl font-bold text-white\">BRAIN</span>\r\n              <span className=\"text-2xl font-bold text-green-400\">WAVE</span>\r\n            </div>\r\n            <p className=\"text-gray-400 text-sm leading-relaxed max-w-2xl mx-auto\">\r\n              Tanzania's premier educational platform empowering students to achieve academic excellence through innovative learning solutions.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Copyright Bar */}\r\n          <div className=\"border-t border-gray-700 pt-6\">\r\n            <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\r\n              <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <span className=\"text-sm text-gray-400\">© 2022</span>\r\n                  <span className=\"text-lg font-bold text-white\">Brainwave.zone</span>\r\n                </div>\r\n                <span className=\"text-sm text-blue-400 font-medium\">Study Smarter</span>\r\n                <span className=\"text-sm text-gray-400\">Kigamboni - Dar es Salaam, Tanzania</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-6 text-sm text-gray-400\">\r\n                <span>Privacy Policy</span>\r\n                <span>Terms of Service</span>\r\n                <span>Support</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-4 text-center text-xs text-gray-500\">\r\n              Empowering Tanzanian students since 2022 • All rights reserved\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n\r\n\r\n\r\n      {/* Floating WhatsApp Button - Blue & White */}\r\n      <motion.div\r\n        initial={{ scale: 0, rotate: -180 }}\r\n        animate={{ scale: 1, rotate: 0 }}\r\n        transition={{ duration: 0.5, delay: 1 }}\r\n        className=\"fixed bottom-6 right-6 z-50\"\r\n      >\r\n        <motion.a\r\n          href=\"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"group flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-white\"\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          {/* WhatsApp Icon */}\r\n          <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n          </svg>\r\n\r\n          {/* Pulse Animation - Blue */}\r\n          <div className=\"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20\"></div>\r\n\r\n          {/* Tooltip */}\r\n          <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n            Chat with us on WhatsApp\r\n            <div className=\"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"></div>\r\n          </div>\r\n        </motion.a>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEb,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE+B;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+B,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,UAAU,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmC,iBAAiB,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMoC,UAAU,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMqC,eAAe,GAAIC,GAAG,IAAK;IAAA,IAAAC,YAAA;IAC/B,CAAAA,YAAA,GAAAD,GAAG,CAACE,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACrD,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGmB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoB,IAAI,GAAG,MAAMjC,SAAS,CAACQ,QAAQ,CAAC;MACtC,IAAIyB,IAAI,CAACC,OAAO,EAAE;QAChBpC,OAAO,CAACoC,OAAO,CAAC,4BAA4B,CAAC;QAC7CzB,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEb,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLA,OAAO,CAACqC,KAAK,CAACF,IAAI,CAACnC,OAAO,IAAI,uBAAuB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,0CAA0C,CAAC;IAC3D;IACAtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKiC,SAAS,EAAC,sDAAsD;IAAAC,QAAA,gBAEnElC,OAAA,CAAChB,MAAM,CAACmD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eAEtFlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA;UAAKiC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDlC,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClC,OAAA,CAACd,OAAO;cAAC+C,SAAS,EAAC;YAAuB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C3C,OAAA;cAAMiC,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAGN3C,OAAA;YAAKiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlC,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACJ,WAAW,CAAE;cAC5CmB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACH,UAAU,CAAE;cAC3CkB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACF,iBAAiB,CAAE;cAClDiB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACD,UAAU,CAAE;cAC3CgB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN3C,OAAA;YAAKiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlC,OAAA,CAAClB,IAAI;cACH+D,EAAE,EAAC,QAAQ;cACXZ,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC5E;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3C,OAAA,CAAClB,IAAI;cACH+D,EAAE,EAAC,WAAW;cACdZ,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EACpI;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN3C,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMxC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtD8B,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAEnE/B,gBAAgB,gBAAGH,OAAA,CAACN,GAAG;cAACuC,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3C,OAAA,CAACP,OAAO;cAACwC,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLxC,gBAAgB,iBACfH,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAEnDlC,OAAA;YAAKiC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClC,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM;gBAAE1B,eAAe,CAACJ,WAAW,CAAC;gBAAEV,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC7E6B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM;gBAAE1B,eAAe,CAACH,UAAU,CAAC;gBAAEX,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E6B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM;gBAAE1B,eAAe,CAACF,iBAAiB,CAAC;gBAAEZ,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cACnF6B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAM;gBAAE1B,eAAe,CAACD,UAAU,CAAC;gBAAEb,mBAAmB,CAAC,KAAK,CAAC;cAAE,CAAE;cAC5E6B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cAAKiC,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACpElC,OAAA,CAAClB,IAAI;gBACH+D,EAAE,EAAC,QAAQ;gBACXZ,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC5E;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP3C,OAAA,CAAClB,IAAI;gBACH+D,EAAE,EAAC,WAAW;gBACdZ,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EACtH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEb3C,OAAA;MAASiC,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eACnFlC,OAAA;QAAKiC,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpElC,OAAA;UAAKiC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCR,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BhB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAGpClC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC3C,OAAA;gBAAAkC,QAAA,EAAM;cAAmC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAACmE,EAAE;cACRf,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,6EAA6E;cACvFmB,KAAK,EAAE;gBAAEC,UAAU,EAAE;cAAwC,CAAE;cAAAnB,QAAA,GAChE,6BACkB,EAAC,GAAG,eACrBlC,OAAA;gBAAMiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAGZ3C,OAAA,CAAChB,MAAM,CAACsE,CAAC;cACPlB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGX3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAE3ElC,OAAA,CAAClB,IAAI;gBACH+D,EAAE,EAAC,WAAW;gBACdZ,SAAS,EAAC,6JAA6J;gBAAAC,QAAA,EACxK;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP3C,OAAA;gBACE4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACH,UAAU,CAAE;gBAC3CkB,SAAS,EAAC,uLAAuL;gBAAAC,QAAA,EAClM;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BR,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CjB,SAAS,EAAC,UAAU;YAAAC,QAAA,eAEpBlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,4FAA4F;kBAAAC,QAAA,eACzGlC,OAAA,CAACd,OAAO;oBAAC+C,SAAS,EAAC;kBAAsB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN3C,OAAA;kBAAIiC,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvF3C,OAAA;kBAAGiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAyD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3C,OAAA;MAASiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjClC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAGjDlC,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA,CAACX,OAAO;gBAAC4C,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAAC4D,GAAG,EAAE,KAAM;gBAACT,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KACjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAGN3C,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGlC,OAAA,CAACb,MAAM;gBAAC8C,SAAS,EAAC;cAAwB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAAC4D,GAAG,EAAE,EAAG;gBAACT,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAGN3C,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGlC,OAAA,CAACV,QAAQ;gBAAC2C,SAAS,EAAC;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA,CAACF,eAAe;gBAAC4D,GAAG,EAAE,EAAG;gBAACT,QAAQ,EAAE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAGN3C,OAAA;YAAKiC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjGlC,OAAA;cAAKiC,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChE3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV3C,OAAA;MAASmB,GAAG,EAAEL,WAAY;MAACmB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACrDlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChF3C,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb3C,OAAA;UAAKiC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAExClC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FlC,OAAA,CAACb,MAAM;gBAAC8C,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN3C,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvF3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FlC,OAAA,CAACZ,QAAQ;gBAAC6C,SAAS,EAAC;cAAwB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN3C,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzF3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBAEhFlC,OAAA;cAAKiC,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGlC,OAAA,CAACT,MAAM;gBAAC0C,SAAS,EAAC;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN3C,OAAA;cAAIiC,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrF3C,OAAA;cAAGiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3C,OAAA;MAASmB,GAAG,EAAEH,iBAAkB;MAACiB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACzDlC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E3C,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb3C,OAAA;UAAKiC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAEvDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,EAAC;cAE1G;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzD3C,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,EAAC;cAE3G;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzD3C,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEhDlC,OAAA;cAAKiC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClC,OAAA;gBAAKiC,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EAAC;cAE5G;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBlC,OAAA;kBAAIiC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzD3C,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAGiC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3C,OAAA;MAASmB,GAAG,EAAEF,UAAW;MAACgB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAChElC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAIiC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzD3C,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BlC,OAAA;YAAM2D,QAAQ,EAAE/B,YAAa;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDlC,OAAA;cAAKiC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9D3C,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXrD,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrBsD,QAAQ,EAAErC,YAAa;kBACvBsC,QAAQ;kBACR7B,SAAS,EAAC,qIAAqI;kBAC/I8B,WAAW,EAAC;gBAAW;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3C,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/D3C,OAAA;kBACE4D,IAAI,EAAC,OAAO;kBACZrD,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtBqD,QAAQ,EAAErC,YAAa;kBACvBsC,QAAQ;kBACR7B,SAAS,EAAC,qIAAqI;kBAC/I8B,WAAW,EAAC;gBAAgB;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE3C,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACdmB,KAAK,EAAErB,QAAQ,CAACV,OAAQ;gBACxBkE,QAAQ,EAAErC,YAAa;gBACvBsC,QAAQ;gBACRE,IAAI,EAAE,CAAE;gBACR/B,SAAS,EAAC,qIAAqI;gBAC/I8B,WAAW,EAAC;cAAiB;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BlC,OAAA;gBACE4D,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAExD,OAAQ;gBAClBwB,SAAS,EAAC,sJAAsJ;gBAAAC,QAAA,EAE/JzB,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3C,OAAA;MAAQiC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eAC3ClC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAKiC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DlC,OAAA,CAACd,OAAO;cAAC+C,SAAS,EAAC;YAAuB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C3C,OAAA;cAAMiC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN3C,OAAA;YAAGiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3C,OAAA;YAAKiC,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClElC,OAAA,CAAClB,IAAI;cAAC+D,EAAE,EAAC,QAAQ;cAACZ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7E3C,OAAA,CAAClB,IAAI;cAAC+D,EAAE,EAAC,WAAW;cAACZ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnF3C,OAAA;cAAQ4C,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACD,UAAU,CAAE;cAACgB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChH,CAAC,eACN3C,OAAA;YAAKiC,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3ElC,OAAA;cAAAkC,QAAA,EAAG;YAA2C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,8BAMT,eACA3C,OAAA;MAASiC,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAEjFlC,OAAA;QAAKiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClC,OAAA;UAAKiC,SAAS,EAAC,kBAAkB;UAACmB,KAAK,EAAE;YACvCc,eAAe,EAAG,kQAAiQ;YACnRC,cAAc,EAAE;UAClB;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN3C,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DlC,OAAA;UAAKiC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAI9FlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCR,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BhB,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAGxGlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3C7B,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;gBAAE+B,KAAK,EAAE;cAAE,CAAE;cACxCpB,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbC,KAAK,EAAE,GAAG;gBACVU,IAAI,EAAE,QAAQ;gBACdS,SAAS,EAAE,GAAG;gBACdC,OAAO,EAAE;cACX,CAAE;cACFrC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAGzClC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,mCAAmC;gBAC7CM,OAAO,EAAE;kBACPgC,UAAU,EAAE,CACV,+GAA+G,EAC/G,+GAA+G,EAC/G,+GAA+G,EAC/G,+GAA+G;gBAEnH,CAAE;gBACFvB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXuB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGD,CAAC,GAAGgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB9E,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,+BAA+B;gBACzCmB,KAAK,EAAE;kBACLmB,UAAU,EAAEO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;kBAC/CC,IAAI,EAAG,GAAE,EAAE,GAAGD,CAAC,GAAG,EAAG,GAAE;kBACvBE,GAAG,EAAG,GAAE,EAAE,GAAGF,CAAC,GAAG,EAAG;gBACtB,CAAE;gBACFvC,OAAO,EAAE;kBACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBAClBT,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBACtB8B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;gBACvB,CAAE;gBACFpB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC,GAAG6B,CAAC,GAAG,GAAG;kBACrBN,MAAM,EAAEC,QAAQ;kBAChBvB,KAAK,EAAE4B,CAAC,GAAG,GAAG;kBACdJ,IAAI,EAAE;gBACR;cAAE,GAlBGI,CAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBP,CACF,CAAC,eAEF3C,OAAA,CAAChB,MAAM,CAACmE,EAAE;gBACRlB,SAAS,EAAC,gJAAgJ;gBAC1JmB,KAAK,EAAE;kBACLC,UAAU,EAAE,yDAAyD;kBACrE4B,aAAa,EAAE,SAAS;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBAAAhD,QAAA,gBAGFlC,OAAA,CAAChB,MAAM,CAACmG,IAAI;kBACVlD,SAAS,EAAC,4BAA4B;kBACtCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAES,CAAC,EAAE,CAAC,EAAE;oBAAEqB,KAAK,EAAE,GAAG;oBAAEgB,OAAO,EAAE,CAAC;kBAAG,CAAE;kBAC1D7C,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVS,CAAC,EAAE,CAAC;oBACJqB,KAAK,EAAE,CAAC;oBACRgB,OAAO,EAAE,CAAC;oBACVC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFrC,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVU,IAAI,EAAE,QAAQ;oBACdS,SAAS,EAAE,EAAE;oBACbC,OAAO,EAAE,EAAE;oBACXe,UAAU,EAAE;sBACVpC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFY,UAAU,EAAE;oBACVlB,KAAK,EAAE,IAAI;oBACXmB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACrBlD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACVW,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbW,IAAI,EAAE,QAAQ;sBACdS,SAAS,EAAE;oBACb;kBACF,CAAE;kBACFjB,KAAK,EAAE;oBACLmB,UAAU,EAAE,gEAAgE;oBAC5EiB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtBC,UAAU,EAAE,KAAK;oBACjBN,UAAU,EAAE,kCAAkC;oBAC9CO,MAAM,EAAE;kBACV,CAAE;kBAAA1D,QAAA,GACH,OAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,+CAA+C;oBACzDM,OAAO,EAAE;sBACP6B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBACtB9B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;sBACtBiD,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;sBAChBhB,UAAU,EAAE,CACV,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C;oBAE9C,CAAE;oBACFvB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,CAAC;sBACRwB,IAAI,EAAE;oBACR,CAAE;oBACFtB,KAAK,EAAE;sBACLmB,UAAU,EAAE,0CAA0C;sBACtDsB,SAAS,EAAE;oBACb;kBAAE;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,iDAAiD;oBAC3DM,OAAO,EAAE;sBACP6B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBACtB9B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;sBACtBS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACZV,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACbkD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG;oBACxB,CAAE;oBACFvC,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,GAAG;sBACVwB,IAAI,EAAE;oBACR,CAAE;oBACFtB,KAAK,EAAE;sBACLmB,UAAU,EAAE,2CAA2C;sBACvDsB,SAAS,EAAE;oBACb;kBAAE;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGF3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,6BAA6B;oBACvCM,OAAO,EAAE;sBACPgC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE;oBAE7E,CAAE;oBACFvB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,CAAC;sBACRwB,IAAI,EAAE;oBACR;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAGd3C,OAAA,CAAChB,MAAM,CAACmG,IAAI;kBACVlD,SAAS,EAAC,uBAAuB;kBACjCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAES,CAAC,EAAE,EAAE;oBAAEqB,KAAK,EAAE,GAAG;oBAAEgB,OAAO,EAAE;kBAAG,CAAE;kBACxD7C,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVS,CAAC,EAAE,CAAC;oBACJqB,KAAK,EAAE,CAAC;oBACRgB,OAAO,EAAE,CAAC;oBACV/C,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACnBgD,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFrC,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVU,IAAI,EAAE,QAAQ;oBACdS,SAAS,EAAE,EAAE;oBACbC,OAAO,EAAE,EAAE;oBACXjC,CAAC,EAAE;sBACDY,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAC;oBACDW,UAAU,EAAE;sBACVpC,QAAQ,EAAE,GAAG;sBACbuB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFY,UAAU,EAAE;oBACVlB,KAAK,EAAE,IAAI;oBACXmB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrBlD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACVW,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbW,IAAI,EAAE,QAAQ;sBACdS,SAAS,EAAE;oBACb;kBACF,CAAE;kBACFjB,KAAK,EAAE;oBACLmB,UAAU,EAAE,6EAA6E;oBACzFiB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtBC,UAAU,EAAE,KAAK;oBACjBN,UAAU,EAAE,kCAAkC;oBAC9CO,MAAM,EAAE;kBACV,CAAE;kBAAA1D,QAAA,GACH,SAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,4CAA4C;oBACtDM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;sBAC1BS,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;sBAClCV,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;sBAC7B+B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBAChCG,UAAU,EAAE,CACV,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C;oBAE9C,CAAE;oBACFvB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,GAAG;sBACVwB,IAAI,EAAE;oBACR,CAAE;oBACFtB,KAAK,EAAE;sBACLmB,UAAU,EAAE,0CAA0C;sBACtDsB,SAAS,EAAE;oBACb;kBAAE;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGD,CAAC,GAAGgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB9E,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBAETb,SAAS,EAAC,mCAAmC;oBAC7CM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;sBACpBS,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG+B,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,EAAE,GAAGA,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;sBAC7EzC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGyC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE,EAAE,GAAGA,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;sBAC1DV,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;oBAC/B,CAAE;oBACFpB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,CAAC,GAAG4B,CAAC,GAAG,GAAG;sBAClBJ,IAAI,EAAE;oBACR,CAAE;oBACFtB,KAAK,EAAE;sBACLmB,UAAU,EAAG,0CAAyC;sBACtDsB,SAAS,EAAE;oBACb;kBAAE,GAjBGf,CAAC;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkBP,CACF,CAAC,eAGF3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,6BAA6B;oBACvCM,OAAO,EAAE;sBACPgC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE;oBAE7E,CAAE;oBACFvB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBvB,KAAK,EAAE,GAAG;sBACVwB,IAAI,EAAE;oBACR;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGZ3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,yEAAyE;gBACnFG,OAAO,EAAE;kBAAE0D,KAAK,EAAE,CAAC;kBAAExD,OAAO,EAAE;gBAAE,CAAE;gBAClCC,OAAO,EAAE;kBACPuD,KAAK,EAAE,KAAK;kBACZxD,OAAO,EAAE,CAAC;kBACVuD,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACF7C,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,KAAK,EAAE,GAAG;kBACV2C,SAAS,EAAE;oBACT5C,QAAQ,EAAE,CAAC;oBACXuB,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR;gBACF,CAAE;gBACFtB,KAAK,EAAE;kBACLmB,UAAU,EAAE,4DAA4D;kBACxEsB,SAAS,EAAE;gBACb,CAAE;gBAAA3D,QAAA,eAGFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBACTb,SAAS,EAAC,+BAA+B;kBACzCM,OAAO,EAAE;oBACPgC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE,CAC1E;oBACDxB,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;kBACrB,CAAE;kBACFC,UAAU,EAAE;oBACVuB,UAAU,EAAE;sBACVtB,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAC;oBACD3B,CAAC,EAAE;sBACDE,QAAQ,EAAE,CAAC;sBACXuB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE,WAAW;sBACjBxB,KAAK,EAAE;oBACT;kBACF;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EAGZ,CAAC,GAAGgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB9E,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,wCAAwC;gBAClDmB,KAAK,EAAE;kBACL0C,KAAK,EAAG,GAAE,EAAE,GAAGhB,CAAC,GAAG,CAAE,IAAG;kBACxBiB,MAAM,EAAG,GAAE,EAAE,GAAGjB,CAAC,GAAG,CAAE,IAAG;kBACzBP,UAAU,EAAEO,CAAC,GAAG,CAAC,KAAK,CAAC,GACnB,0CAA0C,GAC1C,0CAA0C;kBAC9Ce,SAAS,EAAEf,CAAC,GAAG,CAAC,KAAK,CAAC,GAClB,kCAAkC,GAClC,kCAAkC;kBACtCkB,SAAS,EAAE;gBACb,CAAE;gBACFzD,OAAO,EAAE;kBACPgD,MAAM,EAAET,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;kBAChC/B,CAAC,EAAE,CACD,CAAC,EACDkD,IAAI,CAACC,GAAG,CAAEpB,CAAC,GAAGmB,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,GAAG,GAAGrB,CAAC,GAAG,EAAE,CAAC,EAC5C,CAAC,EACD,CAACmB,IAAI,CAACC,GAAG,CAAEpB,CAAC,GAAGmB,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,GAAG,GAAGrB,CAAC,GAAG,EAAE,CAAC,EAC7C,CAAC,CACF;kBACDzC,CAAC,EAAE,CACD,CAAC,EACD,CAAC4D,IAAI,CAACG,GAAG,CAAEtB,CAAC,GAAGmB,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAG,EAAE,CAAC,EAC5C,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,CAAC,EACf,CAACmB,IAAI,CAACG,GAAG,CAAEtB,CAAC,GAAGmB,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,GAAGrB,CAAC,GAAG,EAAE,CAAC,EAC5C,CAAC,CACF;kBACDxC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBAC9B8B,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG;gBAC/B,CAAE;gBACFpB,UAAU,EAAE;kBACVC,QAAQ,EAAE,EAAE,GAAG6B,CAAC,GAAG,CAAC;kBACpBN,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBxB,KAAK,EAAE4B,CAAC,GAAG;gBACb;cAAE,GArCGA,CAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCP,CACF,CAAC,EAGD,CAAC,GAAGgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB9E,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,+BAA+B;gBACzCmB,KAAK,EAAE;kBACLmB,UAAU,EAAG,0BACX,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACO,CAAC,CAC3F,YAAW;kBACZC,IAAI,EAAG,GAAE,EAAE,GAAGD,CAAC,GAAG,EAAG,GAAE;kBACvBE,GAAG,EAAG,GAAE,EAAE,GAAIF,CAAC,GAAG,CAAC,GAAI,EAAG,GAAE;kBAC5Be,SAAS,EAAG,YACV,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACf,CAAC,CAC3F;gBACH,CAAE;gBACFvC,OAAO,EAAE;kBACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,CAAC,EAAE,CAAC,CAAC,EAAEkD,IAAI,CAACG,GAAG,CAACtB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;kBAC3BxC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAClB8B,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBACpBmB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;gBACtB,CAAE;gBACFvC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC,GAAG6B,CAAC,GAAG,GAAG;kBACrBN,MAAM,EAAEC,QAAQ;kBAChBvB,KAAK,EAAE4B,CAAC,GAAG,GAAG;kBACdJ,IAAI,EAAE;gBACR;cAAE,GAxBI,WAAUI,CAAE,EAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBpB,CACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,iGAAiG;cAC3GmB,KAAK,EAAE;gBACLmB,UAAU,EAAE,gEAAgE;gBAC5EsB,SAAS,EAAE,oEAAoE;gBAC/EQ,MAAM,EAAE;cACV,CAAE;cAAAnE,QAAA,gBAGFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,gFAAgF;gBAC1FM,OAAO,EAAE;kBACPQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;gBACrB,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXuB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEF3C,OAAA,CAACV,QAAQ;gBAAC2C,SAAS,EAAC;cAA4C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnE3C,OAAA;gBAAMiC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE3D;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGP3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,+BAA+B;gBACzCM,OAAO,EAAE;kBACPsD,SAAS,EAAE,CACT,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC;gBAErC,CAAE;gBACF7C,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXuB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAACsE,CAAC;cACPlB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAID;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGX3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,wIAAwI;cAAAC,QAAA,EAEjJ,CAACvB,IAAI,gBACJX,OAAA,CAACrB,KAAK,CAAC2H,QAAQ;gBAAApE,QAAA,eAIblC,OAAA;kBAAKiC,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBACpFlC,OAAA,CAAClB,IAAI;oBAAC+D,EAAE,EAAC,WAAW;oBAACZ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/ClC,OAAA,CAAChB,MAAM,CAACuH,MAAM;sBACZtE,SAAS,EAAC,yOAAyO;sBACnPqD,UAAU,EAAE;wBAAElB,KAAK,EAAE;sBAAK,CAAE;sBAC5BoC,QAAQ,EAAE;wBAAEpC,KAAK,EAAE;sBAAK,CAAE;sBAAAlC,QAAA,eAE1BlC,OAAA;wBAAKiC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzDlC,OAAA,CAACyG,UAAU;0BAACxE,SAAS,EAAC;wBAAqC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9D3C,OAAA;0BAAMiC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eAEP3C,OAAA,CAAClB,IAAI;oBAAC+D,EAAE,EAAC,QAAQ;oBAACZ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC5ClC,OAAA,CAAChB,MAAM,CAACuH,MAAM;sBACZtE,SAAS,EAAC,yOAAyO;sBACnPqD,UAAU,EAAE;wBAAElB,KAAK,EAAE;sBAAK,CAAE;sBAC5BoC,QAAQ,EAAE;wBAAEpC,KAAK,EAAE;sBAAK,CAAE;sBAAAlC,QAAA,eAE1BlC,OAAA;wBAAKiC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzDlC,OAAA,CAACR,OAAO;0BAACyC,SAAS,EAAC;wBAAqC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3D3C,OAAA;0BAAMiC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAK;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,gBAEjB3C,OAAA,CAAClB,IAAI;gBAAC+D,EAAE,EAAC,WAAW;gBAACZ,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/ClC,OAAA,CAAChB,MAAM,CAACuH,MAAM;kBACZtE,SAAS,EAAC,iMAAiM;kBAC3MqD,UAAU,EAAE;oBAAElB,KAAK,EAAE;kBAAK,CAAE;kBAC5BoC,QAAQ,EAAE;oBAAEpC,KAAK,EAAE;kBAAK,CAAE;kBAAAlC,QAAA,eAE1BlC,OAAA;oBAAKiC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDlC,OAAA,CAAC0G,0BAA0B;sBAACzE,SAAS,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChE3C,OAAA;sBAAMiC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBAEjElC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,eACrElC,OAAA,CAACF,eAAe;oBAAC4D,GAAG,EAAC,MAAM;oBAACT,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,eACvElC,OAAA,CAACF,eAAe;oBAAC4D,GAAG,EAAC,MAAM;oBAACT,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN3C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,eACtElC,OAAA,CAACF,eAAe;oBAAC4D,GAAG,EAAC,KAAK;oBAACT,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN3C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,eACvElC,OAAA,CAACF,eAAe;oBAAC4D,GAAG,EAAC,MAAM;oBAACT,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACN3C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BR,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAE;YAC9BC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CjB,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FlC,OAAA;cAAKiC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAEvBlC,OAAA;gBAAKiC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7FlC,OAAA;kBACE2G,GAAG,EAAEC,MAAO;kBACZC,GAAG,EAAC,mBAAmB;kBACvB5E,SAAS,EAAC,yCAAyC;kBACnDxB,OAAO,EAAC;gBAAM;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEuB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE;gBAAY,CAAE;gBACjEzC,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5IlC,OAAA,CAACZ,QAAQ;kBAAC6C,SAAS,EAAC;gBAAqC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAEb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEuB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAExB,KAAK,EAAE;gBAAE,CAAE;gBAC3EjB,SAAS,EAAC,uIAAuI;gBAAAC,QAAA,eAEjJlC,OAAA,CAACb,MAAM;kBAAC8C,SAAS,EAAC;gBAAqC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAEb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAE;gBAC5BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEuB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAExB,KAAK,EAAE;gBAAI,CAAE;gBAC7EjB,SAAS,EAAC,4HAA4H;gBAAAC,QAAA,eAEtIlC,OAAA,CAACd,OAAO;kBAAC+C,SAAS,EAAC;gBAAqC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAGb3C,OAAA;gBAAKiC,SAAS,EAAC;cAAoI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3C,OAAA;QAAKiC,SAAS,EAAC,2GAA2G;QAAAC,QAAA,eACxHlC,OAAA;UAAKiC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAClFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BO,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EAEnF,CACC;cAAE4E,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE3H,OAAO;cAAE4H,KAAK,EAAE;YAA4B,CAAC,EAC9F;cAAEH,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAE1H,QAAQ;cAAE2H,KAAK,EAAE;YAA8B,CAAC,EACjG;cAAEH,MAAM,EAAE,OAAO;cAAEC,IAAI,EAAE,eAAe;cAAEC,IAAI,EAAE7H,MAAM;cAAE8H,KAAK,EAAE;YAAgC,CAAC,EAChG;cAAEH,MAAM,EAAE,KAAK;cAAEC,IAAI,EAAE,cAAc;cAAEC,IAAI,EAAE5H,QAAQ;cAAE6H,KAAK,EAAE;YAAgC,CAAC,CAChG,CAACrC,GAAG,CAAC,CAACsC,IAAI,EAAEC,KAAK,kBAChBnH,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cAETV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,EAAE;gBAAE+B,KAAK,EAAE;cAAI,CAAE;cAC3Cb,WAAW,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;gBAAE+B,KAAK,EAAE;cAAE,CAAE;cAC5CpB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEiE,KAAK,GAAG;cAAI,CAAE;cAClD3D,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB6B,UAAU,EAAE;gBAAElB,KAAK,EAAE,IAAI;gBAAE/B,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCJ,SAAS,EAAC,gIAAgI;cAAAC,QAAA,gBAE1IlC,OAAA;gBAAKiC,SAAS,EAAG,gFAA+EiF,IAAI,CAACD,KAAM,2FAA2F;gBAAA/E,QAAA,eACpMlC,OAAA,CAACkH,IAAI,CAACF,IAAI;kBAAC/E,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EAAEgF,IAAI,CAACJ;cAAM;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1G3C,OAAA;gBAAKiC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAEgF,IAAI,CAACH;cAAI;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAZvFwE,KAAK;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV3C,OAAA;MAASmB,GAAG,EAAEiG,iBAAkB;MAACnF,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC5ElC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7ClC,OAAA;YAAIiC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb3C,OAAA;UAAKiC,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EACnG,CACC;YACE3B,IAAI,EAAE,cAAc;YACpB8G,KAAK,EAAE,QAAQ;YACfC,MAAM,EAAE,CAAC;YACTP,IAAI,EAAE,+GAA+G;YACrHQ,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACEjH,IAAI,EAAE,cAAc;YACpB8G,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE,CAAC;YACTP,IAAI,EAAE,gGAAgG;YACtGQ,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACEjH,IAAI,EAAE,aAAa;YACnB8G,KAAK,EAAE,QAAQ;YACfC,MAAM,EAAE,CAAC;YACTP,IAAI,EAAE,2GAA2G;YACjHQ,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,CACF,CAAC5C,GAAG,CAAC,CAAC6C,MAAM,EAAEN,KAAK,kBAClBnH,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YAETV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkB,WAAW,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEiE,KAAK,GAAG;YAAI,CAAE;YAClD3D,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,6JAA6J;YAAAC,QAAA,gBAGvKlC,OAAA;cAAKiC,SAAS,EAAG,qDAAoDwF,MAAM,CAACD,OAAQ;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG7F3C,OAAA;cAAKiC,SAAS,EAAC,0FAA0F;cAAAC,QAAA,eACvGlC,OAAA;gBAAKiC,SAAS,EAAC,uBAAuB;gBAACyF,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAzF,QAAA,eAC5ElC,OAAA;kBAAM4H,CAAC,EAAC;gBAAmN;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1N;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3C,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDlC,OAAA;gBAAKiC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC,CAAC,GAAGyC,KAAK,CAAC8C,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC1C,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClC9E,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBAETV,OAAO,EAAE;oBAAEgC,KAAK,EAAE,CAAC;oBAAEmB,MAAM,EAAE,CAAC;kBAAI,CAAE;kBACpChC,WAAW,EAAE;oBAAEa,KAAK,EAAE,CAAC;oBAAEmB,MAAM,EAAE;kBAAE,CAAE;kBACrCvC,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,KAAK,EAAE4B,CAAC,GAAG;kBAAI,CAAE;kBAC9CtB,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAK,CAAE;kBAAAvB,QAAA,eAEzBlC,OAAA,CAACT,MAAM;oBAAC0C,SAAS,EAAC,wCAAwC;oBAACmB,KAAK,EAAE;sBAAEsE,IAAI,EAAE,SAAS;sBAAET,KAAK,EAAE;oBAAU;kBAAE;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GANtGmC,CAAC;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOI,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3C,OAAA;gBAAKiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEN3C,OAAA;cAAGiC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACpElC,OAAA;gBAAMiC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzE3C,OAAA;gBAAMiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEuF,MAAM,CAACV;cAAI;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD3C,OAAA;gBAAMiC,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eAEJ3C,OAAA;cAAKiC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAE5BlC,OAAA;kBAAKiC,SAAS,EAAG,2CAA0CwF,MAAM,CAACD,OAAQ,QAAQ;kBAAAtF,QAAA,gBAChFlC,OAAA;oBACE2G,GAAG,EAAEc,MAAM,CAACF,MAAO;oBACnBV,GAAG,EAAEY,MAAM,CAAClH,IAAK;oBACjB0B,SAAS,EAAC,+DAA+D;oBACzE4F,OAAO,EAAGpG,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACE,MAAM,CAACyB,KAAK,CAAC0E,OAAO,GAAG,MAAM;sBAC/BrG,CAAC,CAACE,MAAM,CAACoG,WAAW,CAAC3E,KAAK,CAAC0E,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF3C,OAAA;oBAAKiC,SAAS,EAAC,4FAA4F;oBAACmB,KAAK,EAAE;sBAAC0E,OAAO,EAAE;oBAAM,CAAE;oBAAA5F,QAAA,eACnIlC,OAAA;sBAAMiC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAClDuF,MAAM,CAAClH,IAAI,CAACyH,KAAK,CAAC,GAAG,CAAC,CAACpD,GAAG,CAACqD,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;oBAAC;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3C,OAAA;kBAAKiC,SAAS,EAAC;gBAAqF;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eAEN3C,OAAA;gBAAKiC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlC,OAAA;kBAAKiC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ClC,OAAA;oBAAIiC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEuF,MAAM,CAAClH;kBAAI;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAElE3C,OAAA;oBAAKiC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,eAChClC,OAAA;sBAAKiC,SAAS,EAAC,uBAAuB;sBAACyF,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAzF,QAAA,eAC5ElC,OAAA;wBAAM4H,CAAC,EAAC;sBAA8F;wBAAApF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ClC,OAAA;oBAAMiC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEuF,MAAM,CAACJ;kBAAK;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzE3C,OAAA;oBAAMiC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChD3C,OAAA;oBAAMiC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAlFDwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmFA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV3C,OAAA;MAASmB,GAAG,EAAEgH,YAAa;MAAClG,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eACrElC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7ClC,OAAA;YAAIiC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE9E;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YAAGiC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,GAAC,8HAC+C,eAAAlC,OAAA;cAAAkC,QAAA,EAAQ;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAC/J;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkB,WAAW,EAAE;YAAEjB,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eAElElC,OAAA;YAAM2D,QAAQ,EAAE/B,YAAa;YAACK,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAC9DlC,OAAA;cAAKiC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChElC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR3C,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXrD,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrBsD,QAAQ,EAAErC,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxH8B,WAAW,EAAC,sBAAsB;kBAClCD,QAAQ;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3C,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR3C,OAAA;kBACE4D,IAAI,EAAC,OAAO;kBACZrD,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtBqD,QAAQ,EAAErC,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxH8B,WAAW,EAAC,kBAAkB;kBAC9BD,QAAQ;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3C,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3C,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACdmB,KAAK,EAAErB,QAAQ,CAACV,OAAQ;gBACxBkE,QAAQ,EAAErC,YAAa;gBACvBwC,IAAI,EAAE,CAAE;gBACR/B,SAAS,EAAC,8GAA8G;gBACxH8B,WAAW,EAAC,gCAAgC;gBAC5CD,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3C,OAAA;cAAKiC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC3DlC,OAAA;gBACE4D,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAExD,OAAQ;gBAClBwB,SAAS,EAAC,iMAAiM;gBAAAC,QAAA,EAE1MzB,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAGT3C,OAAA,CAAChB,MAAM,CAACoJ,CAAC;gBACPC,IAAI,EAAC,mBAAmB;gBACxBpG,SAAS,EAAC,kPAAkP;gBAC5PqD,UAAU,EAAE;kBAAElB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoC,QAAQ,EAAE;kBAAEpC,KAAK,EAAE;gBAAK,CAAE;gBAAAlC,QAAA,gBAE1BlC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAACyF,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAzF,QAAA,eAC9DlC,OAAA;oBAAM4H,CAAC,EAAC;kBAAkP;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzP,CAAC,eACN3C,OAAA;kBAAAkC,QAAA,EAAM;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eAGX3C,OAAA,CAAChB,MAAM,CAACoJ,CAAC;gBACPC,IAAI,EAAC,+IAA+I;gBACpJ1G,MAAM,EAAC,QAAQ;gBACf2G,GAAG,EAAC,qBAAqB;gBACzBrG,SAAS,EAAC,sPAAsP;gBAChQqD,UAAU,EAAE;kBAAElB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoC,QAAQ,EAAE;kBAAEpC,KAAK,EAAE;gBAAK,CAAE;gBAAAlC,QAAA,gBAE1BlC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAACyF,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAzF,QAAA,eAC9DlC,OAAA;oBAAM4H,CAAC,EAAC;kBAAklC;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACN3C,OAAA;kBAAAkC,QAAA,EAAM;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEL4F,eAAe,iBACdvI,OAAA;cAAKiC,SAAS,EAAG,kBAAiBsG,eAAe,CAACC,QAAQ,CAAC,SAAS,CAAC,GAAG,6BAA6B,GAAG,yBAA0B,EAAE;cAAAtG,QAAA,EACjIqG;YAAe;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3C,OAAA;MAAQiC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7ClC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE7DlC,OAAA;UAAKiC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlC,OAAA;YAAKiC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DlC,OAAA;cAAMiC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D3C,OAAA;cAAMiC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN3C,OAAA;YAAGiC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN3C,OAAA;UAAKiC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5ClC,OAAA;YAAKiC,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FlC,OAAA;cAAKiC,SAAS,EAAC,4EAA4E;cAAAC,QAAA,gBACzFlC,OAAA;gBAAKiC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClC,OAAA;kBAAMiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD3C,OAAA;kBAAMiC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN3C,OAAA;gBAAMiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxE3C,OAAA;gBAAMiC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAEN3C,OAAA;cAAKiC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChElC,OAAA;gBAAAkC,QAAA,EAAM;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3B3C,OAAA;gBAAAkC,QAAA,EAAM;cAAgB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7B3C,OAAA;gBAAAkC,QAAA,EAAM;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA;YAAKiC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAExD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAKT3C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;MACTV,OAAO,EAAE;QAAEgC,KAAK,EAAE,CAAC;QAAEmB,MAAM,EAAE,CAAC;MAAI,CAAE;MACpChD,OAAO,EAAE;QAAE6B,KAAK,EAAE,CAAC;QAAEmB,MAAM,EAAE;MAAE,CAAE;MACjCvC,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACxCjB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAEvClC,OAAA,CAAChB,MAAM,CAACoJ,CAAC;QACPC,IAAI,EAAC,uHAAuH;QAC5H1G,MAAM,EAAC,QAAQ;QACf2G,GAAG,EAAC,qBAAqB;QACzBrG,SAAS,EAAC,0KAA0K;QACpLqD,UAAU,EAAE;UAAElB,KAAK,EAAE;QAAI,CAAE;QAC3BoC,QAAQ,EAAE;UAAEpC,KAAK,EAAE;QAAK,CAAE;QAAAlC,QAAA,gBAG1BlC,OAAA;UAAKiC,SAAS,EAAC,oBAAoB;UAACyF,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAzF,QAAA,eACzElC,OAAA;YAAM4H,CAAC,EAAC;UAAklC;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzlC,CAAC,eAGN3C,OAAA;UAAKiC,SAAS,EAAC;QAAmE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGzF3C,OAAA;UAAKiC,SAAS,EAAC,8LAA8L;UAAAC,QAAA,GAAC,0BAE5M,eAAAlC,OAAA;YAAKiC,SAAS,EAAC;UAAoF;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACzC,EAAA,CA1iDID,IAAI;EAAA,QAISL,WAAW,EACXb,WAAW;AAAA;AAAA0J,EAAA,GALxBxI,IAAI;AA4iDV,eAAeA,IAAI;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}