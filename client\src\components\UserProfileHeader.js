import React from 'react';
import { useSelector } from 'react-redux';
import { Tb<PERSON><PERSON>, TbUser } from 'react-icons/tb';
import { useLanguage } from '../contexts/LanguageContext';

const UserProfileHeader = ({ className = "" }) => {
  const { user } = useSelector((state) => state.user);
  const { getClassName } = useLanguage();

  if (!user) return null;

  // Get display name (fallback to username if name not available)
  const displayName = user.name || user.username || 'User';
  
  // Get class display with proper formatting
  const getClassDisplay = () => {
    if (!user.class || !user.level) return '';
    
    // Use the language context function if available
    if (getClassName) {
      return getClassName(user.class, user.level);
    }
    
    // Fallback formatting
    const level = user.level?.toLowerCase();
    const classNum = user.class;
    
    if (level === 'primary') {
      return `Class ${classNum}`;
    } else if (level === 'secondary') {
      return `Form ${classNum}`;
    } else if (level === 'advance') {
      return `Form ${classNum}`;
    }
    
    return `${user.level} - Class ${classNum}`;
  };

  const classDisplay = getClassDisplay();

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* User Profile Section */}
      <div className="flex items-center space-x-2">
        {/* Profile Picture or Avatar */}
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
          {user.profileImage ? (
            <img 
              src={user.profileImage} 
              alt="Profile" 
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <TbUser className="w-4 h-4 text-white" />
          )}
        </div>
        
        {/* User Info */}
        <div className="text-right">
          <div className="flex items-center justify-end space-x-1">
            <p className="text-sm font-medium text-gray-900 truncate max-w-[120px]">
              {displayName}
            </p>
          </div>
          {classDisplay && (
            <p className="text-xs text-gray-500 truncate max-w-[120px]">
              {classDisplay}
            </p>
          )}
        </div>
      </div>

      {/* Bell Icon */}
      <div className="relative">
        <TbBell className="w-5 h-5 text-gray-600 hover:text-gray-800 cursor-pointer transition-colors" />
        {/* Optional notification dot */}
        {/* <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div> */}
      </div>
    </div>
  );
};

export default UserProfileHeader;
