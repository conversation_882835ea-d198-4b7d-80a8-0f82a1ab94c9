{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbBrain, TbHome, TbRefresh, TbMedal, TbRocket, TbDiamond, TbAward, TbShield, TbUsers } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport OnlineStatusIndicator from '../../../components/common/OnlineStatusIndicator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // State for full user data\n  const [fullUserData, setFullUserData] = useState(null);\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users (simplified)\n  if (user && !fullUserData) {\n    console.log('🔍 Loading user data for:', user.userId);\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const [userHasBeenShown, setUserHasBeenShown] = useState(false);\n  const [autoScrollCompleted, setAutoScrollCompleted] = useState(false);\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = leagueKey => {\n    var _leagueGroups$leagueK;\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) || document.getElementById(`league-${leagueKey}`) || leagueRefs.current[leagueKey];\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      // Clear old caches for other levels to prevent contamination\n      const currentLevel = (user === null || user === void 0 ? void 0 : user.level) || 'primary';\n      const allLevels = ['primary', 'secondary', 'advance'];\n      allLevels.forEach(level => {\n        if (level !== currentLevel) {\n          localStorage.removeItem(`ranking_cache_${level}`);\n          localStorage.removeItem(`ranking_cache_time_${level}`);\n        }\n      });\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'primary';\n        const cachedRanking = localStorage.getItem(`ranking_cache_${userLevel}`);\n        const cacheTime = localStorage.getItem(`ranking_cache_time_${userLevel}`);\n        const now = Date.now();\n\n        // Use cache if less than 2 minutes old and for the same level\n        if (cachedRanking && cacheTime && now - parseInt(cacheTime) < 120000) {\n          const cached = JSON.parse(cachedRanking);\n          setRankingData(cached.data || []);\n          setCurrentUserRank(cached.userRank);\n          setLoading(false);\n          return;\n        }\n      }\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && {\n            _t: Date.now()\n          })\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n\n          // Debug: Check first few users' profile data\n          console.log('🔍 First 3 users profile data:', filteredData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          // Debug: Check final transformed data for top 3 users\n          console.log('🏆 Top 3 transformed users:', transformedData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          // Cache the results with level-specific key\n          const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'primary';\n          const cacheData = {\n            data: transformedData,\n            userRank: userRankIndex >= 0 ? userRankIndex + 1 : null\n          };\n          localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n          localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => {\n          // Filter out invalid users\n          if (!userData || !userData._id) return false;\n\n          // Apply level filtering for non-admin users\n          if (!userData.isAdmin && user !== null && user !== void 0 && user.level) {\n            const userLevel = user.level.toLowerCase();\n            const dataLevel = (userData.level || 'primary').toLowerCase();\n            if (userLevel === 'primary') {\n              // Primary users should only see primary users\n              return dataLevel === 'primary';\n            } else if (userLevel === 'secondary') {\n              // Secondary users should only see secondary users\n              return dataLevel === 'secondary';\n            } else if (userLevel === 'advance') {\n              // Advance users should only see advance users\n              return dataLevel === 'advance';\n            }\n          }\n          return true; // Include admins and when no level filtering needed\n        }).map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Cache the fallback results with level-specific key\n        const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'primary';\n        const cacheData = {\n          data: transformedData,\n          userRank: null // Will be set below after finding user rank\n        };\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Update cache with final user rank\n        cacheData.userRank = userRank >= 0 ? userRank + 1 : null;\n        localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n        localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking (development only)\n        if (process.env.NODE_ENV === 'development') {\n          console.log('🔍 Enhanced User ranking debug:', {\n            currentUser: user === null || user === void 0 ? void 0 : user.name,\n            userId: user === null || user === void 0 ? void 0 : user._id,\n            userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n            isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n            userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n            userRankIndex: userRank,\n            userRankPosition: userRank >= 0 ? userRank + 1 : null,\n            totalRankedUsers: transformedData.length,\n            firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n              id: u._id,\n              type: typeof u._id,\n              name: u.name\n            })),\n            exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n            stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n            nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n          });\n        }\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch full user data\n  const fetchFullUserData = async () => {\n    if (!(user !== null && user !== void 0 && user.userId)) {\n      console.log('❌ No userId available:', user);\n      return;\n    }\n    try {\n      console.log('🔍 Fetching full user data for userId:', user.userId);\n      const response = await getAllUsers();\n      console.log('📋 getAllUsers response:', response);\n      if (response.success) {\n        console.log('📊 Total users found:', response.data.length);\n        console.log('🔍 Looking for userId:', user.userId);\n        console.log('📝 First 5 user IDs:', response.data.slice(0, 5).map(u => ({\n          id: u._id,\n          name: u.name\n        })));\n        const userData = response.data.find(u => String(u._id) === String(user.userId));\n        if (userData) {\n          console.log('✅ Found full user data:', userData);\n          // Ensure profile picture properties are set\n          const userDataWithProfile = {\n            ...userData,\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || ''\n          };\n          setFullUserData(userDataWithProfile);\n        } else {\n          console.log('❌ User not found in users list');\n          console.log('🔍 Trying alternative search methods...');\n\n          // Try different ID formats\n          const userDataAlt = response.data.find(u => u._id === user.userId || u.id === user.userId || String(u._id).includes(user.userId) || String(user.userId).includes(u._id));\n          if (userDataAlt) {\n            console.log('✅ Found user with alternative method:', userDataAlt);\n            // Ensure profile picture properties are set\n            const userDataWithProfile = {\n              ...userDataAlt,\n              profilePicture: userDataAlt.profileImage || userDataAlt.profilePicture || '',\n              profileImage: userDataAlt.profileImage || userDataAlt.profilePicture || ''\n            };\n            setFullUserData(userDataWithProfile);\n          } else {\n            console.log('❌ User not found with any method');\n          }\n        }\n      } else {\n        console.log('❌ getAllUsers failed:', response);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching user data:', error);\n    }\n  };\n\n  // Try to find user in ranking data as fallback\n  useEffect(() => {\n    if (!fullUserData && user !== null && user !== void 0 && user.userId && rankingData.length > 0) {\n      console.log('🔍 Trying to find user in ranking data...');\n      const userInRanking = rankingData.find(u => String(u._id) === String(user.userId));\n      if (userInRanking) {\n        console.log('✅ Found user in ranking data:', userInRanking);\n        // Ensure profile picture properties are set\n        const userDataWithProfile = {\n          ...userInRanking,\n          profilePicture: userInRanking.profileImage || userInRanking.profilePicture || '',\n          profileImage: userInRanking.profileImage || userInRanking.profilePicture || ''\n        };\n        setFullUserData(userDataWithProfile);\n      } else {\n        console.log('❌ User not found in ranking data either');\n      }\n    }\n  }, [rankingData, user, fullUserData]);\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    fetchFullUserData(); // Fetch full user data\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            var _event$detail;\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if ((_event$detail = event.detail) !== null && _event$detail !== void 0 && _event$detail.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!(user !== null && user !== void 0 && user._id)) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      var _leagueData$users;\n      const userInLeague = (_leagueData$users = leagueData.users) === null || _leagueData$users === void 0 ? void 0 : _leagueData$users.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n    return null;\n  };\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = userId => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Helper function to check if user should be highlighted (only before they've been shown)\n  const shouldHighlightUser = userId => {\n    return isCurrentUser(userId) && !userHasBeenShown;\n  };\n\n  // Allow users to click anywhere to disable highlighting\n  const handlePageClick = () => {\n    if (!userHasBeenShown) {\n      setUserHasBeenShown(true);\n      console.log('👆 User clicked - highlighting disabled');\n    }\n  };\n\n  // Reset highlighting when user or league changes\n  useEffect(() => {\n    setUserHasBeenShown(false);\n    setAutoScrollCompleted(false); // Reset auto-scroll state\n  }, [user === null || user === void 0 ? void 0 : user._id, selectedLeague]);\n\n  // Auto-scroll to user position ONLY on first visit\n  useEffect(() => {\n    console.log('🔄 Auto-scroll check:', {\n      userId: user === null || user === void 0 ? void 0 : user._id,\n      autoScrollCompleted,\n      rankingDataLength: rankingData.length\n    });\n\n    // Only scroll if user exists, hasn't been scrolled yet, and we have data\n    if (!(user !== null && user !== void 0 && user._id) || autoScrollCompleted || rankingData.length === 0) {\n      console.log('❌ Auto-scroll skipped:', {\n        hasUser: !!(user !== null && user !== void 0 && user._id),\n        completed: autoScrollCompleted,\n        hasData: rankingData.length > 0\n      });\n      return;\n    }\n    const scrollToUser = () => {\n      console.log('🎯 Starting auto-scroll for user:', user._id);\n\n      // First, try to find user in any ranking data\n      const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n      if (!userInRanking) {\n        console.log('❌ User not found in ranking data');\n        setAutoScrollCompleted(true); // Mark as completed even if not found\n        return;\n      }\n      console.log('✅ User found in ranking at position:', userInRanking.rank);\n\n      // Check if user is in top 3 (podium)\n      const isInPodium = userInRanking.rank <= 3;\n      console.log('🏆 Is user in podium?', isInPodium);\n      if (isInPodium) {\n        // Scroll to podium section\n        console.log('📍 Scrolling to podium section...');\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        console.log('🎪 Podium section found:', !!podiumSection);\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to podium');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          setAutoScrollCompleted(true);\n        }\n      } else {\n        // Look for user element in the ranking list\n        console.log('📍 Looking for user element with ID:', user._id);\n        const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n        console.log('🎯 User element found:', !!userElement);\n        if (userElement) {\n          setTimeout(() => {\n            userElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to user position');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          console.log('❌ User element not found in DOM');\n          setAutoScrollCompleted(true);\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready, but not too long\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user === null || user === void 0 ? void 0 : user._id, rankingData, autoScrollCompleted]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Skeleton loading component\n  const RankingSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-end mb-16 space-x-8\",\n        children: [2, 1, 3].map(position => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-center ${position === 1 ? 'order-2' : position === 2 ? 'order-1' : 'order-3'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 15\n          }, this)]\n        }, position, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1131,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 max-w-4xl mx-auto\",\n        children: [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 rounded-xl p-4 animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white/10 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-white/10 rounded w-32 mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-white/5 rounded w-24\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 bg-white/10 rounded w-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 15\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1120,\n    columnNumber: 5\n  }, this);\n\n  // Show skeleton only on initial load\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(RankingSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1160,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\",\n      onClick: handlePageClick,\n      children: [!userHasBeenShown && user && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -20\n        },\n        className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium\",\n        children: \"\\uD83C\\uDFAF Finding your position in the rankings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1345,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1347,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1343,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1353,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1351,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-2 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-4 md:py-6 lg:py-8\",\n          style: {\n            padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [userLeagueInfo && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\",\n                  style: {\n                    background: userLeagueInfo.type === 'podium' ? 'linear-gradient(135deg, #FFD700, #FFA500)' : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                    color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                    fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: userLeagueInfo.type === 'podium' ? `🏆 Podium #${userLeagueInfo.position}` : `${userLeagueInfo.league} #${userLeagueInfo.position}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1407,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 19\n                }, this), fullUserData && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 relative\",\n                      children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                        user: fullUserData,\n                        size: \"xl\",\n                        showOnlineStatus: false,\n                        style: {\n                          border: '3px solid #facc15',\n                          boxShadow: '0 10px 25px rgba(0,0,0,0.15)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1425,\n                        columnNumber: 25\n                      }, this), fullUserData.isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          position: 'absolute',\n                          bottom: '4px',\n                          right: '4px',\n                          width: '16px',\n                          height: '16px',\n                          backgroundColor: '#22c55e',\n                          borderRadius: '50%',\n                          border: '3px solid #ffffff',\n                          boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                          zIndex: 10\n                        },\n                        title: \"Online\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1424,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-2 truncate\",\n                        children: fullUserData.name || fullUserData.username || 'User'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1456,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 gap-2 text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-green-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-green-300 text-xs\",\n                            children: \"Total XP\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1463,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple XP field names for migrated users\n                              const xp = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                              return xp.toLocaleString();\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1464,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1462,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-purple-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-purple-300 text-xs\",\n                            children: \"Rank\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1474,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try to find user in ranking data\n                              const userInRanking = rankingData.find(u => String(u._id) === String(fullUserData._id));\n                              return userInRanking ? `#${userInRanking.rank}` : currentUserRank ? `#${currentUserRank}` : 'N/A';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1475,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1473,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-blue-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-blue-300 text-xs\",\n                            children: \"League\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1485,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold text-xs\",\n                            children: (() => {\n                              // Find user's league with icon - try multiple XP sources\n                              const userXP = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                              for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                var _leagueData$users2;\n                                const userInLeague = (_leagueData$users2 = leagueData.users) === null || _leagueData$users2 === void 0 ? void 0 : _leagueData$users2.find(u => String(u._id) === String(fullUserData._id));\n                                if (userInLeague) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                }\n                              }\n                              // Fallback: calculate league from XP even if not in league data\n                              if (userXP > 0) {\n                                const leagueInfo = getUserLeague(userXP);\n                                return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                              }\n                              return '🔰 Unranked';\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1486,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1484,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-orange-500/20 rounded-lg p-2 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-orange-300 text-xs\",\n                            children: \"Quizzes\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1508,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: (() => {\n                              // Try multiple quiz count field names\n                              return fullUserData.quizzesCompleted || fullUserData.totalQuizzesTaken || fullUserData.quizzesTaken || fullUserData.totalQuizzes || 0;\n                            })()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1509,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1507,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1461,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 mt-2 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-yellow-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-yellow-300 text-xs\",\n                            children: \"Level\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1521,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: fullUserData.currentLevel || fullUserData.level || 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1522,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1520,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-red-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-red-300 text-xs\",\n                            children: \"Streak\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1528,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: fullUserData.currentStreak || fullUserData.streak || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1529,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1527,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-cyan-500/20 rounded-lg p-1.5 text-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-cyan-300 text-xs\",\n                            children: \"Avg Score\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1535,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-white font-bold\",\n                            children: [(() => {\n                              const avgScore = fullUserData.averageScore || fullUserData.avgScore || 0;\n                              return Math.round(avgScore);\n                            })(), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1536,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1534,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1519,\n                        columnNumber: 25\n                      }, this), (() => {\n                        // Find user's position in their league\n                        for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                          var _leagueData$users3;\n                          const userIndex = (_leagueData$users3 = leagueData.users) === null || _leagueData$users3 === void 0 ? void 0 : _leagueData$users3.findIndex(u => String(u._id) === String(fullUserData._id));\n                          if (userIndex !== -1 && userIndex !== undefined) {\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"mt-2 text-center\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-yellow-300 text-xs\",\n                                  children: \"League Position\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1554,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"text-white font-bold text-sm\",\n                                  children: [\"#\", userIndex + 1, \" of \", leagueData.users.length]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1555,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1553,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1552,\n                              columnNumber: 33\n                            }, this);\n                          }\n                        }\n                        return null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1455,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1422,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1417,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.h3, {\n                    className: \"text-2xl md:text-3xl font-black mb-2\",\n                    style: {\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    },\n                    animate: {\n                      scale: [1, 1.02, 1]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    children: \"\\uD83C\\uDFC6 LEAGUES \\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1583,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap items-center justify-center gap-3 md:gap-4\",\n                    children: getOrderedLeagues().map(leagueKey => {\n                      var _leagueGroups$leagueK2;\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = ((_leagueGroups$leagueK2 = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK2 === void 0 ? void 0 : _leagueGroups$leagueK2.users.length) || 0;\n                      return /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"flex flex-col items-center gap-2\",\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                          whileHover: {\n                            scale: 1.1,\n                            y: -3\n                          },\n                          whileTap: {\n                            scale: 0.95\n                          },\n                          onClick: () => handleLeagueSelect(leagueKey),\n                          className: `relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${isSelected ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl' : 'hover:ring-2 hover:ring-white/30'}`,\n                          style: {\n                            background: isSelected ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)` : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                            border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                            boxShadow: isSelected ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80` : `0 4px 15px ${league.shadowColor}40`,\n                            transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                            filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                          },\n                          animate: isSelected ? {\n                            boxShadow: [`0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`, `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`, `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`],\n                            scale: [1.1, 1.15, 1.1]\n                          } : {},\n                          transition: {\n                            duration: 2,\n                            repeat: isSelected ? Infinity : 0,\n                            ease: \"easeInOut\"\n                          },\n                          title: `Click to view ${league.title} League (${userCount} users)`,\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-3xl md:text-4xl\",\n                            children: league.leagueIcon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1646,\n                            columnNumber: 29\n                          }, this), isSelected && /*#__PURE__*/_jsxDEV(motion.div, {\n                            initial: {\n                              scale: 0,\n                              rotate: -360,\n                              opacity: 0\n                            },\n                            animate: {\n                              scale: [1, 1.3, 1],\n                              rotate: [0, 360, 720],\n                              opacity: 1,\n                              boxShadow: ['0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)', '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)', '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)']\n                            },\n                            transition: {\n                              scale: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              rotate: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                              },\n                              boxShadow: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              opacity: {\n                                duration: 0.3\n                              }\n                            },\n                            className: \"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                              border: '3px solid white',\n                              zIndex: 10\n                            },\n                            children: /*#__PURE__*/_jsxDEV(motion.span, {\n                              className: \"text-sm font-black text-gray-900\",\n                              animate: {\n                                scale: [1, 1.2, 1],\n                                rotate: [0, -10, 10, 0]\n                              },\n                              transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                              },\n                              children: \"\\u2713\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1673,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1648,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\",\n                            style: {\n                              background: league.borderColor,\n                              color: '#FFFFFF',\n                              fontSize: '11px'\n                            },\n                            children: userCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1689,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1611,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                          className: \"text-center\",\n                          whileHover: {\n                            scale: 1.05\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `1px 1px 2px ${league.shadowColor}`,\n                              background: `${league.borderColor}20`,\n                              border: `1px solid ${league.borderColor}40`\n                            },\n                            children: league.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1706,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1702,\n                          columnNumber: 27\n                        }, this)]\n                      }, leagueKey, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1606,\n                        columnNumber: 25\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1599,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/70 text-sm text-center mt-2\",\n                    children: \"Click any league to view its members and scroll to their section\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1723,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1581,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1741,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1742,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1731,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1387,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1386,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1385,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1376,\n          columnNumber: 9\n        }, this), false && ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1761,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1760,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1764,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1765,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1763,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1759,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1758,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1757,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1751,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1784,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1785,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1805,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1824,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1825,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1804,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1792,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1850,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1873,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1867,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1935,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1936,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1940,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1951,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1923,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1883,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1789,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1788,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1783,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1776,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1976,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1981,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1971,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2003,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center max-w-5xl mx-auto mb-4 md:mb-8\",\n                style: {\n                  gap: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px',\n                  padding: window.innerWidth <= 768 ? '8px' : '16px 24px'\n                },\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${shouldHighlightUser(topPerformers[1]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: window.innerWidth <= 768 ? '200px' : '280px',\n                    transform: shouldHighlightUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[1]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800 relative z-20\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2062,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2061,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2076,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2079,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                          user: topPerformers[1],\n                          size: \"md\",\n                          showOnlineStatus: true,\n                          style: {\n                            width: '40px',\n                            height: '40px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2092,\n                          columnNumber: 31\n                        }, this), console.log('🥈 Second place user:', topPerformers[1])]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2090,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2106,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2113,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2118,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2121,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2117,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2073,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2066,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2023,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${shouldHighlightUser(topPerformers[0]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: window.innerWidth <= 768 ? '240px' : '320px',\n                    transform: shouldHighlightUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[0]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  \"data-section\": \"podium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900 relative z-20\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2173,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2172,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2182,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2177,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2199,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2202,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"relative\",\n                          children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                            user: topPerformers[0],\n                            size: \"lg\",\n                            showOnlineStatus: false,\n                            style: {\n                              width: '48px',\n                              height: '48px'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2217,\n                            columnNumber: 33\n                          }, this), topPerformers[0].isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              position: 'absolute',\n                              bottom: '-2px',\n                              right: '-2px',\n                              width: '14px',\n                              height: '14px',\n                              backgroundColor: '#22c55e',\n                              borderRadius: '50%',\n                              border: '2px solid #ffffff',\n                              boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                              zIndex: 10\n                            },\n                            title: \"Online\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2228,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2216,\n                          columnNumber: 31\n                        }, this), console.log('🥇 First place user:', topPerformers[0]), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-gray-900\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2255,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2248,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2214,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-black truncate\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: topPerformers[0].name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2262,\n                          columnNumber: 31\n                        }, this), isCurrentUser(topPerformers[0]._id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 rounded-full text-xs font-black animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                            color: '#1f2937',\n                            boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                            border: '1px solid #FFFFFF',\n                            fontSize: '10px'\n                          },\n                          children: \"\\uD83C\\uDFAF YOU\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2273,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2261,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2302,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2288,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2307,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2318,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2319,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2317,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2323,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2316,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2327,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2328,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2326,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2332,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2325,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2315,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2306,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2193,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2186,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2132,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${shouldHighlightUser(topPerformers[2]._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                  style: {\n                    height: window.innerWidth <= 768 ? '200px' : '280px',\n                    transform: shouldHighlightUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                    filter: shouldHighlightUser(topPerformers[2]._id) ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))' : 'none',\n                    transition: 'all 0.3s ease',\n                    border: shouldHighlightUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                    borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                    background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900 relative z-20\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2382,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2381,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2396,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#1f2937',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2399,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                          user: topPerformers[2],\n                          size: \"md\",\n                          showOnlineStatus: true,\n                          style: {\n                            width: '40px',\n                            height: '40px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2411,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2410,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2423,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2430,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2435,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2438,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2434,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2393,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2386,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2343,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2014,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1997,\n              columnNumber: 17\n            }, this), selectedLeague ? /* SELECTED LEAGUE VIEW */\n            leagueUsers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: [leagueSystem[selectedLeague].leagueIcon, \" \", leagueSystem[selectedLeague].title, \" LEAGUE \", leagueSystem[selectedLeague].leagueIcon]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2470,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: [leagueUsers.length, \" champions in this league\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2484,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setSelectedLeague(null),\n                  className: \"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"\\u2190 Back to All Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2487,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2469,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: leagueUsers.map((champion, index) => {\n                    const actualRank = index + 1;\n                    const isCurrentUser = user && String(champion._id) === String(user._id);\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 0.1 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${shouldHighlightUser(champion._id) ? 'ring-8 ring-yellow-400 ring-opacity-100' : ''}`,\n                      style: {\n                        transform: shouldHighlightUser(champion._id) ? 'scale(1.05)' : 'scale(1)',\n                        filter: shouldHighlightUser(champion._id) ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))' : 'none',\n                        transition: 'all 0.3s ease',\n                        border: shouldHighlightUser(champion._id) ? '4px solid #FFD700' : 'none',\n                        borderRadius: shouldHighlightUser(champion._id) ? '16px' : '0px',\n                        background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                        position: 'relative',\n                        zIndex: isCurrentUser ? 10 : 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2546,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2552,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2551,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                                user: champion,\n                                size: \"sm\",\n                                showOnlineStatus: false,\n                                style: {\n                                  width: '32px',\n                                  height: '32px'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2567,\n                                columnNumber: 39\n                              }, this), champion.isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                                style: {\n                                  position: 'absolute',\n                                  bottom: '-2px',\n                                  right: '-2px',\n                                  width: '10px',\n                                  height: '10px',\n                                  backgroundColor: '#22c55e',\n                                  borderRadius: '50%',\n                                  border: '2px solid #ffffff',\n                                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                                  zIndex: 10\n                                },\n                                title: \"Online\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2578,\n                                columnNumber: 41\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-gray-900\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2603,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2596,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2566,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2549,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2614,\n                                  columnNumber: 41\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-3 py-1 rounded-full text-sm font-black animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                    border: '2px solid #FFFFFF',\n                                    textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                    fontSize: '12px',\n                                    fontWeight: '900'\n                                  },\n                                  children: \"\\uD83C\\uDFAF YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2625,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2613,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2643,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2611,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2610,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2652,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2672,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2673,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2665,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2682,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2683,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2675,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2664,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2650,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2539,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2533,\n                        columnNumber: 31\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2505,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2499,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2498,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2462,\n              columnNumber: 19\n            }, this) : /* ALL LEAGUES GROUPED VIEW */\n            Object.keys(leagueGroups).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              id: \"grouped-leagues-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: \"\\uD83C\\uDFC6 LEAGUE RANKINGS \\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2708,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: \"Click on any league icon above to see its members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2722,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2707,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 space-y-8\",\n                children: getOrderedLeagues().map(leagueKey => {\n                  const league = leagueSystem[leagueKey];\n                  const leagueData = leagueGroups[leagueKey];\n                  const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                  return /*#__PURE__*/_jsxDEV(motion.div, {\n                    ref: el => leagueRefs.current[leagueKey] = el,\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: 0.2,\n                      duration: 0.6\n                    },\n                    className: \"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\",\n                    id: `league-${leagueKey}`,\n                    \"data-league\": leagueKey,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\",\n                          style: {\n                            background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                            border: `2px solid ${league.borderColor}60`,\n                            boxShadow: `0 4px 20px ${league.shadowColor}40`\n                          },\n                          children: league.leagueIcon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2748,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                            className: \"text-2xl font-black mb-1\",\n                            style: {\n                              color: league.nameColor,\n                              textShadow: `2px 2px 4px ${league.shadowColor}`,\n                              filter: 'drop-shadow(0 0 8px currentColor)'\n                            },\n                            children: [league.title, \" LEAGUE\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2759,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-white/70 text-sm\",\n                            children: [leagueData.users.length, \" champions \\u2022 \", league.description]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2769,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2758,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2747,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => handleLeagueSelect(leagueKey),\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\",\n                        children: [\"View All (\", leagueData.users.length, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2774,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2746,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                      children: topUsers.map((champion, index) => {\n                        const isCurrentUser = user && champion._id === user._id;\n                        const leagueRank = index + 1;\n                        return /*#__PURE__*/_jsxDEV(motion.div, {\n                          \"data-user-id\": champion._id,\n                          \"data-user-rank\": leagueRank,\n                          initial: {\n                            opacity: 0,\n                            scale: 0.9\n                          },\n                          animate: {\n                            opacity: 1,\n                            scale: 1\n                          },\n                          transition: {\n                            delay: 0.3 + index * 0.1,\n                            duration: 0.4\n                          },\n                          whileHover: {\n                            scale: 1.02,\n                            y: -2\n                          },\n                          className: `relative ${shouldHighlightUser(champion._id) ? 'ring-2 ring-yellow-400/60' : ''}`,\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`,\n                            style: {\n                              boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: `${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2814,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\",\n                                style: {\n                                  background: league.borderColor,\n                                  color: '#FFFFFF',\n                                  border: '2px solid #FFFFFF'\n                                },\n                                children: [\"#\", leagueRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2817,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `relative mx-auto mb-3 ${isCurrentUser ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                                  user: champion,\n                                  size: \"md\",\n                                  showOnlineStatus: true,\n                                  style: {\n                                    width: '40px',\n                                    height: '40px'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2834,\n                                  columnNumber: 43\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                  },\n                                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                    className: \"w-2.5 h-2.5 text-gray-900\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 2851,\n                                    columnNumber: 47\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2844,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2829,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"text-sm font-bold mb-2 truncate\",\n                                style: {\n                                  color: champion.tier.nameColor\n                                },\n                                children: [champion.name, isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"ml-1 text-xs text-yellow-400\",\n                                  children: \"\\uD83D\\uDC51\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2863,\n                                  columnNumber: 45\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2857,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-lg font-black mb-2\",\n                                style: {\n                                  color: champion.tier.textColor\n                                },\n                                children: [champion.totalXP.toLocaleString(), \" XP\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2867,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex justify-center gap-3 text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83E\\uDDE0 \", champion.totalQuizzesTaken]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2872,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    color: champion.tier.textColor\n                                  },\n                                  children: [\"\\uD83D\\uDD25 \", champion.currentStreak]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2875,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2871,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2811,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2805,\n                            columnNumber: 37\n                          }, this)\n                        }, champion._id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2791,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2785,\n                      columnNumber: 29\n                    }, this), leagueData.users.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: [\"+\", leagueData.users.length - 3, \" more champions in this league\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2889,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2888,\n                      columnNumber: 31\n                    }, this)]\n                  }, leagueKey, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2735,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2728,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2699,\n              columnNumber: 19\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2912,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2913,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2918,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2907,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1993,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1987,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1374,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1328,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"2oJgcCnITao5T8TtrsxZjKrFxvY=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbRocket", "TbDiamond", "TbAward", "TbShield", "TbUsers", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "ProfilePicture", "OnlineStatusIndicator", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "reduxUser", "user", "localStorageUser", "userData", "localStorage", "getItem", "JSON", "parse", "tokenUser", "token", "payload", "atob", "split", "fullUserData", "setFullUserData", "console", "log", "redux", "final", "userId", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "userHasBeenShown", "setUserHasBeenShown", "autoScrollCompleted", "setAutoScrollCompleted", "leagueRefs", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "setTimeout", "leagueElement", "document", "querySelector", "getElementById", "current", "scrollIntoView", "behavior", "block", "inline", "style", "transform", "transition", "boxShadow", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "forceRefresh", "currentLevel", "level", "allLevels", "removeItem", "userLevel", "cachedRanking", "cacheTime", "now", "Date", "parseInt", "cached", "data", "xpLeaderboardResponse", "limit", "levelFilter", "includeInactive", "_t", "success", "filteredData", "totalQuizzesTaken", "slice", "map", "name", "profileImage", "profilePicture", "hasProfileData", "transformedData", "index", "email", "class", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "cacheData", "setItem", "stringify", "toString", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "reports", "isAdmin", "toLowerCase", "dataLevel", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "process", "env", "NODE_ENV", "userIdType", "role", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "fetchFullUserData", "response", "userDataWithProfile", "userDataAlt", "includes", "userInRanking", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "refreshWithRetry", "attempts", "i", "_event$detail", "newTotalXP", "updatedUser", "Promise", "resolve", "window", "addEventListener", "clearInterval", "removeEventListener", "leagueData", "userInLeague", "topPerformers", "otherPerformers", "getUserLeagueInfo", "isInPodium", "some", "performer", "podiumPosition", "position", "_leagueData$users", "totalUsers", "userLeagueInfo", "isCurrentUser", "shouldHighlightUser", "handlePageClick", "rankingData<PERSON>ength", "<PERSON><PERSON>ser", "completed", "hasData", "scrollToUser", "podiumSection", "userElement", "timer", "clearTimeout", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "endDate", "isActive", "text", "RankingSkeleton", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "_", "onClick", "div", "initial", "opacity", "y", "animate", "exit", "x", "duration", "repeat", "Infinity", "delay", "left", "top", "padding", "innerWidth", "background", "fontSize", "size", "showOnlineStatus", "border", "isOnline", "bottom", "right", "width", "height", "backgroundColor", "borderRadius", "zIndex", "username", "points", "toLocaleString", "_leagueData$users2", "leagueInfo", "toUpperCase", "quizzesCompleted", "quizzesTaken", "streak", "avgScore", "_leagueData$users3", "undefined", "h3", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "scale", "_leagueGroups$leagueK2", "isSelected", "userCount", "whileHover", "button", "whileTap", "ease", "rotate", "span", "disabled", "rotateY", "backgroundPosition", "backgroundSize", "fontWeight", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "stat", "gap", "ref", "createElement", "h2", "champion", "actualRank", "topUsers", "el", "leagueRank", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbRocket,\n  TbDiamond,\n  TbAward,\n  TbShield,\n  TbUsers\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport OnlineStatusIndicator from '../../../components/common/OnlineStatusIndicator';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const reduxUser = userState.user || null;\n\n  // Try multiple sources for user data\n  const localStorageUser = (() => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch {\n      return null;\n    }\n  })();\n\n  const tokenUser = (() => {\n    try {\n      const token = localStorage.getItem('token');\n      if (token) {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return payload;\n      }\n      return null;\n    } catch {\n      return null;\n    }\n  })();\n\n  // Use the first available user data\n  const user = reduxUser || localStorageUser || tokenUser;\n\n  // State for full user data\n  const [fullUserData, setFullUserData] = useState(null);\n\n  // Debug: Log all user sources\n  console.log('🔍 User Data Sources:', {\n    redux: reduxUser,\n    localStorage: localStorageUser,\n    token: tokenUser,\n    final: user\n  });\n\n  // Debug: Log user data structure for migrated users (simplified)\n  if (user && !fullUserData) {\n    console.log('🔍 Loading user data for:', user.userId);\n  }\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const [userHasBeenShown, setUserHasBeenShown] = useState(false);\n  const [autoScrollCompleted, setAutoScrollCompleted] = useState(false);\n\n  // Refs for league sections\n  const leagueRefs = useRef({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection with unique visual effect\n  const handleLeagueSelect = (leagueKey) => {\n    console.log('🎯 League selected:', leagueKey);\n\n    // Set selected league with unique visual effect\n    setSelectedLeague(leagueKey);\n    setShowLeagueView(true);\n    setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n\n    // Scroll to league section with smooth animation\n    setTimeout(() => {\n      const leagueElement = document.querySelector(`[data-league=\"${leagueKey}\"]`) ||\n                           document.getElementById(`league-${leagueKey}`) ||\n                           leagueRefs.current[leagueKey];\n\n      if (leagueElement) {\n        leagueElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n\n        // Add unique visual effect - pulse animation\n        leagueElement.style.transform = 'scale(1.02)';\n        leagueElement.style.transition = 'all 0.3s ease';\n        leagueElement.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';\n\n        setTimeout(() => {\n          leagueElement.style.transform = 'scale(1)';\n          leagueElement.style.boxShadow = '';\n        }, 600);\n      }\n    }, 100);\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async (forceRefresh = false) => {\n    try {\n      // Clear old caches for other levels to prevent contamination\n      const currentLevel = user?.level || 'primary';\n      const allLevels = ['primary', 'secondary', 'advance'];\n      allLevels.forEach(level => {\n        if (level !== currentLevel) {\n          localStorage.removeItem(`ranking_cache_${level}`);\n          localStorage.removeItem(`ranking_cache_time_${level}`);\n        }\n      });\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const userLevel = user?.level || 'primary';\n        const cachedRanking = localStorage.getItem(`ranking_cache_${userLevel}`);\n        const cacheTime = localStorage.getItem(`ranking_cache_time_${userLevel}`);\n        const now = Date.now();\n\n        // Use cache if less than 2 minutes old and for the same level\n        if (cachedRanking && cacheTime && (now - parseInt(cacheTime)) < 120000) {\n          const cached = JSON.parse(cachedRanking);\n          setRankingData(cached.data || []);\n          setCurrentUserRank(cached.userRank);\n          setLoading(false);\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...', forceRefresh ? '(Force Refresh)' : '');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false,\n          // Add timestamp for cache busting when force refreshing\n          ...(forceRefresh && { _t: Date.now() })\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          // Debug: Check first few users' profile data\n          console.log('🔍 First 3 users profile data:', filteredData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          // Debug: Check final transformed data for top 3 users\n          console.log('🏆 Top 3 transformed users:', transformedData.slice(0, 3).map(u => ({\n            _id: u._id,\n            name: u.name,\n            profileImage: u.profileImage,\n            profilePicture: u.profilePicture,\n            hasProfileData: !!(u.profileImage || u.profilePicture)\n          })));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          // Cache the results with level-specific key\n          const userLevel = user?.level || 'primary';\n          const cacheData = {\n            data: transformedData,\n            userRank: userRankIndex >= 0 ? userRankIndex + 1 : null\n          };\n          localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n          localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => {\n            // Filter out invalid users\n            if (!userData || !userData._id) return false;\n\n            // Apply level filtering for non-admin users\n            if (!userData.isAdmin && user?.level) {\n              const userLevel = user.level.toLowerCase();\n              const dataLevel = (userData.level || 'primary').toLowerCase();\n\n              if (userLevel === 'primary') {\n                // Primary users should only see primary users\n                return dataLevel === 'primary';\n              } else if (userLevel === 'secondary') {\n                // Secondary users should only see secondary users\n                return dataLevel === 'secondary';\n              } else if (userLevel === 'advance') {\n                // Advance users should only see advance users\n                return dataLevel === 'advance';\n              }\n            }\n\n            return true; // Include admins and when no level filtering needed\n          })\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profileImage || userData.profilePicture || '',\n              profileImage: userData.profileImage || userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n\n        // Cache the fallback results with level-specific key\n        const userLevel = user?.level || 'primary';\n        const cacheData = {\n          data: transformedData,\n          userRank: null // Will be set below after finding user rank\n        };\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Update cache with final user rank\n        cacheData.userRank = userRank >= 0 ? userRank + 1 : null;\n        localStorage.setItem(`ranking_cache_${userLevel}`, JSON.stringify(cacheData));\n        localStorage.setItem(`ranking_cache_time_${userLevel}`, Date.now().toString());\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking (development only)\n        if (process.env.NODE_ENV === 'development') {\n          console.log('🔍 Enhanced User ranking debug:', {\n            currentUser: user?.name,\n            userId: user?._id,\n            userIdType: typeof user?._id,\n            isAdmin: user?.role === 'admin' || user?.isAdmin,\n            userXP: user?.totalXP,\n            userRankIndex: userRank,\n            userRankPosition: userRank >= 0 ? userRank + 1 : null,\n            totalRankedUsers: transformedData.length,\n            firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n            exactMatch: transformedData.find(item => item._id === user?._id),\n            stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n            nameMatch: transformedData.find(item => item.name === user?.name)\n          });\n        }\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch full user data\n  const fetchFullUserData = async () => {\n    if (!user?.userId) {\n      console.log('❌ No userId available:', user);\n      return;\n    }\n\n    try {\n      console.log('🔍 Fetching full user data for userId:', user.userId);\n      const response = await getAllUsers();\n      console.log('📋 getAllUsers response:', response);\n\n      if (response.success) {\n        console.log('📊 Total users found:', response.data.length);\n        console.log('🔍 Looking for userId:', user.userId);\n        console.log('📝 First 5 user IDs:', response.data.slice(0, 5).map(u => ({ id: u._id, name: u.name })));\n\n        const userData = response.data.find(u => String(u._id) === String(user.userId));\n        if (userData) {\n          console.log('✅ Found full user data:', userData);\n          // Ensure profile picture properties are set\n          const userDataWithProfile = {\n            ...userData,\n            profilePicture: userData.profileImage || userData.profilePicture || '',\n            profileImage: userData.profileImage || userData.profilePicture || ''\n          };\n          setFullUserData(userDataWithProfile);\n        } else {\n          console.log('❌ User not found in users list');\n          console.log('🔍 Trying alternative search methods...');\n\n          // Try different ID formats\n          const userDataAlt = response.data.find(u =>\n            u._id === user.userId ||\n            u.id === user.userId ||\n            String(u._id).includes(user.userId) ||\n            String(user.userId).includes(u._id)\n          );\n\n          if (userDataAlt) {\n            console.log('✅ Found user with alternative method:', userDataAlt);\n            // Ensure profile picture properties are set\n            const userDataWithProfile = {\n              ...userDataAlt,\n              profilePicture: userDataAlt.profileImage || userDataAlt.profilePicture || '',\n              profileImage: userDataAlt.profileImage || userDataAlt.profilePicture || ''\n            };\n            setFullUserData(userDataWithProfile);\n          } else {\n            console.log('❌ User not found with any method');\n          }\n        }\n      } else {\n        console.log('❌ getAllUsers failed:', response);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching user data:', error);\n    }\n  };\n\n  // Try to find user in ranking data as fallback\n  useEffect(() => {\n    if (!fullUserData && user?.userId && rankingData.length > 0) {\n      console.log('🔍 Trying to find user in ranking data...');\n      const userInRanking = rankingData.find(u => String(u._id) === String(user.userId));\n      if (userInRanking) {\n        console.log('✅ Found user in ranking data:', userInRanking);\n        // Ensure profile picture properties are set\n        const userDataWithProfile = {\n          ...userInRanking,\n          profilePicture: userInRanking.profileImage || userInRanking.profilePicture || '',\n          profileImage: userInRanking.profileImage || userInRanking.profilePicture || ''\n        };\n        setFullUserData(userDataWithProfile);\n      } else {\n        console.log('❌ User not found in ranking data either');\n      }\n    }\n  }, [rankingData, user, fullUserData]);\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n    fetchFullUserData(); // Fetch full user data\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData(true); // Force refresh when returning from quiz\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n\n      // Clear any cached data to ensure fresh fetch\n      localStorage.removeItem('rankingCache');\n      localStorage.removeItem('userRankingPosition');\n      localStorage.removeItem('leaderboardData');\n\n      // Immediate refresh after quiz completion with multiple attempts\n      const refreshWithRetry = async (attempts = 3) => {\n        for (let i = 0; i < attempts; i++) {\n          try {\n            console.log(`🔄 Refreshing ranking data (attempt ${i + 1}/${attempts})`);\n            await fetchRankingData(true); // Force refresh to bypass cache\n\n            // Verify the XP was updated by checking if user's XP matches the event data\n            if (event.detail?.newTotalXP && user) {\n              const updatedUser = rankingData.find(u => String(u._id) === String(user._id));\n              if (updatedUser && updatedUser.totalXP >= event.detail.newTotalXP) {\n                console.log('✅ XP update confirmed in ranking data');\n                break;\n              }\n            }\n\n            // Wait before retry\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          } catch (error) {\n            console.error(`❌ Ranking refresh attempt ${i + 1} failed:`, error);\n            if (i < attempts - 1) {\n              await new Promise(resolve => setTimeout(resolve, 1500));\n            }\n          }\n        }\n      };\n\n      // Start refresh with delay to ensure server processing\n      setTimeout(() => {\n        refreshWithRetry();\n      }, 1000);\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Auto-select user's current league when data loads\n  useEffect(() => {\n    if (user && leagueGroups && Object.keys(leagueGroups).length > 0 && !selectedLeague) {\n      // Find user's current league\n      for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n        const userInLeague = leagueData.users.find(u => String(u._id) === String(user._id));\n        if (userInLeague) {\n          console.log('🎯 Auto-selecting user league:', leagueKey);\n          setSelectedLeague(leagueKey);\n          setShowLeagueView(true);\n          setLeagueUsers(leagueData.users);\n          break;\n        }\n      }\n    }\n  }, [user, leagueGroups, selectedLeague]);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n\n\n  // Get user's current league information\n  const getUserLeagueInfo = () => {\n    if (!user?._id) return null;\n\n    // Check if user is in top 3 (podium)\n    const isInPodium = topPerformers.some(performer => String(performer._id) === String(user._id));\n    if (isInPodium) {\n      const podiumPosition = topPerformers.findIndex(performer => String(performer._id) === String(user._id)) + 1;\n      return {\n        type: 'podium',\n        position: podiumPosition,\n        league: 'Champion Podium',\n        leagueKey: 'podium'\n      };\n    }\n\n    // Find user's league\n    for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n      const userInLeague = leagueData.users?.find(u => String(u._id) === String(user._id));\n      if (userInLeague) {\n        const position = leagueData.users.findIndex(u => String(u._id) === String(user._id)) + 1;\n        return {\n          type: 'league',\n          position: position,\n          league: leagueData.title,\n          leagueKey: leagueKey,\n          totalUsers: leagueData.users.length\n        };\n      }\n    }\n\n    return null;\n  };\n\n  const userLeagueInfo = getUserLeagueInfo();\n\n  // Helper function to check if a user is the current user\n  const isCurrentUser = (userId) => {\n    return user && String(userId) === String(user._id);\n  };\n\n  // Helper function to check if user should be highlighted (only before they've been shown)\n  const shouldHighlightUser = (userId) => {\n    return isCurrentUser(userId) && !userHasBeenShown;\n  };\n\n  // Allow users to click anywhere to disable highlighting\n  const handlePageClick = () => {\n    if (!userHasBeenShown) {\n      setUserHasBeenShown(true);\n      console.log('👆 User clicked - highlighting disabled');\n    }\n  };\n\n  // Reset highlighting when user or league changes\n  useEffect(() => {\n    setUserHasBeenShown(false);\n    setAutoScrollCompleted(false); // Reset auto-scroll state\n  }, [user?._id, selectedLeague]);\n\n  // Auto-scroll to user position ONLY on first visit\n  useEffect(() => {\n    console.log('🔄 Auto-scroll check:', {\n      userId: user?._id,\n      autoScrollCompleted,\n      rankingDataLength: rankingData.length\n    });\n\n    // Only scroll if user exists, hasn't been scrolled yet, and we have data\n    if (!user?._id || autoScrollCompleted || rankingData.length === 0) {\n      console.log('❌ Auto-scroll skipped:', {\n        hasUser: !!user?._id,\n        completed: autoScrollCompleted,\n        hasData: rankingData.length > 0\n      });\n      return;\n    }\n\n    const scrollToUser = () => {\n      console.log('🎯 Starting auto-scroll for user:', user._id);\n\n      // First, try to find user in any ranking data\n      const userInRanking = rankingData.find(u => String(u._id) === String(user._id));\n      if (!userInRanking) {\n        console.log('❌ User not found in ranking data');\n        setAutoScrollCompleted(true); // Mark as completed even if not found\n        return;\n      }\n\n      console.log('✅ User found in ranking at position:', userInRanking.rank);\n\n      // Check if user is in top 3 (podium)\n      const isInPodium = userInRanking.rank <= 3;\n      console.log('🏆 Is user in podium?', isInPodium);\n\n      if (isInPodium) {\n        // Scroll to podium section\n        console.log('📍 Scrolling to podium section...');\n        const podiumSection = document.querySelector('[data-section=\"podium\"]');\n        console.log('🎪 Podium section found:', !!podiumSection);\n        if (podiumSection) {\n          setTimeout(() => {\n            podiumSection.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to podium');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          setAutoScrollCompleted(true);\n        }\n      } else {\n        // Look for user element in the ranking list\n        console.log('📍 Looking for user element with ID:', user._id);\n        const userElement = document.querySelector(`[data-user-id=\"${user._id}\"]`);\n        console.log('🎯 User element found:', !!userElement);\n        if (userElement) {\n          setTimeout(() => {\n            userElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n            console.log('✅ Scrolled to user position');\n            // Mark as completed after scroll\n            setTimeout(() => {\n              setUserHasBeenShown(true);\n              setAutoScrollCompleted(true);\n              console.log('✅ Auto-scroll completed');\n            }, 1000);\n          }, 500);\n        } else {\n          console.log('❌ User element not found in DOM');\n          setAutoScrollCompleted(true);\n        }\n      }\n    };\n\n    // Delay to ensure DOM is ready, but not too long\n    const timer = setTimeout(scrollToUser, 2000);\n    return () => clearTimeout(timer);\n  }, [user?._id, rankingData, autoScrollCompleted]);\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Skeleton loading component\n  const RankingSkeleton = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header Skeleton */}\n        <div className=\"text-center mb-12\">\n          <div className=\"h-12 bg-white/10 rounded-lg w-96 mx-auto mb-4 animate-pulse\"></div>\n          <div className=\"h-6 bg-white/5 rounded w-64 mx-auto animate-pulse\"></div>\n        </div>\n\n        {/* Podium Skeleton */}\n        <div className=\"flex justify-center items-end mb-16 space-x-8\">\n          {[2, 1, 3].map((position) => (\n            <div key={position} className={`text-center ${position === 1 ? 'order-2' : position === 2 ? 'order-1' : 'order-3'}`}>\n              <div className={`w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full mx-auto mb-4 animate-pulse`}></div>\n              <div className=\"h-4 bg-white/10 rounded w-16 mx-auto mb-2 animate-pulse\"></div>\n              <div className=\"h-3 bg-white/5 rounded w-12 mx-auto animate-pulse\"></div>\n            </div>\n          ))}\n        </div>\n\n        {/* List Skeleton */}\n        <div className=\"space-y-4 max-w-4xl mx-auto\">\n          {[...Array(8)].map((_, i) => (\n            <div key={i} className=\"bg-white/5 rounded-xl p-4 animate-pulse\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-white/10 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-white/10 rounded w-32 mb-2\"></div>\n                  <div className=\"h-3 bg-white/5 rounded w-24\"></div>\n                </div>\n                <div className=\"h-6 bg-white/10 rounded w-16\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Show skeleton only on initial load\n  if (loading && rankingData.length === 0) {\n    return <RankingSkeleton />;\n  }\n\n  return (\n    <>\n      <style>{`\n        /* Dark background for better color visibility */\n        body {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;\n          min-height: 100vh;\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container {\n          background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);\n          min-height: 100vh;\n          color: #ffffff;\n        }\n\n        /* Fix black text visibility - Enhanced */\n        .ranking-page-container * {\n          color: inherit;\n        }\n\n        .ranking-page-container .text-black,\n        .ranking-page-container .text-gray-900,\n        .ranking-page-container h1,\n        .ranking-page-container h2,\n        .ranking-page-container h3,\n        .ranking-page-container h4,\n        .ranking-page-container h5,\n        .ranking-page-container h6,\n        .ranking-page-container p,\n        .ranking-page-container span,\n        .ranking-page-container div {\n          color: #ffffff !important;\n        }\n\n        .ranking-page-container [style*=\"color: #000000\"],\n        .ranking-page-container [style*=\"color: black\"],\n        .ranking-page-container [style*=\"color:#000000\"],\n        .ranking-page-container [style*=\"color:black\"],\n        .ranking-page-container [style*=\"color: #1f2937\"],\n        .ranking-page-container [style*=\"color:#1f2937\"] {\n          color: #ffffff !important;\n        }\n\n        /* Force white text for names and content */\n        .ranking-page-container .font-bold,\n        .ranking-page-container .font-black,\n        .ranking-page-container .font-semibold,\n        .ranking-page-container .font-medium {\n          color: #ffffff !important;\n        }\n\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page-container ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden\" onClick={handlePageClick}>\n\n      {/* Highlighting Notification */}\n      {!userHasBeenShown && user && (\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-500/90 backdrop-blur-sm text-black px-4 py-2 rounded-lg shadow-lg text-sm font-medium\"\n        >\n          🎯 Finding your position in the rankings...\n        </motion.div>\n      )}\n\n      {/* Animated Background Elements - Darker Theme */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-cyan-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-2 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-4 md:py-6 lg:py-8\"\n          style={{\n            padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px'\n          }}\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n\n\n                {/* User League Info Display */}\n                {userLeagueInfo && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-bold shadow-lg\"\n                    style={{\n                      background: userLeagueInfo.type === 'podium'\n                        ? 'linear-gradient(135deg, #FFD700, #FFA500)'\n                        : 'linear-gradient(135deg, #3B82F6, #8B5CF6)',\n                      color: userLeagueInfo.type === 'podium' ? '#1F2937' : '#FFFFFF',\n                      boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',\n                      fontSize: window.innerWidth < 768 ? '0.9rem' : '1rem'\n                    }}\n                  >\n                    <TbTrophy className=\"w-5 h-5 md:w-6 md:h-6\" />\n                    <span>\n                      {userLeagueInfo.type === 'podium'\n                        ? `🏆 Podium #${userLeagueInfo.position}`\n                        : `${userLeagueInfo.league} #${userLeagueInfo.position}`}\n                    </span>\n                  </motion.div>\n                )}\n\n                {/* User Profile Window */}\n                {fullUserData && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-4 border border-blue-400/30 shadow-2xl max-w-sm\"\n                  >\n                    <div className=\"flex items-center gap-4\">\n                      {/* Profile Picture with Online Status */}\n                      <div className=\"flex-shrink-0 relative\">\n                        <ProfilePicture\n                          user={fullUserData}\n                          size=\"xl\"\n                          showOnlineStatus={false}\n                          style={{\n                            border: '3px solid #facc15',\n                            boxShadow: '0 10px 25px rgba(0,0,0,0.15)'\n                          }}\n                        />\n                        {/* Only show online dot if user is actually online */}\n                        {fullUserData.isOnline && (\n                          <div\n                            style={{\n                              position: 'absolute',\n                              bottom: '4px',\n                              right: '4px',\n                              width: '16px',\n                              height: '16px',\n                              backgroundColor: '#22c55e',\n                              borderRadius: '50%',\n                              border: '3px solid #ffffff',\n                              boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                              zIndex: 10\n                            }}\n                            title=\"Online\"\n                          />\n                        )}\n                      </div>\n\n                      {/* User Details */}\n                      <div className=\"flex-grow\">\n                        <h3 className=\"text-lg font-bold text-white mb-2 truncate\">\n                          {fullUserData.name || fullUserData.username || 'User'}\n                        </h3>\n\n                        {/* Stats Grid */}\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                          <div className=\"bg-green-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-green-300 text-xs\">Total XP</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple XP field names for migrated users\n                                const xp = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                return xp.toLocaleString();\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-purple-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-purple-300 text-xs\">Rank</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try to find user in ranking data\n                                const userInRanking = rankingData.find(u => String(u._id) === String(fullUserData._id));\n                                return userInRanking ? `#${userInRanking.rank}` : (currentUserRank ? `#${currentUserRank}` : 'N/A');\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-blue-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-blue-300 text-xs\">League</div>\n                            <div className=\"text-white font-bold text-xs\">\n                              {(() => {\n                                // Find user's league with icon - try multiple XP sources\n                                const userXP = fullUserData.totalXP || fullUserData.xp || fullUserData.points || fullUserData.totalPoints || 0;\n                                for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                                  const userInLeague = leagueData.users?.find(u => String(u._id) === String(fullUserData._id));\n                                  if (userInLeague) {\n                                    const leagueInfo = getUserLeague(userXP);\n                                    return `${leagueInfo.leagueIcon} ${leagueKey.toUpperCase()}`;\n                                  }\n                                }\n                                // Fallback: calculate league from XP even if not in league data\n                                if (userXP > 0) {\n                                  const leagueInfo = getUserLeague(userXP);\n                                  return `${leagueInfo.leagueIcon} ${leagueInfo.league.toUpperCase()}`;\n                                }\n                                return '🔰 Unranked';\n                              })()}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-orange-500/20 rounded-lg p-2 text-center\">\n                            <div className=\"text-orange-300 text-xs\">Quizzes</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                // Try multiple quiz count field names\n                                return fullUserData.quizzesCompleted || fullUserData.totalQuizzesTaken || fullUserData.quizzesTaken || fullUserData.totalQuizzes || 0;\n                              })()}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Additional Stats Row */}\n                        <div className=\"grid grid-cols-3 gap-2 mt-2 text-xs\">\n                          <div className=\"bg-yellow-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-yellow-300 text-xs\">Level</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentLevel || fullUserData.level || 1}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-red-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-red-300 text-xs\">Streak</div>\n                            <div className=\"text-white font-bold\">\n                              {fullUserData.currentStreak || fullUserData.streak || 0}\n                            </div>\n                          </div>\n\n                          <div className=\"bg-cyan-500/20 rounded-lg p-1.5 text-center\">\n                            <div className=\"text-cyan-300 text-xs\">Avg Score</div>\n                            <div className=\"text-white font-bold\">\n                              {(() => {\n                                const avgScore = fullUserData.averageScore || fullUserData.avgScore || 0;\n                                return Math.round(avgScore);\n                              })()}%\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* League Position */}\n                        {(() => {\n                          // Find user's position in their league\n                          for (const [leagueKey, leagueData] of Object.entries(leagueGroups)) {\n                            const userIndex = leagueData.users?.findIndex(u => String(u._id) === String(fullUserData._id));\n                            if (userIndex !== -1 && userIndex !== undefined) {\n                              return (\n                                <div className=\"mt-2 text-center\">\n                                  <div className=\"bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg p-1.5\">\n                                    <div className=\"text-yellow-300 text-xs\">League Position</div>\n                                    <div className=\"text-white font-bold text-sm\">\n                                      #{userIndex + 1} of {leagueData.users.length}\n                                    </div>\n                                  </div>\n                                </div>\n                              );\n                            }\n                          }\n                          return null;\n                        })()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n\n\n\n\n\n\n\n\n\n\n                {/* League Selection Section */}\n                <div className=\"flex flex-col items-center gap-4 bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-4xl mx-auto\">\n                  {/* LEAGUES Title */}\n                  <motion.h3\n                    className=\"text-2xl md:text-3xl font-black mb-2\"\n                    style={{\n                      background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      filter: 'drop-shadow(0 0 10px #FFD700)'\n                    }}\n                    animate={{ scale: [1, 1.02, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    🏆 LEAGUES 🏆\n                  </motion.h3>\n\n                  {/* League Icons */}\n                  <div className=\"flex flex-wrap items-center justify-center gap-3 md:gap-4\">\n                    {getOrderedLeagues().map((leagueKey) => {\n                      const league = leagueSystem[leagueKey];\n                      const isSelected = selectedLeague === leagueKey;\n                      const userCount = leagueGroups[leagueKey]?.users.length || 0;\n\n                      return (\n                        <motion.div\n                          key={leagueKey}\n                          className=\"flex flex-col items-center gap-2\"\n                          whileHover={{ scale: 1.05 }}\n                        >\n                          <motion.button\n                            whileHover={{ scale: 1.1, y: -3 }}\n                            whileTap={{ scale: 0.95 }}\n                            onClick={() => handleLeagueSelect(leagueKey)}\n                            className={`relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-2xl transition-all duration-300 ${\n                              isSelected\n                                ? 'ring-4 ring-yellow-400 ring-opacity-100 shadow-2xl'\n                                : 'hover:ring-2 hover:ring-white/30'\n                            }`}\n                            style={{\n                              background: isSelected\n                                ? `linear-gradient(135deg, ${league.borderColor}80, ${league.textColor}50, ${league.borderColor}80)`\n                                : `linear-gradient(135deg, ${league.borderColor}60, ${league.textColor}30)`,\n                              border: `3px solid ${isSelected ? '#FFD700' : league.borderColor + '80'}`,\n                              boxShadow: isSelected\n                                ? `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080, 0 6px 30px ${league.shadowColor}80`\n                                : `0 4px 15px ${league.shadowColor}40`,\n                              transform: isSelected ? 'scale(1.1)' : 'scale(1)',\n                              filter: isSelected ? 'brightness(1.3) saturate(1.2)' : 'brightness(1)'\n                            }}\n                            animate={isSelected ? {\n                              boxShadow: [\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`,\n                                `0 0 40px ${league.shadowColor}100, 0 0 80px #FFD700A0`,\n                                `0 0 30px ${league.shadowColor}80, 0 0 60px #FFD70080`\n                              ],\n                              scale: [1.1, 1.15, 1.1]\n                            } : {}}\n                            transition={{\n                              duration: 2,\n                              repeat: isSelected ? Infinity : 0,\n                              ease: \"easeInOut\"\n                            }}\n                            title={`Click to view ${league.title} League (${userCount} users)`}\n                          >\n                            <span className=\"text-3xl md:text-4xl\">{league.leagueIcon}</span>\n                            {isSelected && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -360, opacity: 0 }}\n                                animate={{\n                                  scale: [1, 1.3, 1],\n                                  rotate: [0, 360, 720],\n                                  opacity: 1,\n                                  boxShadow: [\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)',\n                                    '0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 1)',\n                                    '0 0 15px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8)'\n                                  ]\n                                }}\n                                transition={{\n                                  scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                                  rotate: { duration: 4, repeat: Infinity, ease: \"linear\" },\n                                  boxShadow: { duration: 1.5, repeat: Infinity, ease: \"easeInOut\" },\n                                  opacity: { duration: 0.3 }\n                                }}\n                                className=\"absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 rounded-full flex items-center justify-center border-3 border-white shadow-lg\"\n                                style={{\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                  border: '3px solid white',\n                                  zIndex: 10\n                                }}\n                              >\n                                <motion.span\n                                  className=\"text-sm font-black text-gray-900\"\n                                  animate={{\n                                    scale: [1, 1.2, 1],\n                                    rotate: [0, -10, 10, 0]\n                                  }}\n                                  transition={{\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\"\n                                  }}\n                                >\n                                  ✓\n                                </motion.span>\n                              </motion.div>\n                            )}\n                            <div\n                              className=\"absolute -bottom-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white\"\n                              style={{\n                                background: league.borderColor,\n                                color: '#FFFFFF',\n                                fontSize: '11px'\n                              }}\n                            >\n                              {userCount}\n                            </div>\n                          </motion.button>\n\n                          {/* League Name */}\n                          <motion.div\n                            className=\"text-center\"\n                            whileHover={{ scale: 1.05 }}\n                          >\n                            <div\n                              className=\"text-xs md:text-sm font-bold px-2 py-1 rounded-lg\"\n                              style={{\n                                color: league.nameColor,\n                                textShadow: `1px 1px 2px ${league.shadowColor}`,\n                                background: `${league.borderColor}20`,\n                                border: `1px solid ${league.borderColor}40`\n                              }}\n                            >\n                              {league.title}\n                            </div>\n                          </motion.div>\n                        </motion.div>\n                      );\n                    })}\n                  </div>\n\n                  <p className=\"text-white/70 text-sm text-center mt-2\">\n                    Click any league to view its members and scroll to their section\n                  </p>\n                </div>\n\n\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice - DISABLED FOR TESTING */}\n        {false && (user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Header removed - using ProtectedRoute header only */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div\n                    className=\"flex items-end justify-center max-w-5xl mx-auto mb-4 md:mb-8\"\n                    style={{\n                      gap: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '32px',\n                      padding: window.innerWidth <= 768 ? '8px' : '16px 24px'\n                    }}\n                  >\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && String(topPerformers[1]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${\n                          shouldHighlightUser(topPerformers[1]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '200px' : '280px',\n                          transform: shouldHighlightUser(topPerformers[1]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[1]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[1]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[1]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[1]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center z-10\">\n                          <span className=\"text-2xl font-black text-gray-800 relative z-20\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <ProfilePicture\n                                user={topPerformers[1]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                              {/* Debug: Show user data */}\n                              {console.log('🥈 Second place user:', topPerformers[1])}\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && String(topPerformers[0]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${\n                          shouldHighlightUser(topPerformers[0]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '240px' : '320px',\n                          transform: shouldHighlightUser(topPerformers[0]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[0]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[0]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[0]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[0]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                        data-section=\"podium\"\n\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center z-10\">\n                          <span className=\"text-3xl font-black text-yellow-900 relative z-20\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n\n                              <div className=\"relative\">\n                                <ProfilePicture\n                                  user={topPerformers[0]}\n                                  size=\"lg\"\n                                  showOnlineStatus={false}\n                                  style={{\n                                    width: '48px',\n                                    height: '48px'\n                                  }}\n                                />\n                                {/* Only show online dot if user is actually online */}\n                                {topPerformers[0].isOnline && (\n                                  <div\n                                    style={{\n                                      position: 'absolute',\n                                      bottom: '-2px',\n                                      right: '-2px',\n                                      width: '14px',\n                                      height: '14px',\n                                      backgroundColor: '#22c55e',\n                                      borderRadius: '50%',\n                                      border: '2px solid #ffffff',\n                                      boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                                      zIndex: 10\n                                    }}\n                                    title=\"Online\"\n                                  />\n                                )}\n                              </div>\n                              {/* Debug: Show user data */}\n                              {console.log('🥇 First place user:', topPerformers[0])}\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-gray-900\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <div className=\"flex items-center justify-center gap-2 mb-2\">\n                              <h3\n                                className=\"text-lg font-black truncate\"\n                                style={{\n                                  color: topPerformers[0].tier.nameColor,\n                                  textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                  filter: 'drop-shadow(0 0 8px currentColor)'\n                                }}\n                              >\n                                {topPerformers[0].name}\n                              </h3>\n                              {isCurrentUser(topPerformers[0]._id) && (\n                                <span\n                                  className=\"px-2 py-1 rounded-full text-xs font-black animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#1f2937',\n                                    boxShadow: '0 2px 8px rgba(255,215,0,0.8)',\n                                    border: '1px solid #FFFFFF',\n                                    fontSize: '10px'\n                                  }}\n                                >\n                                  🎯 YOU\n                                </span>\n                              )}\n                            </div>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && String(topPerformers[2]._id) === String(user._id) ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${\n                          shouldHighlightUser(topPerformers[2]._id)\n                            ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                            : ''\n                        }`}\n                        style={{\n                          height: window.innerWidth <= 768 ? '200px' : '280px',\n                          transform: shouldHighlightUser(topPerformers[2]._id) ? 'scale(1.08)' : 'scale(1)',\n                          filter: shouldHighlightUser(topPerformers[2]._id)\n                            ? 'brightness(1.3) saturate(1.2) drop-shadow(0 0 30px rgba(255, 215, 0, 1))'\n                            : 'none',\n                          transition: 'all 0.3s ease',\n                          border: shouldHighlightUser(topPerformers[2]._id) ? '4px solid #FFD700' : 'none',\n                          borderRadius: isCurrentUser(topPerformers[2]._id) ? '20px' : '0px',\n                          background: isCurrentUser(topPerformers[2]._id) ? 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1))' : 'transparent'\n                        }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center z-10\">\n                          <span className=\"text-xl font-black text-amber-900 relative z-20\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#1f2937',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <ProfilePicture\n                                user={topPerformers[2]}\n                                size=\"md\"\n                                showOnlineStatus={true}\n                                style={{\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              />\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n\n\n\n\n\n\n\n                </motion.div>\n              )}\n\n              {/* LEAGUE-BASED RANKING DISPLAY */}\n              {selectedLeague ? (\n                /* SELECTED LEAGUE VIEW */\n                leagueUsers.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                  >\n                    {/* Selected League Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: `linear-gradient(45deg, ${leagueSystem[selectedLeague].borderColor}, ${leagueSystem[selectedLeague].textColor})`,\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: `drop-shadow(0 0 12px ${leagueSystem[selectedLeague].borderColor})`\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        {leagueSystem[selectedLeague].leagueIcon} {leagueSystem[selectedLeague].title} LEAGUE {leagueSystem[selectedLeague].leagueIcon}\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        {leagueUsers.length} champions in this league\n                      </p>\n                      <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setSelectedLeague(null)}\n                        className=\"mt-4 px-6 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        ← Back to All Leagues\n                      </motion.button>\n                    </div>\n\n                    {/* Selected League Users */}\n                    <div className=\"max-w-6xl mx-auto px-4\">\n                      <div className=\"grid gap-3 md:gap-4\">\n                        {leagueUsers.map((champion, index) => {\n                          const actualRank = index + 1;\n                          const isCurrentUser = user && String(champion._id) === String(user._id);\n\n                          return (\n                            <motion.div\n                              key={champion._id}\n                              ref={isCurrentUser ? listUserRef : null}\n                              data-user-id={champion._id}\n                              data-user-rank={actualRank}\n                              initial={{ opacity: 0, y: 20 }}\n                              animate={{ opacity: 1, y: 0 }}\n                              transition={{ delay: 0.1 + index * 0.05, duration: 0.4 }}\n                              whileHover={{ scale: 1.01, y: -2 }}\n                              className={`ranking-card group relative ${\n                                shouldHighlightUser(champion._id)\n                                  ? 'ring-8 ring-yellow-400 ring-opacity-100'\n                                  : ''\n                              }`}\n                              style={{\n                                transform: shouldHighlightUser(champion._id) ? 'scale(1.05)' : 'scale(1)',\n                                filter: shouldHighlightUser(champion._id)\n                                  ? 'brightness(1.25) saturate(1.3) drop-shadow(0 0 25px rgba(255, 215, 0, 1))'\n                                  : 'none',\n                                transition: 'all 0.3s ease',\n                                border: shouldHighlightUser(champion._id) ? '4px solid #FFD700' : 'none',\n                                borderRadius: shouldHighlightUser(champion._id) ? '16px' : '0px',\n                                background: isCurrentUser ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15))' : 'transparent',\n                                position: 'relative',\n                                zIndex: isCurrentUser ? 10 : 1\n                              }}\n                            >\n                              {/* League User Card */}\n                              <div\n                                className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                                style={{\n                                  boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                                }}\n                              >\n                                <div\n                                  className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                                  style={{\n                                    border: `1px solid ${champion.tier.borderColor}30`\n                                  }}\n                                >\n                                  {/* Subtle Background Gradient */}\n                                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                                  {/* Left Section: Rank & Profile */}\n                                  <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                    {/* Rank Badge */}\n                                    <div className=\"relative\">\n                                      <div\n                                        className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                        style={{\n                                          color: '#FFFFFF',\n                                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                          border: '2px solid rgba(255,255,255,0.2)',\n                                          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                        }}\n                                      >\n                                        #{actualRank}\n                                      </div>\n                                    </div>\n\n                                    {/* Profile Picture with Online Status */}\n                                    <div className=\"relative\">\n                                      <ProfilePicture\n                                        user={champion}\n                                        size=\"sm\"\n                                        showOnlineStatus={false}\n                                        style={{\n                                          width: '32px',\n                                          height: '32px'\n                                        }}\n                                      />\n                                      {/* Only show online dot if user is actually online */}\n                                      {champion.isOnline && (\n                                        <div\n                                          style={{\n                                            position: 'absolute',\n                                            bottom: '-2px',\n                                            right: '-2px',\n                                            width: '10px',\n                                            height: '10px',\n                                            backgroundColor: '#22c55e',\n                                            borderRadius: '50%',\n                                            border: '2px solid #ffffff',\n                                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                                            zIndex: 10\n                                          }}\n                                          title=\"Online\"\n                                        />\n                                      )}\n                                      {/* Current User Indicator */}\n                                      {isCurrentUser && (\n                                        <div\n                                          className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                          style={{\n                                            background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                            boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                          }}\n                                        >\n                                          <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n\n                                  {/* Center Section: User Info */}\n                                  <div className=\"flex-1 min-w-0 px-2\">\n                                    <div className=\"space-y-1\">\n                                      {/* User Name */}\n                                      <div className=\"flex items-center gap-2 mb-1\">\n                                        <h3\n                                          className=\"text-base md:text-lg font-bold truncate\"\n                                          style={{\n                                            color: champion.tier.nameColor,\n                                            textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                            filter: 'drop-shadow(0 0 4px currentColor)'\n                                          }}\n                                        >\n                                          {champion.name}\n                                        </h3>\n                                        {isCurrentUser && (\n                                          <span\n                                            className=\"px-3 py-1 rounded-full text-sm font-black animate-pulse\"\n                                            style={{\n                                              background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',\n                                              color: '#1f2937',\n                                              boxShadow: '0 4px 12px rgba(255,215,0,0.8), 0 0 20px rgba(255,215,0,0.6)',\n                                              border: '2px solid #FFFFFF',\n                                              textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                              fontSize: '12px',\n                                              fontWeight: '900'\n                                            }}\n                                          >\n                                            🎯 YOU\n                                          </span>\n                                        )}\n                                      </div>\n\n                                      {/* Class Info */}\n                                      <div className=\"text-xs text-white/70 mt-0.5\">\n                                        {champion.level} • Class {champion.class}\n                                      </div>\n                                    </div>\n                                  </div>\n\n                                  {/* Right Section: Stats */}\n                                  <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                    {/* XP Display */}\n                                    <div\n                                      className=\"text-lg md:text-xl font-black mb-2\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 6px currentColor)'\n                                      }}\n                                    >\n                                      {champion.totalXP.toLocaleString()} XP\n                                    </div>\n\n                                    {/* Compact Stats */}\n                                    <div className=\"flex items-center gap-3 text-xs\">\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: `${champion.tier.borderColor}20`,\n                                          color: champion.tier.textColor\n                                        }}\n                                      >\n                                        <TbBrain className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                      </div>\n                                      <div\n                                        className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                        style={{\n                                          backgroundColor: '#FF6B3520',\n                                          color: '#FF6B35'\n                                        }}\n                                      >\n                                        <TbFlame className=\"w-3 h-3\" />\n                                        <span className=\"font-medium\">{champion.currentStreak}</span>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            </motion.div>\n                          );\n                        })}\n                      </div>\n                    </div>\n                  </motion.div>\n                )\n              ) : (\n                /* ALL LEAGUES GROUPED VIEW */\n                Object.keys(leagueGroups).length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 30 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 1, duration: 0.8 }}\n                    className=\"mt-16 main-ranking-section\"\n                    id=\"grouped-leagues-section\"\n                  >\n                    {/* All Leagues Header */}\n                    <div className=\"text-center mb-8 md:mb-12\">\n                      <motion.h2\n                        className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                        style={{\n                          background: 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          filter: 'drop-shadow(0 0 12px #8B5CF6)'\n                        }}\n                        animate={{ scale: [1, 1.01, 1] }}\n                        transition={{ duration: 4, repeat: Infinity }}\n                      >\n                        🏆 LEAGUE RANKINGS 🏆\n                      </motion.h2>\n                      <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                        Click on any league icon above to see its members\n                      </p>\n                    </div>\n\n                    {/* Grouped Leagues Display */}\n                    <div className=\"max-w-6xl mx-auto px-4 space-y-8\">\n                      {getOrderedLeagues().map((leagueKey) => {\n                        const league = leagueSystem[leagueKey];\n                        const leagueData = leagueGroups[leagueKey];\n                        const topUsers = leagueData.users.slice(0, 3); // Show top 3 from each league\n\n                        return (\n                          <motion.div\n                            key={leagueKey}\n                            ref={(el) => (leagueRefs.current[leagueKey] = el)}\n                            initial={{ opacity: 0, y: 20 }}\n                            animate={{ opacity: 1, y: 0 }}\n                            transition={{ delay: 0.2, duration: 0.6 }}\n                            className=\"bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10\"\n                            id={`league-${leagueKey}`}\n                            data-league={leagueKey}\n                          >\n                            {/* League Header */}\n                            <div className=\"flex items-center justify-between mb-6\">\n                              <div className=\"flex items-center gap-4\">\n                                <div\n                                  className=\"w-16 h-16 rounded-xl flex items-center justify-center text-3xl\"\n                                  style={{\n                                    background: `linear-gradient(135deg, ${league.borderColor}40, ${league.textColor}20)`,\n                                    border: `2px solid ${league.borderColor}60`,\n                                    boxShadow: `0 4px 20px ${league.shadowColor}40`\n                                  }}\n                                >\n                                  {league.leagueIcon}\n                                </div>\n                                <div>\n                                  <h3\n                                    className=\"text-2xl font-black mb-1\"\n                                    style={{\n                                      color: league.nameColor,\n                                      textShadow: `2px 2px 4px ${league.shadowColor}`,\n                                      filter: 'drop-shadow(0 0 8px currentColor)'\n                                    }}\n                                  >\n                                    {league.title} LEAGUE\n                                  </h3>\n                                  <p className=\"text-white/70 text-sm\">\n                                    {leagueData.users.length} champions • {league.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={() => handleLeagueSelect(leagueKey)}\n                                className=\"px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n                              >\n                                View All ({leagueData.users.length})\n                              </motion.button>\n                            </div>\n\n                            {/* Top 3 Users from League */}\n                            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                              {topUsers.map((champion, index) => {\n                                const isCurrentUser = user && champion._id === user._id;\n                                const leagueRank = index + 1;\n\n                                return (\n                                  <motion.div\n                                    key={champion._id}\n                                    data-user-id={champion._id}\n                                    data-user-rank={leagueRank}\n                                    initial={{ opacity: 0, scale: 0.9 }}\n                                    animate={{ opacity: 1, scale: 1 }}\n                                    transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}\n                                    whileHover={{ scale: 1.02, y: -2 }}\n                                    className={`relative ${\n                                      shouldHighlightUser(champion._id)\n                                        ? 'ring-2 ring-yellow-400/60'\n                                        : ''\n                                    }`}\n                                  >\n                                    <div\n                                      className={`bg-gradient-to-br ${champion.tier.color} p-0.5 rounded-xl ${champion.tier.glow} shadow-lg`}\n                                      style={{\n                                        boxShadow: `0 4px 15px ${champion.tier.shadowColor}30`\n                                      }}\n                                    >\n                                      <div\n                                        className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                                      >\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                                        {/* League Rank Badge */}\n                                        <div\n                                          className=\"absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center font-black text-xs\"\n                                          style={{\n                                            background: league.borderColor,\n                                            color: '#FFFFFF',\n                                            border: '2px solid #FFFFFF'\n                                          }}\n                                        >\n                                          #{leagueRank}\n                                        </div>\n\n                                        {/* Profile Picture */}\n                                        <div className={`relative mx-auto mb-3 ${\n                                          isCurrentUser\n                                            ? 'ring-1 ring-yellow-400 ring-opacity-80'\n                                            : ''\n                                        }`}>\n                                          <ProfilePicture\n                                            user={champion}\n                                            size=\"md\"\n                                            showOnlineStatus={true}\n                                            style={{\n                                              width: '40px',\n                                              height: '40px'\n                                            }}\n                                          />\n                                          {isCurrentUser && (\n                                            <div\n                                              className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                              style={{\n                                                background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                                boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                              }}\n                                            >\n                                              <TbStar className=\"w-2.5 h-2.5 text-gray-900\" />\n                                            </div>\n                                          )}\n                                        </div>\n\n                                        {/* Name and Stats */}\n                                        <h4\n                                          className=\"text-sm font-bold mb-2 truncate\"\n                                          style={{ color: champion.tier.nameColor }}\n                                        >\n                                          {champion.name}\n                                          {isCurrentUser && (\n                                            <span className=\"ml-1 text-xs text-yellow-400\">👑</span>\n                                          )}\n                                        </h4>\n\n                                        <div className=\"text-lg font-black mb-2\" style={{ color: champion.tier.textColor }}>\n                                          {champion.totalXP.toLocaleString()} XP\n                                        </div>\n\n                                        <div className=\"flex justify-center gap-3 text-xs\">\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🧠 {champion.totalQuizzesTaken}\n                                          </span>\n                                          <span style={{ color: champion.tier.textColor }}>\n                                            🔥 {champion.currentStreak}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  </motion.div>\n                                );\n                              })}\n                            </div>\n\n                            {/* Show More Indicator */}\n                            {leagueData.users.length > 3 && (\n                              <div className=\"text-center mt-4\">\n                                <p className=\"text-white/60 text-sm\">\n                                  +{leagueData.users.length - 3} more champions in this league\n                                </p>\n                              </div>\n                            )}\n                          </motion.div>\n                        );\n                      })}\n                    </div>\n                  </motion.div>\n                )\n              )}\n\n\n\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,OAAO,QACF,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,qBAAqB,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErF,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,SAAS,GAAGH,SAAS,CAACI,IAAI,IAAI,IAAI;;EAExC;EACA,MAAMC,gBAAgB,GAAG,CAAC,MAAM;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;EAEJ,MAAMK,SAAS,GAAG,CAAC,MAAM;IACvB,IAAI;MACF,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAII,KAAK,EAAE;QACT,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACI,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,OAAOF,OAAO;MAChB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;;EAEJ;EACA,MAAMT,IAAI,GAAGD,SAAS,IAAIE,gBAAgB,IAAIM,SAAS;;EAEvD;EACA,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACAmD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;IACnCC,KAAK,EAAEjB,SAAS;IAChBI,YAAY,EAAEF,gBAAgB;IAC9BO,KAAK,EAAED,SAAS;IAChBU,KAAK,EAAEjB;EACT,CAAC,CAAC;;EAEF;EACA,IAAIA,IAAI,IAAI,CAACY,YAAY,EAAE;IACzBE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,IAAI,CAACkB,MAAM,CAAC;EACvD;EACA,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACiF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAMqF,UAAU,GAAGnF,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMoF,SAAS,GAAGpF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqF,cAAc,GAAGrF,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsF,aAAa,GAAGtF,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuF,WAAW,GAAGvF,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMwF,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE3F,OAAO;MACb4F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEnF,SAAS;MACfoF,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEjF,QAAQ;MACdkF,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAElF,OAAO;MACbmF,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE5F,QAAQ;MACd6F,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAErF,OAAO;MACbsF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE1F,MAAM;MACZ2F,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEpF,QAAQ;MACdqF,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIvF,KAAK,IAAK;IACpC,MAAMwF,OAAO,GAAG,CAAC,CAAC;IAElBxF,KAAK,CAACyF,OAAO,CAACvF,IAAI,IAAI;MACpB,MAAMwF,UAAU,GAAGT,aAAa,CAAC/E,IAAI,CAACyF,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClB1F,KAAK,EAAE;QACT,CAAC;MACH;MACAwF,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAACnF,KAAK,CAAC4F,IAAI,CAAC;QACpC,GAAG1F,IAAI;QACP2F,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAAC/F,KAAK,CAACgG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMrD,WAAW,GAAG8D,QAAQ,CAACE,MAAM,CAACpG,IAAI,IAAI;MAC1C,MAAMiF,MAAM,GAAGF,aAAa,CAAC/E,IAAI,CAACyF,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClB1F,KAAK,EAAEsC,WAAW;MAClBiE,QAAQ,EAAEjE,WAAW,CAACkE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAErE,WAAW,CAACsE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IAAA,IAAAe,qBAAA;IACxC9F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8E,SAAS,CAAC;;IAE7C;IACApD,iBAAiB,CAACoD,SAAS,CAAC;IAC5BtD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,EAAAuE,qBAAA,GAAAlE,YAAY,CAACmD,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyB9G,KAAK,KAAI,EAAE,CAAC;;IAEpD;IACA+G,UAAU,CAAC,MAAM;MACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAE,iBAAgBnB,SAAU,IAAG,CAAC,IACvDkB,QAAQ,CAACE,cAAc,CAAE,UAASpB,SAAU,EAAC,CAAC,IAC9C7C,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC;MAElD,IAAIiB,aAAa,EAAE;QACjBA,aAAa,CAACK,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;;QAEF;QACAR,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,aAAa;QAC7CV,aAAa,CAACS,KAAK,CAACE,UAAU,GAAG,eAAe;QAChDX,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,kCAAkC;QAElEb,UAAU,CAAC,MAAM;UACfC,aAAa,CAACS,KAAK,CAACC,SAAS,GAAG,UAAU;UAC1CV,aAAa,CAACS,KAAK,CAACG,SAAS,GAAG,EAAE;QACpC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACxB,MAAM,CAACnB,MAAM,IAAIvC,YAAY,CAACuC,MAAM,CAAC,IAAIvC,YAAY,CAACuC,MAAM,CAAC,CAACnF,KAAK,CAAC4G,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAID;EACA,MAAMmB,gBAAgB,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACvD,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,CAAA/H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI,SAAS;MAC7C,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;MACrDA,SAAS,CAAC1C,OAAO,CAACyC,KAAK,IAAI;QACzB,IAAIA,KAAK,KAAKD,YAAY,EAAE;UAC1B5H,YAAY,CAAC+H,UAAU,CAAE,iBAAgBF,KAAM,EAAC,CAAC;UACjD7H,YAAY,CAAC+H,UAAU,CAAE,sBAAqBF,KAAM,EAAC,CAAC;QACxD;MACF,CAAC,CAAC;MACF;MACA,IAAI,CAACF,YAAY,EAAE;QACjB,MAAMK,SAAS,GAAG,CAAAnI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI,SAAS;QAC1C,MAAMI,aAAa,GAAGjI,YAAY,CAACC,OAAO,CAAE,iBAAgB+H,SAAU,EAAC,CAAC;QACxE,MAAME,SAAS,GAAGlI,YAAY,CAACC,OAAO,CAAE,sBAAqB+H,SAAU,EAAC,CAAC;QACzE,MAAMG,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACA,IAAIF,aAAa,IAAIC,SAAS,IAAKC,GAAG,GAAGE,QAAQ,CAACH,SAAS,CAAC,GAAI,MAAM,EAAE;UACtE,MAAMI,MAAM,GAAGpI,IAAI,CAACC,KAAK,CAAC8H,aAAa,CAAC;UACxC/G,cAAc,CAACoH,MAAM,CAACC,IAAI,IAAI,EAAE,CAAC;UACjCjH,kBAAkB,CAACgH,MAAM,CAACpC,QAAQ,CAAC;UACnC9E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEAA,UAAU,CAAC,IAAI,CAAC;MAChBT,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE+G,YAAY,GAAG,iBAAiB,GAAG,EAAE,CAAC;;MAE7F;MACA,IAAI;QACFhH,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAM4H,qBAAqB,GAAG,MAAM1J,gBAAgB,CAAC;UACnD2J,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAA7I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI,KAAK;UACjCc,eAAe,EAAE,KAAK;UACtB;UACA,IAAIhB,YAAY,IAAI;YAAEiB,EAAE,EAAER,IAAI,CAACD,GAAG,CAAC;UAAE,CAAC;QACxC,CAAC,CAAC;QAEFxH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4H,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACD,IAAI,EAAE;UACxF5H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMkI,YAAY,GAAGN,qBAAqB,CAACD,IAAI,CAACtC,MAAM,CAAClG,QAAQ,IAC5DA,QAAQ,CAACuF,OAAO,IAAIvF,QAAQ,CAACuF,OAAO,GAAG,CAAC,IACxCvF,QAAQ,CAACgJ,iBAAiB,IAAIhJ,QAAQ,CAACgJ,iBAAiB,GAAG,CAC9D,CAAC;;UAED;UACApI,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkI,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC7C,CAAC,KAAK;YAC/EC,GAAG,EAAED,CAAC,CAACC,GAAG;YACV6C,IAAI,EAAE9C,CAAC,CAAC8C,IAAI;YACZC,YAAY,EAAE/C,CAAC,CAAC+C,YAAY;YAC5BC,cAAc,EAAEhD,CAAC,CAACgD,cAAc;YAChCC,cAAc,EAAE,CAAC,EAAEjD,CAAC,CAAC+C,YAAY,IAAI/C,CAAC,CAACgD,cAAc;UACvD,CAAC,CAAC,CAAC,CAAC;UAEJ,MAAME,eAAe,GAAGR,YAAY,CAACG,GAAG,CAAC,CAAClJ,QAAQ,EAAEwJ,KAAK,MAAM;YAC7DlD,GAAG,EAAEtG,QAAQ,CAACsG,GAAG;YACjB6C,IAAI,EAAEnJ,QAAQ,CAACmJ,IAAI,IAAI,oBAAoB;YAC3CM,KAAK,EAAEzJ,QAAQ,CAACyJ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE1J,QAAQ,CAAC0J,KAAK,IAAI,EAAE;YAC3B5B,KAAK,EAAE9H,QAAQ,CAAC8H,KAAK,IAAI,EAAE;YAC3BuB,cAAc,EAAErJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI,EAAE;YACtED,YAAY,EAAEpJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI,EAAE;YACpE9D,OAAO,EAAEvF,QAAQ,CAACuF,OAAO,IAAI,CAAC;YAC9ByD,iBAAiB,EAAEhJ,QAAQ,CAACgJ,iBAAiB,IAAI,CAAC;YAClDW,YAAY,EAAE3J,QAAQ,CAAC2J,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAE5J,QAAQ,CAAC4J,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAE7J,QAAQ,CAAC6J,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAE9J,QAAQ,CAAC8J,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEP,KAAK,GAAG,CAAC;YACf/D,IAAI,EAAEZ,aAAa,CAAC7E,QAAQ,CAACuF,OAAO,IAAI,CAAC,CAAC;YAC1CyE,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEjK,QAAQ,CAACiK,YAAY,IAAI,CAAC;YACxC;YACApC,YAAY,EAAE7H,QAAQ,CAAC6H,YAAY,IAAI,CAAC;YACxCqC,aAAa,EAAElK,QAAQ,CAACkK,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnK,QAAQ,CAACmK,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpK,QAAQ,CAACoK,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErK,QAAQ,CAACqK,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;;UAEH;UACA1J,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0I,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC7C,CAAC,KAAK;YAC/EC,GAAG,EAAED,CAAC,CAACC,GAAG;YACV6C,IAAI,EAAE9C,CAAC,CAAC8C,IAAI;YACZC,YAAY,EAAE/C,CAAC,CAAC+C,YAAY;YAC5BC,cAAc,EAAEhD,CAAC,CAACgD,cAAc;YAChCC,cAAc,EAAE,CAAC,EAAEjD,CAAC,CAAC+C,YAAY,IAAI/C,CAAC,CAACgD,cAAc;UACvD,CAAC,CAAC,CAAC,CAAC;UAEJlI,cAAc,CAACoI,eAAe,CAAC;;UAE/B;UACA,MAAMgB,aAAa,GAAGhB,eAAe,CAACnD,SAAS,CAACoE,IAAI,IAAIA,IAAI,CAAClE,GAAG,MAAKxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAC;UAC/E/E,kBAAkB,CAACgJ,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAIzK,IAAI,EAAE;YACR,MAAM2K,cAAc,GAAG1E,wBAAwB,CAACwD,eAAe,EAAEzJ,IAAI,CAAC;YACtEmC,oBAAoB,CAACwI,cAAc,CAAC;YACpCtI,cAAc,CAAC,CAAAsI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7K,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAM8K,OAAO,GAAGvF,kBAAkB,CAACoE,eAAe,CAAC;UACnD9G,eAAe,CAACiI,OAAO,CAAC;;UAExB;UACA,MAAMzC,SAAS,GAAG,CAAAnI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI,SAAS;UAC1C,MAAM6C,SAAS,GAAG;YAChBnC,IAAI,EAAEe,eAAe;YACrBpD,QAAQ,EAAEoE,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG;UACrD,CAAC;UACDtK,YAAY,CAAC2K,OAAO,CAAE,iBAAgB3C,SAAU,EAAC,EAAE9H,IAAI,CAAC0K,SAAS,CAACF,SAAS,CAAC,CAAC;UAC7E1K,YAAY,CAAC2K,OAAO,CAAE,sBAAqB3C,SAAU,EAAC,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC0C,QAAQ,CAAC,CAAC,CAAC;UAE9EzJ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO0J,OAAO,EAAE;QAChBnK,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEkK,OAAO,CAAC;MACpE;;MAEA;MACAnK,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAImK,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFrK,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDmK,eAAe,GAAG,MAAMlM,uBAAuB,CAAC,CAAC;QACjD8B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCoK,aAAa,GAAG,MAAMhM,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAOiM,KAAK,EAAE;QACdtK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEqK,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAMhM,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAOkM,SAAS,EAAE;UAClBvK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsK,SAAS,CAAC;QACpD;MACF;MAEA,IAAI5B,eAAe,GAAG,EAAE;MAExB,IAAI0B,aAAa,IAAIA,aAAa,CAACnC,OAAO,IAAImC,aAAa,CAACzC,IAAI,EAAE;QAChE5H,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMuK,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAClC,OAAO,IAAIkC,eAAe,CAACxC,IAAI,EAAE;UACtEwC,eAAe,CAACxC,IAAI,CAACnD,OAAO,CAACmF,IAAI,IAAI;YAAA,IAAAa,UAAA;YACnC,MAAMrK,MAAM,GAAG,EAAAqK,UAAA,GAAAb,IAAI,CAAC1K,IAAI,cAAAuL,UAAA,uBAATA,UAAA,CAAW/E,GAAG,KAAIkE,IAAI,CAACxJ,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVoK,cAAc,CAACpK,MAAM,CAAC,GAAGwJ,IAAI,CAACc,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEA/B,eAAe,GAAG0B,aAAa,CAACzC,IAAI,CACjCtC,MAAM,CAAClG,QAAQ,IAAI;UAClB;UACA,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACsG,GAAG,EAAE,OAAO,KAAK;;UAE5C;UACA,IAAI,CAACtG,QAAQ,CAACuL,OAAO,IAAIzL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgI,KAAK,EAAE;YACpC,MAAMG,SAAS,GAAGnI,IAAI,CAACgI,KAAK,CAAC0D,WAAW,CAAC,CAAC;YAC1C,MAAMC,SAAS,GAAG,CAACzL,QAAQ,CAAC8H,KAAK,IAAI,SAAS,EAAE0D,WAAW,CAAC,CAAC;YAE7D,IAAIvD,SAAS,KAAK,SAAS,EAAE;cAC3B;cACA,OAAOwD,SAAS,KAAK,SAAS;YAChC,CAAC,MAAM,IAAIxD,SAAS,KAAK,WAAW,EAAE;cACpC;cACA,OAAOwD,SAAS,KAAK,WAAW;YAClC,CAAC,MAAM,IAAIxD,SAAS,KAAK,SAAS,EAAE;cAClC;cACA,OAAOwD,SAAS,KAAK,SAAS;YAChC;UACF;UAEA,OAAO,IAAI,CAAC,CAAC;QACf,CAAC,CAAC,CACDvC,GAAG,CAAC,CAAClJ,QAAQ,EAAEwJ,KAAK,KAAK;UACxB;UACA,MAAMkC,WAAW,GAAGN,cAAc,CAACpL,QAAQ,CAACsG,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIqF,YAAY,GAAGD,WAAW,CAAClF,MAAM,IAAIxG,QAAQ,CAACgJ,iBAAiB,IAAI,CAAC;UACxE,IAAI4C,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIrC,YAAY,GAAGgC,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAG3L,QAAQ,CAAC2J,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC+B,WAAW,CAAClF,MAAM,IAAIxG,QAAQ,CAACmM,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACtM,QAAQ,CAACmM,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC3I,GAAG,CAAC,EAAE,EAAE2I,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIrM,QAAQ,CAACmM,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BzC,YAAY,GAAGsC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACvC,YAAY,GAAGgC,YAAY,CAAC;YAEpD/K,OAAO,CAACC,GAAG,CAAE,0BAAyBb,QAAQ,CAACmJ,IAAK,KAAIiD,gBAAiB,aAAYG,gBAAiB,cAAavM,QAAQ,CAACmM,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI5G,OAAO,GAAGvF,QAAQ,CAACuF,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIvF,QAAQ,CAACmM,WAAW,EAAE;cACxB;cACA5G,OAAO,GAAG0G,IAAI,CAACK,KAAK,CAClBtM,QAAQ,CAACmM,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrBhC,YAAY,GAAG,EAAE,GAAGgC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7ChC,YAAY,GAAG,EAAE,GAAGgC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACApG,OAAO,GAAG0G,IAAI,CAACK,KAAK,CACjB3C,YAAY,GAAGgC,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrBhC,YAAY,GAAG,EAAE,GAAGgC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI/B,aAAa,GAAG5J,QAAQ,CAAC4J,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAG7J,QAAQ,CAAC6J,UAAU,IAAI,CAAC;UAEzC,IAAI6B,WAAW,CAAClF,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAIgG,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACrG,OAAO,CAAC0G,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZ3C,UAAU,GAAGoC,IAAI,CAACI,GAAG,CAACxC,UAAU,EAAE2C,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACF5C,aAAa,GAAG4C,UAAU;UAC5B,CAAC,MAAM,IAAIxM,QAAQ,CAACmM,WAAW,IAAI,CAACvC,aAAa,EAAE;YACjD;YACA,MAAM6C,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAG3L,QAAQ,CAACmM,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtB7C,aAAa,GAAGqC,IAAI,CAAC3I,GAAG,CAACqI,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxE5C,UAAU,GAAGoC,IAAI,CAACI,GAAG,CAACzC,aAAa,EAAEqC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLnG,GAAG,EAAEtG,QAAQ,CAACsG,GAAG;YACjB6C,IAAI,EAAEnJ,QAAQ,CAACmJ,IAAI,IAAI,oBAAoB;YAC3CM,KAAK,EAAEzJ,QAAQ,CAACyJ,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAE1J,QAAQ,CAAC0J,KAAK,IAAI,EAAE;YAC3B5B,KAAK,EAAE9H,QAAQ,CAAC8H,KAAK,IAAI,EAAE;YAC3BuB,cAAc,EAAErJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI,EAAE;YACtED,YAAY,EAAEpJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI,EAAE;YACpE9D,OAAO,EAAEA,OAAO;YAChByD,iBAAiB,EAAE2C,YAAY;YAC/BhC,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAE9J,QAAQ,CAAC8J,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEP,KAAK,GAAG,CAAC;YACf/D,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5ByE,UAAU,EAAE,IAAI;YAChB;YACA0C,cAAc,EAAE1M,QAAQ,CAACmM,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAAClF,MAAM,GAAG,CAAC;YAClC8D,UAAU,EAAEoB,WAAW,CAAClF,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGxG,QAAQ,CAACmM,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA5C,eAAe,CAAC3D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACAgE,eAAe,CAAClE,OAAO,CAAC,CAACvF,IAAI,EAAE0J,KAAK,KAAK;UACvC1J,IAAI,CAACiK,IAAI,GAAGP,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFrI,cAAc,CAACoI,eAAe,CAAC;;QAE/B;QACA,MAAMtB,SAAS,GAAG,CAAAnI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,KAAK,KAAI,SAAS;QAC1C,MAAM6C,SAAS,GAAG;UAChBnC,IAAI,EAAEe,eAAe;UACrBpD,QAAQ,EAAE,IAAI,CAAC;QACjB,CAAC;;QAED;QACA,IAAIA,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIrG,IAAI,EAAE;UACR;UACAqG,QAAQ,GAAGoD,eAAe,CAACnD,SAAS,CAACoE,IAAI,IAAIA,IAAI,CAAClE,GAAG,KAAKxG,IAAI,CAACwG,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGoD,eAAe,CAACnD,SAAS,CAACoE,IAAI,IAAIoC,MAAM,CAACpC,IAAI,CAAClE,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIrG,IAAI,CAACqJ,IAAI,EAAE;YAChChD,QAAQ,GAAGoD,eAAe,CAACnD,SAAS,CAACoE,IAAI,IAAIA,IAAI,CAACrB,IAAI,KAAKrJ,IAAI,CAACqJ,IAAI,CAAC;UACvE;QACF;QAEA5H,kBAAkB,CAAC4E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACAwE,SAAS,CAACxE,QAAQ,GAAGA,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;QACxDlG,YAAY,CAAC2K,OAAO,CAAE,iBAAgB3C,SAAU,EAAC,EAAE9H,IAAI,CAAC0K,SAAS,CAACF,SAAS,CAAC,CAAC;QAC7E1K,YAAY,CAAC2K,OAAO,CAAE,sBAAqB3C,SAAU,EAAC,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC0C,QAAQ,CAAC,CAAC,CAAC;;QAE9E;QACA,IAAIhL,IAAI,EAAE;UACR,MAAM2K,cAAc,GAAG1E,wBAAwB,CAACwD,eAAe,EAAEzJ,IAAI,CAAC;UACtEmC,oBAAoB,CAACwI,cAAc,CAAC;UACpCtI,cAAc,CAAC,CAAAsI,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE7K,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAM8K,OAAO,GAAGvF,kBAAkB,CAACoE,eAAe,CAAC;QACnD9G,eAAe,CAACiI,OAAO,CAAC;;QAExB;QACA,IAAImC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC1CnM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;YAC7CoF,WAAW,EAAEnG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqJ,IAAI;YACvBnI,MAAM,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;YACjB0G,UAAU,EAAE,QAAOlN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;YAC5BiF,OAAO,EAAE,CAAAzL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmN,IAAI,MAAK,OAAO,KAAInN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyL,OAAO;YAChD2B,MAAM,EAAEpN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,OAAO;YACrBgF,aAAa,EAAEpE,QAAQ;YACvBgH,gBAAgB,EAAEhH,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;YACrDiH,gBAAgB,EAAE7D,eAAe,CAAC/C,MAAM;YACxC6G,eAAe,EAAE9D,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC7C,CAAC,KAAK;cAAEiH,EAAE,EAAEjH,CAAC,CAACC,GAAG;cAAEiH,IAAI,EAAE,OAAOlH,CAAC,CAACC,GAAG;cAAE6C,IAAI,EAAE9C,CAAC,CAAC8C;YAAK,CAAC,CAAC,CAAC;YACxGqE,UAAU,EAAEjE,eAAe,CAACkE,IAAI,CAACjD,IAAI,IAAIA,IAAI,CAAClE,GAAG,MAAKxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAC;YAChEoH,WAAW,EAAEnE,eAAe,CAACkE,IAAI,CAACjD,IAAI,IAAIoC,MAAM,CAACpC,IAAI,CAAClE,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,CAAC,CAAC;YACjFqH,SAAS,EAAEpE,eAAe,CAACkE,IAAI,CAACjD,IAAI,IAAIA,IAAI,CAACrB,IAAI,MAAKrJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqJ,IAAI;UAClE,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMyE,WAAW,GAAG;UAClBtC,OAAO,EAAE/B,eAAe,CAACrD,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiE,UAAU,KAAK,SAAS,CAAC,CAAC9D,MAAM;UACvEqH,aAAa,EAAEtE,eAAe,CAACrD,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiE,UAAU,KAAK,eAAe,CAAC,CAAC9D,MAAM;UACnFsH,SAAS,EAAEvE,eAAe,CAACrD,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACiE,UAAU,KAAK,WAAW,CAAC,CAAC9D;QACvE,CAAC;QAED5F,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE0I,eAAe,CAAC/C,MAAM,EAAE,gBAAgB,CAAC;QACxF5F,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+M,WAAW,CAAC;QAC5ChN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0I,eAAe,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC7C,CAAC,KAAK;UACvE8C,IAAI,EAAE9C,CAAC,CAAC8C,IAAI;UACZrE,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACbwI,OAAO,EAAE1H,CAAC,CAAC2C,iBAAiB;UAC5BgF,GAAG,EAAE3H,CAAC,CAACsD,YAAY;UACnBsE,MAAM,EAAE5H,CAAC,CAACiE;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACL1J,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCM,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvD,OAAO,CAACkQ,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdtK,OAAO,CAACsK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDlN,OAAO,CAACkN,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR7J,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8M,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,EAACrO,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,MAAM,GAAE;MACjBJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEf,IAAI,CAAC;MAC3C;IACF;IAEA,IAAI;MACFc,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEf,IAAI,CAACkB,MAAM,CAAC;MAClE,MAAMoN,QAAQ,GAAG,MAAMnP,WAAW,CAAC,CAAC;MACpC2B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEuN,QAAQ,CAAC;MAEjD,IAAIA,QAAQ,CAACtF,OAAO,EAAE;QACpBlI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuN,QAAQ,CAAC5F,IAAI,CAAChC,MAAM,CAAC;QAC1D5F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEf,IAAI,CAACkB,MAAM,CAAC;QAClDJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuN,QAAQ,CAAC5F,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC7C,CAAC,KAAK;UAAEiH,EAAE,EAAEjH,CAAC,CAACC,GAAG;UAAE6C,IAAI,EAAE9C,CAAC,CAAC8C;QAAK,CAAC,CAAC,CAAC,CAAC;QAEtG,MAAMnJ,QAAQ,GAAGoO,QAAQ,CAAC5F,IAAI,CAACiF,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACkB,MAAM,CAAC,CAAC;QAC/E,IAAIhB,QAAQ,EAAE;UACZY,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEb,QAAQ,CAAC;UAChD;UACA,MAAMqO,mBAAmB,GAAG;YAC1B,GAAGrO,QAAQ;YACXqJ,cAAc,EAAErJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI,EAAE;YACtED,YAAY,EAAEpJ,QAAQ,CAACoJ,YAAY,IAAIpJ,QAAQ,CAACqJ,cAAc,IAAI;UACpE,CAAC;UACD1I,eAAe,CAAC0N,mBAAmB,CAAC;QACtC,CAAC,MAAM;UACLzN,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACA,MAAMyN,WAAW,GAAGF,QAAQ,CAAC5F,IAAI,CAACiF,IAAI,CAACpH,CAAC,IACtCA,CAAC,CAACC,GAAG,KAAKxG,IAAI,CAACkB,MAAM,IACrBqF,CAAC,CAACiH,EAAE,KAAKxN,IAAI,CAACkB,MAAM,IACpB4L,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,CAACiI,QAAQ,CAACzO,IAAI,CAACkB,MAAM,CAAC,IACnC4L,MAAM,CAAC9M,IAAI,CAACkB,MAAM,CAAC,CAACuN,QAAQ,CAAClI,CAAC,CAACC,GAAG,CACpC,CAAC;UAED,IAAIgI,WAAW,EAAE;YACf1N,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEyN,WAAW,CAAC;YACjE;YACA,MAAMD,mBAAmB,GAAG;cAC1B,GAAGC,WAAW;cACdjF,cAAc,EAAEiF,WAAW,CAAClF,YAAY,IAAIkF,WAAW,CAACjF,cAAc,IAAI,EAAE;cAC5ED,YAAY,EAAEkF,WAAW,CAAClF,YAAY,IAAIkF,WAAW,CAACjF,cAAc,IAAI;YAC1E,CAAC;YACD1I,eAAe,CAAC0N,mBAAmB,CAAC;UACtC,CAAC,MAAM;YACLzN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UACjD;QACF;MACF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuN,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdtK,OAAO,CAACsK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACAxN,SAAS,CAAC,MAAM;IACd,IAAI,CAACgD,YAAY,IAAIZ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,MAAM,IAAIE,WAAW,CAACsF,MAAM,GAAG,CAAC,EAAE;MAC3D5F,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,MAAM2N,aAAa,GAAGtN,WAAW,CAACuM,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACkB,MAAM,CAAC,CAAC;MAClF,IAAIwN,aAAa,EAAE;QACjB5N,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2N,aAAa,CAAC;QAC3D;QACA,MAAMH,mBAAmB,GAAG;UAC1B,GAAGG,aAAa;UAChBnF,cAAc,EAAEmF,aAAa,CAACpF,YAAY,IAAIoF,aAAa,CAACnF,cAAc,IAAI,EAAE;UAChFD,YAAY,EAAEoF,aAAa,CAACpF,YAAY,IAAIoF,aAAa,CAACnF,cAAc,IAAI;QAC9E,CAAC;QACD1I,eAAe,CAAC0N,mBAAmB,CAAC;MACtC,CAAC,MAAM;QACLzN,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACxD;IACF;EACF,CAAC,EAAE,CAACK,WAAW,EAAEpB,IAAI,EAAEY,YAAY,CAAC,CAAC;;EAErC;EACAhD,SAAS,CAAC,MAAM;IACdiK,gBAAgB,CAAC,CAAC;IAClBwG,iBAAiB,CAAC,CAAC,CAAC,CAAC;;IAErB;IACA,MAAMM,WAAW,GAAGtL,kBAAkB,CAAC8I,IAAI,CAACK,KAAK,CAACL,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAGvL,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FzE,oBAAoB,CAAC0M,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvC/M,iBAAiB,CAACgN,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9BlO,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D8G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMoH,mBAAmB,GAAIC,KAAK,IAAK;MACrCpO,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmO,KAAK,CAACC,MAAM,CAAC;;MAEnE;MACAhP,YAAY,CAAC+H,UAAU,CAAC,cAAc,CAAC;MACvC/H,YAAY,CAAC+H,UAAU,CAAC,qBAAqB,CAAC;MAC9C/H,YAAY,CAAC+H,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACA,MAAMkH,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,GAAG,CAAC,KAAK;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UACjC,IAAI;YAAA,IAAAC,aAAA;YACFzO,OAAO,CAACC,GAAG,CAAE,uCAAsCuO,CAAC,GAAG,CAAE,IAAGD,QAAS,GAAE,CAAC;YACxE,MAAMxH,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;YAE9B;YACA,IAAI,CAAA0H,aAAA,GAAAL,KAAK,CAACC,MAAM,cAAAI,aAAA,eAAZA,aAAA,CAAcC,UAAU,IAAIxP,IAAI,EAAE;cACpC,MAAMyP,WAAW,GAAGrO,WAAW,CAACuM,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;cAC7E,IAAIiJ,WAAW,IAAIA,WAAW,CAAChK,OAAO,IAAIyJ,KAAK,CAACC,MAAM,CAACK,UAAU,EAAE;gBACjE1O,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD;cACF;YACF;;YAEA;YACA,IAAIuO,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAI9I,UAAU,CAAC8I,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;YACdtK,OAAO,CAACsK,KAAK,CAAE,6BAA4BkE,CAAC,GAAG,CAAE,UAAS,EAAElE,KAAK,CAAC;YAClE,IAAIkE,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAAE;cACpB,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAI9I,UAAU,CAAC8I,OAAO,EAAE,IAAI,CAAC,CAAC;YACzD;UACF;QACF;MACF,CAAC;;MAED;MACA9I,UAAU,CAAC,MAAM;QACfuI,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDQ,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEb,iBAAiB,CAAC;IACnDY,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEZ,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXa,aAAa,CAACjB,cAAc,CAAC;MAC7B;MACAe,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEf,iBAAiB,CAAC;MACtDY,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEd,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArR,SAAS,CAAC,MAAM;IACd,IAAIoC,IAAI,IAAI0C,YAAY,IAAIyC,MAAM,CAACS,IAAI,CAAClD,YAAY,CAAC,CAACgE,MAAM,GAAG,CAAC,IAAI,CAAClE,cAAc,EAAE;MACnF;MACA,KAAK,MAAM,CAACqD,SAAS,EAAEmK,UAAU,CAAC,IAAI7K,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;QAClE,MAAMuN,YAAY,GAAGD,UAAU,CAAClQ,KAAK,CAAC6N,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;QACnF,IAAIyJ,YAAY,EAAE;UAChBnP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8E,SAAS,CAAC;UACxDpD,iBAAiB,CAACoD,SAAS,CAAC;UAC5BtD,iBAAiB,CAAC,IAAI,CAAC;UACvBF,cAAc,CAAC2N,UAAU,CAAClQ,KAAK,CAAC;UAChC;QACF;MACF;IACF;EACF,CAAC,EAAE,CAACE,IAAI,EAAE0C,YAAY,EAAEF,cAAc,CAAC,CAAC;;EAExC;EACA,MAAM0N,aAAa,GAAG9O,WAAW,CAAC+H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAMgH,eAAe,GAAG/O,WAAW,CAAC+H,KAAK,CAAC,CAAC,CAAC;;EAI5C;EACA,MAAMiH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACpQ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG,GAAE,OAAO,IAAI;;IAE3B;IACA,MAAM6J,UAAU,GAAGH,aAAa,CAACI,IAAI,CAACC,SAAS,IAAIzD,MAAM,CAACyD,SAAS,CAAC/J,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;IAC9F,IAAI6J,UAAU,EAAE;MACd,MAAMG,cAAc,GAAGN,aAAa,CAAC5J,SAAS,CAACiK,SAAS,IAAIzD,MAAM,CAACyD,SAAS,CAAC/J,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3G,OAAO;QACLiH,IAAI,EAAE,QAAQ;QACdgD,QAAQ,EAAED,cAAc;QACxBvL,MAAM,EAAE,iBAAiB;QACzBY,SAAS,EAAE;MACb,CAAC;IACH;;IAEA;IACA,KAAK,MAAM,CAACA,SAAS,EAAEmK,UAAU,CAAC,IAAI7K,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;MAAA,IAAAgO,iBAAA;MAClE,MAAMT,YAAY,IAAAS,iBAAA,GAAGV,UAAU,CAAClQ,KAAK,cAAA4Q,iBAAA,uBAAhBA,iBAAA,CAAkB/C,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;MACpF,IAAIyJ,YAAY,EAAE;QAChB,MAAMQ,QAAQ,GAAGT,UAAU,CAAClQ,KAAK,CAACwG,SAAS,CAACC,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC,GAAG,CAAC;QACxF,OAAO;UACLiH,IAAI,EAAE,QAAQ;UACdgD,QAAQ,EAAEA,QAAQ;UAClBxL,MAAM,EAAE+K,UAAU,CAAChM,KAAK;UACxB6B,SAAS,EAAEA,SAAS;UACpB8K,UAAU,EAAEX,UAAU,CAAClQ,KAAK,CAAC4G;QAC/B,CAAC;MACH;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMkK,cAAc,GAAGR,iBAAiB,CAAC,CAAC;;EAE1C;EACA,MAAMS,aAAa,GAAI3P,MAAM,IAAK;IAChC,OAAOlB,IAAI,IAAI8M,MAAM,CAAC5L,MAAM,CAAC,KAAK4L,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC;EACpD,CAAC;;EAED;EACA,MAAMsK,mBAAmB,GAAI5P,MAAM,IAAK;IACtC,OAAO2P,aAAa,CAAC3P,MAAM,CAAC,IAAI,CAAC0B,gBAAgB;EACnD,CAAC;;EAED;EACA,MAAMmO,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACnO,gBAAgB,EAAE;MACrBC,mBAAmB,CAAC,IAAI,CAAC;MACzB/B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACdiF,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;EACjC,CAAC,EAAE,CAAC/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAEhE,cAAc,CAAC,CAAC;;EAE/B;EACA5E,SAAS,CAAC,MAAM;IACdkD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnCG,MAAM,EAAElB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG;MACjB1D,mBAAmB;MACnBkO,iBAAiB,EAAE5P,WAAW,CAACsF;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,EAAC1G,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG,KAAI1D,mBAAmB,IAAI1B,WAAW,CAACsF,MAAM,KAAK,CAAC,EAAE;MACjE5F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCkQ,OAAO,EAAE,CAAC,EAACjR,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwG,GAAG;QACpB0K,SAAS,EAAEpO,mBAAmB;QAC9BqO,OAAO,EAAE/P,WAAW,CAACsF,MAAM,GAAG;MAChC,CAAC,CAAC;MACF;IACF;IAEA,MAAM0K,YAAY,GAAGA,CAAA,KAAM;MACzBtQ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEf,IAAI,CAACwG,GAAG,CAAC;;MAE1D;MACA,MAAMkI,aAAa,GAAGtN,WAAW,CAACuM,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,CAAC;MAC/E,IAAI,CAACkI,aAAa,EAAE;QAClB5N,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CgC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9B;MACF;MAEAjC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE2N,aAAa,CAACzE,IAAI,CAAC;;MAEvE;MACA,MAAMoG,UAAU,GAAG3B,aAAa,CAACzE,IAAI,IAAI,CAAC;MAC1CnJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsP,UAAU,CAAC;MAEhD,IAAIA,UAAU,EAAE;QACd;QACAvP,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,MAAMsQ,aAAa,GAAGtK,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC;QACvElG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAACsQ,aAAa,CAAC;QACxD,IAAIA,aAAa,EAAE;UACjBxK,UAAU,CAAC,MAAM;YACfwK,aAAa,CAAClK,cAAc,CAAC;cAC3BC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;YACFxG,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;YACnC;YACA8F,UAAU,CAAC,MAAM;cACfhE,mBAAmB,CAAC,IAAI,CAAC;cACzBE,sBAAsB,CAAC,IAAI,CAAC;cAC5BjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLgC,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,MAAM;QACL;QACAjC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEf,IAAI,CAACwG,GAAG,CAAC;QAC7D,MAAM8K,WAAW,GAAGvK,QAAQ,CAACC,aAAa,CAAE,kBAAiBhH,IAAI,CAACwG,GAAI,IAAG,CAAC;QAC1E1F,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAACuQ,WAAW,CAAC;QACpD,IAAIA,WAAW,EAAE;UACfzK,UAAU,CAAC,MAAM;YACfyK,WAAW,CAACnK,cAAc,CAAC;cACzBC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACV,CAAC,CAAC;YACFxG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1C;YACA8F,UAAU,CAAC,MAAM;cACfhE,mBAAmB,CAAC,IAAI,CAAC;cACzBE,sBAAsB,CAAC,IAAI,CAAC;cAC5BjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CgC,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC;;IAED;IACA,MAAMwO,KAAK,GAAG1K,UAAU,CAACuK,YAAY,EAAE,IAAI,CAAC;IAC5C,OAAO,MAAMI,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACvR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,GAAG,EAAEpF,WAAW,EAAE0B,mBAAmB,CAAC,CAAC;;EAEjD;EACA,MAAM2O,oBAAoB,GAAGA,CAACzH,kBAAkB,EAAE0H,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMvJ,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMuJ,OAAO,GAAGJ,mBAAmB,GAAG,IAAInJ,IAAI,CAACmJ,mBAAmB,CAAC,GAAG,IAAI;IAE1E5Q,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCiJ,kBAAkB;MAClB0H,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfE,OAAO;MACPxJ,GAAG;MACHyJ,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGxJ,GAAG;MAClCuJ;IACF,CAAC,CAAC;;IAEF;IACA,IAAI7H,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAAC8H,OAAO,IAAIA,OAAO,GAAGxJ,GAAG,EAAE;QAC7B;QACA,OAAO;UACL0J,IAAI,EAAE,WAAW;UACjBvO,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL8N,IAAI,EAAE,SAAS;UACfvO,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL8N,IAAI,EAAE,SAAS;QACfvO,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAM+N,eAAe,GAAGA,CAAA,kBACtB1S,OAAA;IAAK2S,SAAS,EAAC,2EAA2E;IAAAC,QAAA,eACxF5S,OAAA;MAAK2S,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C5S,OAAA;QAAK2S,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5S,OAAA;UAAK2S,SAAS,EAAC;QAA6D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFhT,OAAA;UAAK2S,SAAS,EAAC;QAAmD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eAGNhT,OAAA;QAAK2S,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAC3D,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC/I,GAAG,CAAEqH,QAAQ,iBACtBlR,OAAA;UAAoB2S,SAAS,EAAG,eAAczB,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAGA,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU,EAAE;UAAA0B,QAAA,gBAClH5S,OAAA;YAAK2S,SAAS,EAAG;UAAyG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjIhT,OAAA;YAAK2S,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EhT,OAAA;YAAK2S,SAAS,EAAC;UAAmD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAHjE9B,QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhT,OAAA;QAAK2S,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EACzC,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACpJ,GAAG,CAAC,CAACqJ,CAAC,EAAEnD,CAAC,kBACtB/P,OAAA;UAAa2S,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eAC9D5S,OAAA;YAAK2S,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5S,OAAA;cAAK2S,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DhT,OAAA;cAAK2S,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB5S,OAAA;gBAAK2S,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDhT,OAAA;gBAAK2S,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNhT,OAAA;cAAK2S,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC,GAREjD,CAAC;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAIjR,OAAO,IAAIF,WAAW,CAACsF,MAAM,KAAK,CAAC,EAAE;IACvC,oBAAOnH,OAAA,CAAC0S,eAAe;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5B;EAEA,oBACEhT,OAAA,CAAAE,SAAA;IAAA0S,QAAA,gBACE5S,OAAA;MAAA4S,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACVhT,OAAA;MAAK2S,SAAS,EAAC,kIAAkI;MAACQ,OAAO,EAAE3B,eAAgB;MAAAoB,QAAA,GAG1K,CAACvP,gBAAgB,IAAI5C,IAAI,iBACxBT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAC7BZ,SAAS,EAAC,sJAAsJ;QAAAC,QAAA,EACjK;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,eAGDhT,OAAA;QAAK2S,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C5S,OAAA;UAAK2S,SAAS,EAAC;QAA2H;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjJhT,OAAA;UAAK2S,SAAS,EAAC;QAAgJ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtKhT,OAAA;UAAK2S,SAAS,EAAC;QAA6I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnKhT,OAAA;UAAK2S,SAAS,EAAC;QAA8I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGNhT,OAAA;QAAK2S,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACpJ,GAAG,CAAC,CAACqJ,CAAC,EAAEnD,CAAC,kBACvB/P,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UAETT,SAAS,EAAC,mDAAmD;UAC7Da,OAAO,EAAE;YACPD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfG,CAAC,EAAE,CAAC,CAAC,EAAE9G,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnCiE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFpL,UAAU,EAAE;YACVyL,QAAQ,EAAE,CAAC,GAAG/G,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/BuE,MAAM,EAAEC,QAAQ;YAChBC,KAAK,EAAElH,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACFrH,KAAK,EAAE;YACL+L,IAAI,EAAG,GAAEnH,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/B2E,GAAG,EAAG,GAAEpH,IAAI,CAACyC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGU,CAAC;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhT,OAAA;QAAK2S,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5B5S,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BrL,UAAU,EAAE;YAAEyL,QAAQ,EAAE;UAAI,CAAE;UAC9BhB,SAAS,EAAC,2DAA2D;UACrE3K,KAAK,EAAE;YACLiM,OAAO,EAAE5D,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG7D,MAAM,CAAC6D,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG;UACnF,CAAE;UAAAtB,QAAA,eAEF5S,OAAA;YAAK2S,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC5S,OAAA;cAAK2S,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnI5S,OAAA;gBAAK2S,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,GAKpGvB,cAAc,iBACbrR,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,mJAAmJ;kBAC7J3K,KAAK,EAAE;oBACLmM,UAAU,EAAE9C,cAAc,CAACnD,IAAI,KAAK,QAAQ,GACxC,2CAA2C,GAC3C,2CAA2C;oBAC/ChK,KAAK,EAAEmN,cAAc,CAACnD,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAC/D/F,SAAS,EAAE,oCAAoC;oBAC/CiM,QAAQ,EAAE/D,MAAM,CAAC6D,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG;kBACjD,CAAE;kBAAAtB,QAAA,gBAEF5S,OAAA,CAACpB,QAAQ;oBAAC+T,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9ChT,OAAA;oBAAA4S,QAAA,EACGvB,cAAc,CAACnD,IAAI,KAAK,QAAQ,GAC5B,cAAamD,cAAc,CAACH,QAAS,EAAC,GACtC,GAAEG,cAAc,CAAC3L,MAAO,KAAI2L,cAAc,CAACH,QAAS;kBAAC;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACb,EAGA3R,YAAY,iBACXrB,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,eAEhK5S,OAAA;oBAAK2S,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBAEtC5S,OAAA;sBAAK2S,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrC5S,OAAA,CAACH,cAAc;wBACbY,IAAI,EAAEY,YAAa;wBACnBgT,IAAI,EAAC,IAAI;wBACTC,gBAAgB,EAAE,KAAM;wBACxBtM,KAAK,EAAE;0BACLuM,MAAM,EAAE,mBAAmB;0BAC3BpM,SAAS,EAAE;wBACb;sBAAE;wBAAA0K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAED3R,YAAY,CAACmT,QAAQ,iBACpBxU,OAAA;wBACEgI,KAAK,EAAE;0BACLkJ,QAAQ,EAAE,UAAU;0BACpBuD,MAAM,EAAE,KAAK;0BACbC,KAAK,EAAE,KAAK;0BACZC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdC,eAAe,EAAE,SAAS;0BAC1BC,YAAY,EAAE,KAAK;0BACnBP,MAAM,EAAE,mBAAmB;0BAC3BpM,SAAS,EAAE,kCAAkC;0BAC7C4M,MAAM,EAAE;wBACV,CAAE;wBACFtQ,KAAK,EAAC;sBAAQ;wBAAAoO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNhT,OAAA;sBAAK2S,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB5S,OAAA;wBAAI2S,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,EACvDvR,YAAY,CAACyI,IAAI,IAAIzI,YAAY,CAAC2T,QAAQ,IAAI;sBAAM;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC,eAGLhT,OAAA;wBAAK2S,SAAS,EAAC,gCAAgC;wBAAAC,QAAA,gBAC7C5S,OAAA;0BAAK2S,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzD5S,OAAA;4BAAK2S,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAMnN,EAAE,GAAGpE,YAAY,CAAC6E,OAAO,IAAI7E,YAAY,CAACoE,EAAE,IAAIpE,YAAY,CAAC4T,MAAM,IAAI5T,YAAY,CAACyL,WAAW,IAAI,CAAC;8BAC1G,OAAOrH,EAAE,CAACyP,cAAc,CAAC,CAAC;4BAC5B,CAAC,EAAE;0BAAC;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D5S,OAAA;4BAAK2S,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,MAAMzD,aAAa,GAAGtN,WAAW,CAACuM,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAClM,YAAY,CAAC4F,GAAG,CAAC,CAAC;8BACvF,OAAOkI,aAAa,GAAI,IAAGA,aAAa,CAACzE,IAAK,EAAC,GAAIzI,eAAe,GAAI,IAAGA,eAAgB,EAAC,GAAG,KAAM;4BACrG,CAAC,EAAE;0BAAC;4BAAA4Q,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,gBACxD5S,OAAA;4BAAK2S,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnDhT,OAAA;4BAAK2S,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAC1C,CAAC,MAAM;8BACN;8BACA,MAAM/E,MAAM,GAAGxM,YAAY,CAAC6E,OAAO,IAAI7E,YAAY,CAACoE,EAAE,IAAIpE,YAAY,CAAC4T,MAAM,IAAI5T,YAAY,CAACyL,WAAW,IAAI,CAAC;8BAC9G,KAAK,MAAM,CAACxG,SAAS,EAAEmK,UAAU,CAAC,IAAI7K,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;gCAAA,IAAAgS,kBAAA;gCAClE,MAAMzE,YAAY,IAAAyE,kBAAA,GAAG1E,UAAU,CAAClQ,KAAK,cAAA4U,kBAAA,uBAAhBA,kBAAA,CAAkB/G,IAAI,CAACpH,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAClM,YAAY,CAAC4F,GAAG,CAAC,CAAC;gCAC5F,IAAIyJ,YAAY,EAAE;kCAChB,MAAM0E,UAAU,GAAG5P,aAAa,CAACqI,MAAM,CAAC;kCACxC,OAAQ,GAAEuH,UAAU,CAACvQ,UAAW,IAAGyB,SAAS,CAAC+O,WAAW,CAAC,CAAE,EAAC;gCAC9D;8BACF;8BACA;8BACA,IAAIxH,MAAM,GAAG,CAAC,EAAE;gCACd,MAAMuH,UAAU,GAAG5P,aAAa,CAACqI,MAAM,CAAC;gCACxC,OAAQ,GAAEuH,UAAU,CAACvQ,UAAW,IAAGuQ,UAAU,CAAC1P,MAAM,CAAC2P,WAAW,CAAC,CAAE,EAAC;8BACtE;8BACA,OAAO,aAAa;4BACtB,CAAC,EAAE;0BAAC;4BAAAxC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D5S,OAAA;4BAAK2S,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAO;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClC,CAAC,MAAM;8BACN;8BACA,OAAOvR,YAAY,CAACiU,gBAAgB,IAAIjU,YAAY,CAACsI,iBAAiB,IAAItI,YAAY,CAACkU,YAAY,IAAIlU,YAAY,CAACiL,YAAY,IAAI,CAAC;4BACvI,CAAC,EAAE;0BAAC;4BAAAuG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClD5S,OAAA;0BAAK2S,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,gBAC5D5S,OAAA;4BAAK2S,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClCvR,YAAY,CAACmH,YAAY,IAAInH,YAAY,CAACoH,KAAK,IAAI;0BAAC;4BAAAoK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,gBACzD5S,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAClCvR,YAAY,CAACkJ,aAAa,IAAIlJ,YAAY,CAACmU,MAAM,IAAI;0BAAC;4BAAA3C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,6CAA6C;0BAAAC,QAAA,gBAC1D5S,OAAA;4BAAK2S,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAS;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtDhT,OAAA;4BAAK2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,GAClC,CAAC,MAAM;8BACN,MAAM6C,QAAQ,GAAGpU,YAAY,CAACiJ,YAAY,IAAIjJ,YAAY,CAACoU,QAAQ,IAAI,CAAC;8BACxE,OAAO7I,IAAI,CAACC,KAAK,CAAC4I,QAAQ,CAAC;4BAC7B,CAAC,EAAE,CAAC,EAAC,GACP;0BAAA;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAGL,CAAC,MAAM;wBACN;wBACA,KAAK,MAAM,CAAC1M,SAAS,EAAEmK,UAAU,CAAC,IAAI7K,MAAM,CAACC,OAAO,CAAC1C,YAAY,CAAC,EAAE;0BAAA,IAAAuS,kBAAA;0BAClE,MAAMpD,SAAS,IAAAoD,kBAAA,GAAGjF,UAAU,CAAClQ,KAAK,cAAAmV,kBAAA,uBAAhBA,kBAAA,CAAkB3O,SAAS,CAACC,CAAC,IAAIuG,MAAM,CAACvG,CAAC,CAACC,GAAG,CAAC,KAAKsG,MAAM,CAAClM,YAAY,CAAC4F,GAAG,CAAC,CAAC;0BAC9F,IAAIqL,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAKqD,SAAS,EAAE;4BAC/C,oBACE3V,OAAA;8BAAK2S,SAAS,EAAC,kBAAkB;8BAAAC,QAAA,eAC/B5S,OAAA;gCAAK2S,SAAS,EAAC,uEAAuE;gCAAAC,QAAA,gBACpF5S,OAAA;kCAAK2S,SAAS,EAAC,yBAAyB;kCAAAC,QAAA,EAAC;gCAAe;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eAC9DhT,OAAA;kCAAK2S,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,GAAC,GAC3C,EAACN,SAAS,GAAG,CAAC,EAAC,MAAI,EAAC7B,UAAU,CAAClQ,KAAK,CAAC4G,MAAM;gCAAA;kCAAA0L,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACzC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAEV;wBACF;wBACA,OAAO,IAAI;sBACb,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACb,eAaDhT,OAAA;kBAAK2S,SAAS,EAAC,uHAAuH;kBAAAC,QAAA,gBAEpI5S,OAAA,CAACzB,MAAM,CAACqX,EAAE;oBACRjD,SAAS,EAAC,sCAAsC;oBAChD3K,KAAK,EAAE;sBACLmM,UAAU,EAAE,mDAAmD;sBAC/D0B,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE,aAAa;sBAClCC,UAAU,EAAE,6BAA6B;sBACzClP,MAAM,EAAE;oBACV,CAAE;oBACF2M,OAAO,EAAE;sBAAEwC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;oBAAE,CAAE;oBACjC9N,UAAU,EAAE;sBAAEyL,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAAAjB,QAAA,EAC/C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAGZhT,OAAA;oBAAK2S,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,EACvExK,iBAAiB,CAAC,CAAC,CAACyB,GAAG,CAAEvD,SAAS,IAAK;sBAAA,IAAA2P,sBAAA;sBACtC,MAAMvQ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;sBACtC,MAAM4P,UAAU,GAAGjT,cAAc,KAAKqD,SAAS;sBAC/C,MAAM6P,SAAS,GAAG,EAAAF,sBAAA,GAAA9S,YAAY,CAACmD,SAAS,CAAC,cAAA2P,sBAAA,uBAAvBA,sBAAA,CAAyB1V,KAAK,CAAC4G,MAAM,KAAI,CAAC;sBAE5D,oBACEnH,OAAA,CAACzB,MAAM,CAAC6U,GAAG;wBAETT,SAAS,EAAC,kCAAkC;wBAC5CyD,UAAU,EAAE;0BAAEJ,KAAK,EAAE;wBAAK,CAAE;wBAAApD,QAAA,gBAE5B5S,OAAA,CAACzB,MAAM,CAAC8X,MAAM;0BACZD,UAAU,EAAE;4BAAEJ,KAAK,EAAE,GAAG;4BAAEzC,CAAC,EAAE,CAAC;0BAAE,CAAE;0BAClC+C,QAAQ,EAAE;4BAAEN,KAAK,EAAE;0BAAK,CAAE;0BAC1B7C,OAAO,EAAEA,CAAA,KAAM/L,kBAAkB,CAACd,SAAS,CAAE;0BAC7CqM,SAAS,EAAG,+GACVuD,UAAU,GACN,oDAAoD,GACpD,kCACL,EAAE;0BACHlO,KAAK,EAAE;4BACLmM,UAAU,EAAE+B,UAAU,GACjB,2BAA0BxQ,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,OAAMsB,MAAM,CAACf,WAAY,KAAI,GACjG,2BAA0Be,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BAC7EmQ,MAAM,EAAG,aAAY2B,UAAU,GAAG,SAAS,GAAGxQ,MAAM,CAACf,WAAW,GAAG,IAAK,EAAC;4BACzEwD,SAAS,EAAE+N,UAAU,GAChB,YAAWxQ,MAAM,CAACpB,WAAY,sCAAqCoB,MAAM,CAACpB,WAAY,IAAG,GACzF,cAAaoB,MAAM,CAACpB,WAAY,IAAG;4BACxC2D,SAAS,EAAEiO,UAAU,GAAG,YAAY,GAAG,UAAU;4BACjDrP,MAAM,EAAEqP,UAAU,GAAG,+BAA+B,GAAG;0BACzD,CAAE;0BACF1C,OAAO,EAAE0C,UAAU,GAAG;4BACpB/N,SAAS,EAAE,CACR,YAAWzC,MAAM,CAACpB,WAAY,wBAAuB,EACrD,YAAWoB,MAAM,CAACpB,WAAY,yBAAwB,EACtD,YAAWoB,MAAM,CAACpB,WAAY,wBAAuB,CACvD;4BACD0R,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;0BACxB,CAAC,GAAG,CAAC,CAAE;0BACP9N,UAAU,EAAE;4BACVyL,QAAQ,EAAE,CAAC;4BACXC,MAAM,EAAEsC,UAAU,GAAGrC,QAAQ,GAAG,CAAC;4BACjC0C,IAAI,EAAE;0BACR,CAAE;0BACF9R,KAAK,EAAG,iBAAgBiB,MAAM,CAACjB,KAAM,YAAW0R,SAAU,SAAS;0BAAAvD,QAAA,gBAEnE5S,OAAA;4BAAM2S,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAElN,MAAM,CAACb;0BAAU;4BAAAgO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAChEkD,UAAU,iBACTlW,OAAA,CAACzB,MAAM,CAAC6U,GAAG;4BACTC,OAAO,EAAE;8BAAE2C,KAAK,EAAE,CAAC;8BAAEQ,MAAM,EAAE,CAAC,GAAG;8BAAElD,OAAO,EAAE;4BAAE,CAAE;4BAChDE,OAAO,EAAE;8BACPwC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;8BAClBQ,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;8BACrBlD,OAAO,EAAE,CAAC;8BACVnL,SAAS,EAAE,CACT,gEAAgE,EAChE,8DAA8D,EAC9D,gEAAgE;4BAEpE,CAAE;4BACFD,UAAU,EAAE;8BACV8N,KAAK,EAAE;gCAAErC,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAE0C,IAAI,EAAE;8BAAY,CAAC;8BAC3DC,MAAM,EAAE;gCAAE7C,QAAQ,EAAE,CAAC;gCAAEC,MAAM,EAAEC,QAAQ;gCAAE0C,IAAI,EAAE;8BAAS,CAAC;8BACzDpO,SAAS,EAAE;gCAAEwL,QAAQ,EAAE,GAAG;gCAAEC,MAAM,EAAEC,QAAQ;gCAAE0C,IAAI,EAAE;8BAAY,CAAC;8BACjEjD,OAAO,EAAE;gCAAEK,QAAQ,EAAE;8BAAI;4BAC3B,CAAE;4BACFhB,SAAS,EAAC,8KAA8K;4BACxL3K,KAAK,EAAE;8BACLmM,UAAU,EAAE,mDAAmD;8BAC/DI,MAAM,EAAE,iBAAiB;8BACzBQ,MAAM,EAAE;4BACV,CAAE;4BAAAnC,QAAA,eAEF5S,OAAA,CAACzB,MAAM,CAACkY,IAAI;8BACV9D,SAAS,EAAC,kCAAkC;8BAC5Ca,OAAO,EAAE;gCACPwC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gCAClBQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;8BACxB,CAAE;8BACFtO,UAAU,EAAE;gCACVyL,QAAQ,EAAE,CAAC;gCACXC,MAAM,EAAEC,QAAQ;gCAChB0C,IAAI,EAAE;8BACR,CAAE;8BAAA3D,QAAA,EACH;4BAED;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAa;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACb,eACDhT,OAAA;4BACE2S,SAAS,EAAC,2HAA2H;4BACrI3K,KAAK,EAAE;8BACLmM,UAAU,EAAEzO,MAAM,CAACf,WAAW;8BAC9BT,KAAK,EAAE,SAAS;8BAChBkQ,QAAQ,EAAE;4BACZ,CAAE;4BAAAxB,QAAA,EAEDuD;0BAAS;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACO,CAAC,eAGhBhT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;0BACTT,SAAS,EAAC,aAAa;0BACvByD,UAAU,EAAE;4BAAEJ,KAAK,EAAE;0BAAK,CAAE;0BAAApD,QAAA,eAE5B5S,OAAA;4BACE2S,SAAS,EAAC,mDAAmD;4BAC7D3K,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvB0R,UAAU,EAAG,eAAcrQ,MAAM,CAACpB,WAAY,EAAC;8BAC/C6P,UAAU,EAAG,GAAEzO,MAAM,CAACf,WAAY,IAAG;8BACrC4P,MAAM,EAAG,aAAY7O,MAAM,CAACf,WAAY;4BAC1C,CAAE;4BAAAiO,QAAA,EAEDlN,MAAM,CAACjB;0BAAK;4BAAAoO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC;sBAAA,GA9GR1M,SAAS;wBAAAuM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+GJ,CAAC;oBAEjB,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENhT,OAAA;oBAAG2S,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAEtD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAKNhT,OAAA,CAACzB,MAAM,CAAC8X,MAAM;kBACZD,UAAU,EAAE;oBAAEJ,KAAK,EAAE,IAAI;oBAAEQ,MAAM,EAAE;kBAAI,CAAE;kBACzCF,QAAQ,EAAE;oBAAEN,KAAK,EAAE;kBAAK,CAAE;kBAC1B7C,OAAO,EAAE7K,gBAAiB;kBAC1BoO,QAAQ,EAAE3U,OAAQ;kBAClB4Q,SAAS,EAAC,qNAAqN;kBAC/N3K,KAAK,EAAE;oBACLoM,QAAQ,EAAE/D,MAAM,CAAC6D,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAAtB,QAAA,gBAEF5S,OAAA,CAACd,SAAS;oBAACyT,SAAS,EAAG,yBAAwB5Q,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAA8Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClFhT,OAAA;oBAAA4S,QAAA,EAAM;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,KAAK,KAAK,CAAAvS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmN,IAAI,MAAK,OAAO,KAAInN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyL,OAAO,EAAC,iBACjDlM,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BrL,UAAU,EAAE;YAAEyL,QAAQ,EAAE;UAAI,CAAE;UAC9BhB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C5S,OAAA;YAAK2S,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC5S,OAAA;cAAK2S,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7H5S,OAAA;gBAAK2S,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5S,OAAA;kBAAK2S,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClF5S,OAAA;oBAAM2S,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNhT,OAAA;kBAAA4S,QAAA,gBACE5S,OAAA;oBAAI2S,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpDhT,OAAA;oBAAG2S,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGDhT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BrL,UAAU,EAAE;YAAEyL,QAAQ,EAAE,CAAC;YAAE4C,IAAI,EAAE;UAAU,CAAE;UAC7C5D,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzC5S,OAAA;YAAK2S,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9G5S,OAAA;cAAK2S,SAAS,EAAC;YAA6E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnGhT,OAAA;cAAK2S,SAAS,EAAC;YAA+E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrGhT,OAAA;cAAK2S,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxF5S,OAAA;gBAAK2S,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5C5S,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBACTI,OAAO,EAAE;oBACPwC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBW,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFzO,UAAU,EAAE;oBACVyL,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChB0C,IAAI,EAAE;kBACR,CAAE;kBACF5D,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExB5S,OAAA;oBAAI2S,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7G5S,OAAA,CAACzB,MAAM,CAACkY,IAAI;sBACVjD,OAAO,EAAE;wBACPoD,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACF1O,UAAU,EAAE;wBACVyL,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChB0C,IAAI,EAAE;sBACR,CAAE;sBACF5D,SAAS,EAAC,+HAA+H;sBACzI3K,KAAK,EAAE;wBACL6O,cAAc,EAAE,WAAW;wBAC3BhB,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCjP,MAAM,EAAE;sBACV,CAAE;sBAAA+L,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACdhT,OAAA;sBAAA6S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhT,OAAA,CAACzB,MAAM,CAACkY,IAAI;sBACVjD,OAAO,EAAE;wBACPuC,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACF7N,UAAU,EAAE;wBACVyL,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChB0C,IAAI,EAAE;sBACR,CAAE;sBACFvO,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChB4S,UAAU,EAAE,KAAK;wBACjBf,UAAU,EAAE;sBACd,CAAE;sBAAAnD,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGbhT,OAAA,CAACzB,MAAM,CAACwY,CAAC;kBACP1D,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BrL,UAAU,EAAE;oBAAE4L,KAAK,EAAE,GAAG;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBAC1ChB,SAAS,EAAC,8GAA8G;kBACxH3K,KAAK,EAAE;oBACL9D,KAAK,EAAE,SAAS;oBAChB6R,UAAU,EAAE,6BAA6B;oBACzC5B,UAAU,EAAE,0CAA0C;oBACtD0B,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAAlD,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGXhT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAE0C,KAAK,EAAE;kBAAI,CAAE;kBACpCxC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAE0C,KAAK,EAAE;kBAAE,CAAE;kBAClC9N,UAAU,EAAE;oBAAE4L,KAAK,EAAE,GAAG;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBAC1ChB,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExB5S,OAAA;oBAAG2S,SAAS,EAAC,6JAA6J;oBACvK3K,KAAK,EAAE;sBACL+N,UAAU,EAAE,6BAA6B;sBACzCiB,SAAS,EAAE;oBACb,CAAE;oBAAApE,QAAA,EACFnQ;kBAAiB;oBAAAoQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGbhT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BrL,UAAU,EAAE;oBAAE4L,KAAK,EAAE,CAAC;oBAAEH,QAAQ,EAAE;kBAAI,CAAE;kBACxChB,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACEpO,IAAI,EAAEhF,OAAO;oBACbyX,KAAK,EAAEpV,WAAW,CAACsF,MAAM;oBACzB+P,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBzS,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE5F,QAAQ;oBACdqY,KAAK,EAAEtG,aAAa,CAACxJ,MAAM;oBAC3B+P,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpBzS,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEzF,OAAO;oBACbkY,KAAK,EAAEpV,WAAW,CAACgF,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuD,aAAa,GAAG,CAAC,CAAC,CAACpD,MAAM;oBAC1D+P,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpBzS,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAE1F,MAAM;oBACZmY,KAAK,EAAEpV,WAAW,CAAC2K,MAAM,CAAC,CAACC,GAAG,EAAEzF,CAAC,KAAKyF,GAAG,IAAIzF,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgP,cAAc,CAAC,CAAC;oBACjFgC,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpBzS,WAAW,EAAE;kBACf,CAAC,CACF,CAACkF,GAAG,CAAC,CAACwN,IAAI,EAAElN,KAAK,kBAChBnK,OAAA,CAACzB,MAAM,CAAC6U,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAE0C,KAAK,EAAE;oBAAI,CAAE;oBACpCxC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAE0C,KAAK,EAAE;oBAAE,CAAE;oBAClC9N,UAAU,EAAE;sBAAE4L,KAAK,EAAE,GAAG,GAAG3J,KAAK,GAAG,GAAG;sBAAEwJ,QAAQ,EAAE;oBAAI,CAAE;oBACxDyC,UAAU,EAAE;sBAAEJ,KAAK,EAAE,IAAI;sBAAEzC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCZ,SAAS,EAAG,qBAAoB0E,IAAI,CAACF,UAAW,8EAA8E;oBAC9HnP,KAAK,EAAE;sBACLuM,MAAM,EAAG,aAAY8C,IAAI,CAAC1S,WAAY,IAAG;sBACzCwD,SAAS,EAAG,cAAakP,IAAI,CAAC1S,WAAY;oBAC5C,CAAE;oBAAAiO,QAAA,gBAEF5S,OAAA;sBAAK2S,SAAS,EAAC;oBAAgE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFhT,OAAA,CAACqX,IAAI,CAAC7S,IAAI;sBACRmO,SAAS,EAAC,kDAAkD;sBAC5D3K,KAAK,EAAE;wBAAE9D,KAAK,EAAEmT,IAAI,CAACD,SAAS;wBAAEvQ,MAAM,EAAE;sBAAyC;oBAAE;sBAAAgM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACFhT,OAAA;sBACE2S,SAAS,EAAC,0EAA0E;sBACpF3K,KAAK,EAAE;wBACL9D,KAAK,EAAEmT,IAAI,CAACD,SAAS;wBACrBrB,UAAU,EAAG,6BAA4B;wBACzClP,MAAM,EAAE,oCAAoC;wBAC5CuN,QAAQ,EAAE;sBACZ,CAAE;sBAAAxB,QAAA,EAEDyE,IAAI,CAACJ;oBAAK;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNhT,OAAA;sBACE2S,SAAS,EAAC,4CAA4C;sBACtD3K,KAAK,EAAE;wBACL9D,KAAK,EAAE,SAAS;wBAChB6R,UAAU,EAAE,6BAA6B;wBACzC3B,QAAQ,EAAE;sBACZ,CAAE;sBAAAxB,QAAA,EAEDyE,IAAI,CAACH;oBAAK;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCD7I,KAAK;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZjR,OAAO,iBACN/B,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBX,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3D5S,OAAA,CAACzB,MAAM,CAAC6U,GAAG;YACTI,OAAO,EAAE;cAAEgD,MAAM,EAAE;YAAI,CAAE;YACzBtO,UAAU,EAAE;cAAEyL,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAE0C,IAAI,EAAE;YAAS,CAAE;YAC9D5D,SAAS,EAAC;UAA6E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFhT,OAAA;YAAG2S,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACjR,OAAO,iBACP/B,OAAA,CAACzB,MAAM,CAAC6U,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BrL,UAAU,EAAE;YAAE4L,KAAK,EAAE,GAAG;YAAEH,QAAQ,EAAE;UAAI,CAAE;UAC1ChB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjE5S,OAAA;YAAK2S,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/BjC,aAAa,CAACxJ,MAAM,GAAG,CAAC,iBACvBnH,OAAA,CAACzB,MAAM,CAAC6U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAE0C,KAAK,EAAE;cAAI,CAAE;cACpCxC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAE0C,KAAK,EAAE;cAAE,CAAE;cAClC9N,UAAU,EAAE;gBAAE4L,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjB5S,OAAA;gBAAI2S,SAAS,EAAC,gGAAgG;gBAAC3K,KAAK,EAAE;kBACpHmM,UAAU,EAAE,mDAAmD;kBAC/D0B,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,UAAU,EAAE,6BAA6B;kBACzClP,MAAM,EAAE;gBACV,CAAE;gBAAA+L,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLhT,OAAA;gBACE2S,SAAS,EAAC,8DAA8D;gBACxE3K,KAAK,EAAE;kBACLsP,GAAG,EAAEjH,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG7D,MAAM,CAAC6D,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM;kBACnFD,OAAO,EAAE5D,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;gBAC9C,CAAE;gBAAAtB,QAAA,GAGDjC,aAAa,CAAC,CAAC,CAAC,iBACf3Q,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBAETmE,GAAG,EAAE9W,IAAI,IAAI8M,MAAM,CAACoD,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAc+M,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI;kBACnC,kBAAgB,CAAE;kBAClBoM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC,GAAG;oBAAEH,CAAC,EAAE;kBAAG,CAAE;kBACxCC,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVI,CAAC,EAAE,CAAC;oBACJH,CAAC,EAAE,CAAC;oBACJyC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBW,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFzO,UAAU,EAAE;oBACV4L,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbqC,KAAK,EAAE;sBAAErC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY,CAAC;oBAC3DI,OAAO,EAAE;sBAAEhD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFH,UAAU,EAAE;oBAAEJ,KAAK,EAAE,IAAI;oBAAEzC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,oBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACL4M,MAAM,EAAEvE,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;oBACpDjM,SAAS,EAAEsJ,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE0K,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3BqM,MAAM,EAAEhD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChF6N,YAAY,EAAExD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEkN,UAAU,EAAE7C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA2L,QAAA,gBAGF5S,OAAA;oBAAK2S,SAAS,EAAC,sJAAsJ;oBAAAC,QAAA,eACnK5S,OAAA;sBAAM2S,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNhT,OAAA;oBACE2S,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAClC,KAAM,mBAAkByM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAawI,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,IAAG;sBAC9DqQ,KAAK,EAAE;oBACT,CAAE;oBAAA/B,QAAA,eAEF5S,OAAA;sBACE2S,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAyO,QAAA,gBAEnH5S,OAAA;wBAAK2S,SAAS,EAAC;sBAAgE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFhT,OAAA;wBACE2S,SAAS,EAAC,oMAAoM;wBAC9M3K,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChBqQ,MAAM,EAAE;wBACV,CAAE;wBAAA3B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAG,yBAAwBlS,IAAI,IAAIkQ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA2L,QAAA,gBAEnI5S,OAAA,CAACH,cAAc;0BACbY,IAAI,EAAEkQ,aAAa,CAAC,CAAC,CAAE;0BACvB0D,IAAI,EAAC,IAAI;0BACTC,gBAAgB,EAAE,IAAK;0BACvBtM,KAAK,EAAE;4BACL2M,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE;0BACV;wBAAE;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAEDzR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmP,aAAa,CAAC,CAAC,CAAC,CAAC;sBAAA;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC,eAGNhT,OAAA;wBACE2S,SAAS,EAAC,iCAAiC;wBAC3C3K,KAAK,EAAE;0BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC/B;wBAAU,CAAE;wBAAAuO,QAAA,EAEjDjC,aAAa,CAAC,CAAC,CAAC,CAAC7G;sBAAI;wBAAA+I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELhT,OAAA;wBAAK2S,SAAS,EAAC,yBAAyB;wBAAC3K,KAAK,EAAE;0BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;wBAAU,CAAE;wBAAAwO,QAAA,GACxFjC,aAAa,CAAC,CAAC,CAAC,CAACzK,OAAO,CAACgP,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENhT,OAAA;wBAAK2S,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD5S,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;0BAAU,CAAE;0BAAAwO,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAChH,iBAAiB;wBAAA;0BAAAkJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPhT,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;0BAAU,CAAE;0BAAAwO,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAACpG,aAAa;wBAAA;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAtGA,UAASrC,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI,EAAC;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuG3B,CACb,EAGArC,aAAa,CAAC,CAAC,CAAC,iBACf3Q,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBAETmE,GAAG,EAAE9W,IAAI,IAAI8M,MAAM,CAACoD,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAc+M,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI;kBACnC,kBAAgB,CAAE;kBAClBoM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC,GAAG;oBAAEyC,KAAK,EAAE;kBAAI,CAAE;kBAC7CxC,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVC,CAAC,EAAE,CAAC;oBACJyC,KAAK,EAAE,CAAC;oBACRW,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxBpD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFrL,UAAU,EAAE;oBACV4L,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbgD,OAAO,EAAE;sBAAEhD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY,CAAC;oBAC7DhD,CAAC,EAAE;sBAAEI,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFH,UAAU,EAAE;oBAAEJ,KAAK,EAAE,IAAI;oBAAEzC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,yBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACL4M,MAAM,EAAEvE,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;oBACpDjM,SAAS,EAAEsJ,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE0K,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3BqM,MAAM,EAAEhD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChF6N,YAAY,EAAExD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEkN,UAAU,EAAE7C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBACF,gBAAa,QAAQ;kBAAA2L,QAAA,gBAIrB5S,OAAA;oBAAK2S,SAAS,EAAC,4JAA4J;oBAAAC,QAAA,eACzK5S,OAAA;sBAAM2S,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAGNhT,OAAA,CAACzB,MAAM,CAAC6U,GAAG;oBACTI,OAAO,EAAE;sBAAEgD,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEjD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDrL,UAAU,EAAE;sBAAEyL,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9ClB,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErE5S,OAAA,CAACnB,OAAO;sBAAC8T,SAAS,EAAC;oBAA0C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGbhT,OAAA;oBACE2S,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAClC,KAAM,sBAAqByM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5JyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAawI,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/FqQ,KAAK,EAAE;oBACT,CAAE;oBAAA/B,QAAA,eAEF5S,OAAA;sBACE2S,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH6D,KAAK,EAAE;wBACLmM,UAAU,EAAG,GAAExD,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAyO,QAAA,gBAEF5S,OAAA;wBAAK2S,SAAS,EAAC;sBAA4E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlGhT,OAAA;wBACE2S,SAAS,EAAC,wMAAwM;wBAClN3K,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChB6R,UAAU,EAAE,6BAA6B;0BACzCxB,MAAM,EAAE;wBACV,CAAE;wBAAA3B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAG,yBAAwBlS,IAAI,IAAIkQ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA2L,QAAA,gBAEnI5S,OAAA;0BAAK2S,SAAS,EAAC,UAAU;0BAAAC,QAAA,gBACvB5S,OAAA,CAACH,cAAc;4BACbY,IAAI,EAAEkQ,aAAa,CAAC,CAAC,CAAE;4BACvB0D,IAAI,EAAC,IAAI;4BACTC,gBAAgB,EAAE,KAAM;4BACxBtM,KAAK,EAAE;8BACL2M,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE;4BACV;0BAAE;4BAAA/B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EAEDrC,aAAa,CAAC,CAAC,CAAC,CAAC6D,QAAQ,iBACxBxU,OAAA;4BACEgI,KAAK,EAAE;8BACLkJ,QAAQ,EAAE,UAAU;8BACpBuD,MAAM,EAAE,MAAM;8BACdC,KAAK,EAAE,MAAM;8BACbC,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACdC,eAAe,EAAE,SAAS;8BAC1BC,YAAY,EAAE,KAAK;8BACnBP,MAAM,EAAE,mBAAmB;8BAC3BpM,SAAS,EAAE,kCAAkC;8BAC7C4M,MAAM,EAAE;4BACV,CAAE;4BACFtQ,KAAK,EAAC;0BAAQ;4BAAAoO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EAELzR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmP,aAAa,CAAC,CAAC,CAAC,CAAC,EACrDlQ,IAAI,IAAIkQ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,KAAKxG,IAAI,CAACwG,GAAG,iBACxCjH,OAAA;0BACE2S,SAAS,EAAC,4DAA4D;0BACtE3K,KAAK,EAAE;4BACLmM,UAAU,EAAE,0CAA0C;4BACtDhM,SAAS,EAAE;0BACb,CAAE;0BAAAyK,QAAA,eAEF5S,OAAA,CAAClB,MAAM;4BAAC6T,SAAS,EAAC;0BAAuB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,gBAC1D5S,OAAA;0BACE2S,SAAS,EAAC,6BAA6B;0BACvC3K,KAAK,EAAE;4BACL9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC/B,SAAS;4BACtC0R,UAAU,EAAG,eAAcpF,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA+L,QAAA,EAEDjC,aAAa,CAAC,CAAC,CAAC,CAAC7G;wBAAI;0BAAA+I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,EACJ1B,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,iBAClCjH,OAAA;0BACE2S,SAAS,EAAC,yDAAyD;0BACnE3K,KAAK,EAAE;4BACLmM,UAAU,EAAE,0CAA0C;4BACtDjQ,KAAK,EAAE,SAAS;4BAChBiE,SAAS,EAAE,+BAA+B;4BAC1CoM,MAAM,EAAE,mBAAmB;4BAC3BH,QAAQ,EAAE;0BACZ,CAAE;0BAAAxB,QAAA,EACH;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENhT,OAAA;wBACE2S,SAAS,EAAG,6DAA4DhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ8D,KAAK,EAAE;0BACLmM,UAAU,EAAG,2BAA0BxD,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAACzB,WAAY,KAAIgM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChB6R,UAAU,EAAE,6BAA6B;0BACzC5N,SAAS,EAAG,cAAawI,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,IAAG;0BAC9DiQ,MAAM,EAAE;wBACV,CAAE;wBAAA3B,QAAA,GAEDjC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC5B,IAAI,iBAAIrG,KAAK,CAACqZ,aAAa,CAAC7G,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC5B,IAAI,EAAE;0BAC7EmO,SAAS,EAAE,SAAS;0BACpB3K,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACFlE,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAE;0BAAU,CAAE;0BAAA0O,QAAA,EAAEjC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC3B;wBAAK;0BAAAoO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC5S,OAAA;0BAAK2S,SAAS,EAAC,oBAAoB;0BAAC3K,KAAK,EAAE;4BACzC9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC/B,SAAS;4BACtC0R,UAAU,EAAG,eAAcpF,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA+L,QAAA,GACCjC,aAAa,CAAC,CAAC,CAAC,CAACzK,OAAO,CAACgP,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAENhT,OAAA;0BAAK2S,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChD5S,OAAA;4BAAK2S,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B5S,OAAA;8BAAK2S,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrD5S,OAAA,CAAChB,OAAO;gCAAC2T,SAAS,EAAC,SAAS;gCAAC3K,KAAK,EAAE;kCAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;gCAAU;8BAAE;gCAAAyO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClFhT,OAAA;gCAAM2S,SAAS,EAAC,WAAW;gCAAC3K,KAAK,EAAE;kCAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;gCAAU,CAAE;gCAAAwO,QAAA,EAC3EjC,aAAa,CAAC,CAAC,CAAC,CAAChH;8BAAiB;gCAAAkJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNhT,OAAA;8BAAK2S,SAAS,EAAC,oBAAoB;8BAAC3K,KAAK,EAAE;gCAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;8BAAU,CAAE;8BAAAwO,QAAA,EAAC;4BAAO;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACNhT,OAAA;4BAAK2S,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1B5S,OAAA;8BAAK2S,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrD5S,OAAA,CAACjB,OAAO;gCAAC4T,SAAS,EAAC,SAAS;gCAAC3K,KAAK,EAAE;kCAAE9D,KAAK,EAAE;gCAAU;8BAAE;gCAAA2O,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5DhT,OAAA;gCAAM2S,SAAS,EAAC,WAAW;gCAAC3K,KAAK,EAAE;kCAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;gCAAU,CAAE;gCAAAwO,QAAA,EAC3EjC,aAAa,CAAC,CAAC,CAAC,CAACpG;8BAAa;gCAAAsI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNhT,OAAA;8BAAK2S,SAAS,EAAC,oBAAoB;8BAAC3K,KAAK,EAAE;gCAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;8BAAU,CAAE;8BAAAwO,QAAA,EAAC;4BAAM;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA5MA,SAAQrC,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI,EAAC;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6M1B,CACb,EAGArC,aAAa,CAAC,CAAC,CAAC,iBACf3Q,OAAA,CAACzB,MAAM,CAAC6U,GAAG;kBAETmE,GAAG,EAAE9W,IAAI,IAAI8M,MAAM,CAACoD,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC,GAAGrD,aAAa,GAAG,IAAK;kBACtF,gBAAc+M,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI;kBACnC,kBAAgB,CAAE;kBAClBoM,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,GAAG;oBAAEH,CAAC,EAAE;kBAAG,CAAE;kBACvCC,OAAO,EAAE;oBACPF,OAAO,EAAE,CAAC;oBACVI,CAAC,EAAE,CAAC;oBACJH,CAAC,EAAE,CAAC;oBACJyC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBW,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFzO,UAAU,EAAE;oBACV4L,KAAK,EAAE,GAAG;oBACVH,QAAQ,EAAE,GAAG;oBACbqC,KAAK,EAAE;sBAAErC,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY,CAAC;oBAC3DI,OAAO,EAAE;sBAAEhD,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAE0C,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFH,UAAU,EAAE;oBAAEJ,KAAK,EAAE,IAAI;oBAAEzC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCZ,SAAS,EAAG,oBACVpB,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GACrC,yCAAyC,GACzC,EACL,EAAE;kBACHe,KAAK,EAAE;oBACL4M,MAAM,EAAEvE,MAAM,CAAC6D,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;oBACpDjM,SAAS,EAAEsJ,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;oBACjFJ,MAAM,EAAE0K,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAC7C,0EAA0E,GAC1E,MAAM;oBACViB,UAAU,EAAE,eAAe;oBAC3BqM,MAAM,EAAEhD,mBAAmB,CAACZ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;oBAChF6N,YAAY,EAAExD,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;oBAClEkN,UAAU,EAAE7C,aAAa,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,CAAC,GAAG,wEAAwE,GAAG;kBAC/H,CAAE;kBAAA2L,QAAA,gBAGF5S,OAAA;oBAAK2S,SAAS,EAAC,yJAAyJ;oBAAAC,QAAA,eACtK5S,OAAA;sBAAM2S,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eAGNhT,OAAA;oBACE2S,SAAS,EAAG,8BAA6BhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAClC,KAAM,mBAAkByM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIyD,KAAK,EAAE;sBACLG,SAAS,EAAG,cAAawI,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC9B,WAAY,IAAG;sBAC9DqQ,KAAK,EAAE;oBACT,CAAE;oBAAA/B,QAAA,eAEF5S,OAAA;sBACE2S,SAAS,EAAG,GAAEhC,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAyO,QAAA,gBAEnH5S,OAAA;wBAAK2S,SAAS,EAAC;sBAAgE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtFhT,OAAA;wBACE2S,SAAS,EAAC,sMAAsM;wBAChN3K,KAAK,EAAE;0BACL9D,KAAK,EAAE,SAAS;0BAChBqQ,MAAM,EAAE;wBACV,CAAE;wBAAA3B,QAAA,EACH;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGNhT,OAAA;wBAAK2S,SAAS,EAAG,yBAAwBlS,IAAI,IAAIkQ,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAG,KAAKxG,IAAI,CAACwG,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAA2L,QAAA,eACnI5S,OAAA,CAACH,cAAc;0BACbY,IAAI,EAAEkQ,aAAa,CAAC,CAAC,CAAE;0BACvB0D,IAAI,EAAC,IAAI;0BACTC,gBAAgB,EAAE,IAAK;0BACvBtM,KAAK,EAAE;4BACL2M,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE;0BACV;wBAAE;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAGNhT,OAAA;wBACE2S,SAAS,EAAC,iCAAiC;wBAC3C3K,KAAK,EAAE;0BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAC/B;wBAAU,CAAE;wBAAAuO,QAAA,EAEjDjC,aAAa,CAAC,CAAC,CAAC,CAAC7G;sBAAI;wBAAA+I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAELhT,OAAA;wBAAK2S,SAAS,EAAC,yBAAyB;wBAAC3K,KAAK,EAAE;0BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;wBAAU,CAAE;wBAAAwO,QAAA,GACxFjC,aAAa,CAAC,CAAC,CAAC,CAACzK,OAAO,CAACgP,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENhT,OAAA;wBAAK2S,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD5S,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;0BAAU,CAAE;0BAAAwO,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAAChH,iBAAiB;wBAAA;0BAAAkJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACPhT,OAAA;0BAAMgI,KAAK,EAAE;4BAAE9D,KAAK,EAAEyM,aAAa,CAAC,CAAC,CAAC,CAACvK,IAAI,CAAChC;0BAAU,CAAE;0BAAAwO,QAAA,GAAC,eACpD,EAACjC,aAAa,CAAC,CAAC,CAAC,CAACpG,aAAa;wBAAA;0BAAAsI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAnGA,SAAQrC,aAAa,CAAC,CAAC,CAAC,CAAC1J,GAAI,EAAC;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoG1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASI,CACb,EAGA/P,cAAc,GACb;YACAJ,WAAW,CAACsE,MAAM,GAAG,CAAC,iBACpBnH,OAAA,CAACzB,MAAM,CAAC6U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BrL,UAAU,EAAE;gBAAE4L,KAAK,EAAE,CAAC;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cACxChB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtC5S,OAAA;gBAAK2S,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC5S,OAAA,CAACzB,MAAM,CAACkZ,EAAE;kBACR9E,SAAS,EAAC,kDAAkD;kBAC5D3K,KAAK,EAAE;oBACLmM,UAAU,EAAG,0BAAyBpQ,YAAY,CAACd,cAAc,CAAC,CAAC0B,WAAY,KAAIZ,YAAY,CAACd,cAAc,CAAC,CAACmB,SAAU,GAAE;oBAC5HyR,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzClP,MAAM,EAAG,wBAAuB9C,YAAY,CAACd,cAAc,CAAC,CAAC0B,WAAY;kBAC3E,CAAE;kBACF6O,OAAO,EAAE;oBAAEwC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC9N,UAAU,EAAE;oBAAEyL,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAjB,QAAA,GAE7C7O,YAAY,CAACd,cAAc,CAAC,CAAC4B,UAAU,EAAC,GAAC,EAACd,YAAY,CAACd,cAAc,CAAC,CAACwB,KAAK,EAAC,UAAQ,EAACV,YAAY,CAACd,cAAc,CAAC,CAAC4B,UAAU;gBAAA;kBAAAgO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC,eACZhT,OAAA;kBAAG2S,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,GAC1D/P,WAAW,CAACsE,MAAM,EAAC,2BACtB;gBAAA;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJhT,OAAA,CAACzB,MAAM,CAAC8X,MAAM;kBACZD,UAAU,EAAE;oBAAEJ,KAAK,EAAE;kBAAK,CAAE;kBAC5BM,QAAQ,EAAE;oBAAEN,KAAK,EAAE;kBAAK,CAAE;kBAC1B7C,OAAO,EAAEA,CAAA,KAAMjQ,iBAAiB,CAAC,IAAI,CAAE;kBACvCyP,SAAS,EAAC,mJAAmJ;kBAAAC,QAAA,EAC9J;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eAGNhT,OAAA;gBAAK2S,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrC5S,OAAA;kBAAK2S,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACjC/P,WAAW,CAACgH,GAAG,CAAC,CAAC6N,QAAQ,EAAEvN,KAAK,KAAK;oBACpC,MAAMwN,UAAU,GAAGxN,KAAK,GAAG,CAAC;oBAC5B,MAAMmH,aAAa,GAAG7Q,IAAI,IAAI8M,MAAM,CAACmK,QAAQ,CAACzQ,GAAG,CAAC,KAAKsG,MAAM,CAAC9M,IAAI,CAACwG,GAAG,CAAC;oBAEvE,oBACEjH,OAAA,CAACzB,MAAM,CAAC6U,GAAG;sBAETmE,GAAG,EAAEjG,aAAa,GAAGzN,WAAW,GAAG,IAAK;sBACxC,gBAAc6T,QAAQ,CAACzQ,GAAI;sBAC3B,kBAAgB0Q,UAAW;sBAC3BtE,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAG,CAAE;sBAC/BC,OAAO,EAAE;wBAAEF,OAAO,EAAE,CAAC;wBAAEC,CAAC,EAAE;sBAAE,CAAE;sBAC9BrL,UAAU,EAAE;wBAAE4L,KAAK,EAAE,GAAG,GAAG3J,KAAK,GAAG,IAAI;wBAAEwJ,QAAQ,EAAE;sBAAI,CAAE;sBACzDyC,UAAU,EAAE;wBAAEJ,KAAK,EAAE,IAAI;wBAAEzC,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCZ,SAAS,EAAG,+BACVpB,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GAC7B,yCAAyC,GACzC,EACL,EAAE;sBACHe,KAAK,EAAE;wBACLC,SAAS,EAAEsJ,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GAAG,aAAa,GAAG,UAAU;wBACzEJ,MAAM,EAAE0K,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GACrC,2EAA2E,GAC3E,MAAM;wBACViB,UAAU,EAAE,eAAe;wBAC3BqM,MAAM,EAAEhD,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;wBACxE6N,YAAY,EAAEvD,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;wBAChEkN,UAAU,EAAE7C,aAAa,GAAG,2EAA2E,GAAG,aAAa;wBACvHJ,QAAQ,EAAE,UAAU;wBACpB6D,MAAM,EAAEzD,aAAa,GAAG,EAAE,GAAG;sBAC/B,CAAE;sBAAAsB,QAAA,eAGF5S,OAAA;wBACE2S,SAAS,EAAG,oBAAmB+E,QAAQ,CAACtR,IAAI,CAAClC,KAAM,sBAAqBwT,QAAQ,CAACtR,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJyD,KAAK,EAAE;0BACLG,SAAS,EAAG,cAAauP,QAAQ,CAACtR,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAsO,QAAA,eAEF5S,OAAA;0BACE2S,SAAS,EAAG,GAAE+E,QAAQ,CAACtR,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH6D,KAAK,EAAE;4BACLuM,MAAM,EAAG,aAAYmD,QAAQ,CAACtR,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAAiO,QAAA,gBAGF5S,OAAA;4BAAK2S,SAAS,EAAC;0BAA2E;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjGhT,OAAA;4BAAK2S,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpD5S,OAAA;8BAAK2S,SAAS,EAAC,UAAU;8BAAAC,QAAA,eACvB5S,OAAA;gCACE2S,SAAS,EAAC,kJAAkJ;gCAC5J3K,KAAK,EAAE;kCACL9D,KAAK,EAAE,SAAS;kCAChB6R,UAAU,EAAE,6BAA6B;kCACzCxB,MAAM,EAAE,iCAAiC;kCACzCpM,SAAS,EAAE;gCACb,CAAE;gCAAAyK,QAAA,GACH,GACE,EAAC+E,UAAU;8BAAA;gCAAA9E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGNhT,OAAA;8BAAK2S,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvB5S,OAAA,CAACH,cAAc;gCACbY,IAAI,EAAEiX,QAAS;gCACfrD,IAAI,EAAC,IAAI;gCACTC,gBAAgB,EAAE,KAAM;gCACxBtM,KAAK,EAAE;kCACL2M,KAAK,EAAE,MAAM;kCACbC,MAAM,EAAE;gCACV;8BAAE;gCAAA/B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,EAED0E,QAAQ,CAAClD,QAAQ,iBAChBxU,OAAA;gCACEgI,KAAK,EAAE;kCACLkJ,QAAQ,EAAE,UAAU;kCACpBuD,MAAM,EAAE,MAAM;kCACdC,KAAK,EAAE,MAAM;kCACbC,KAAK,EAAE,MAAM;kCACbC,MAAM,EAAE,MAAM;kCACdC,eAAe,EAAE,SAAS;kCAC1BC,YAAY,EAAE,KAAK;kCACnBP,MAAM,EAAE,mBAAmB;kCAC3BpM,SAAS,EAAE,kCAAkC;kCAC7C4M,MAAM,EAAE;gCACV,CAAE;gCACFtQ,KAAK,EAAC;8BAAQ;gCAAAoO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACf,CACF,EAEA1B,aAAa,iBACZtR,OAAA;gCACE2S,SAAS,EAAC,8FAA8F;gCACxG3K,KAAK,EAAE;kCACLmM,UAAU,EAAE,0CAA0C;kCACtDhM,SAAS,EAAE;gCACb,CAAE;gCAAAyK,QAAA,eAEF5S,OAAA,CAAClB,MAAM;kCAAC6T,SAAS,EAAC;gCAA2B;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNhT,OAAA;4BAAK2S,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClC5S,OAAA;8BAAK2S,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExB5S,OAAA;gCAAK2S,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3C5S,OAAA;kCACE2S,SAAS,EAAC,yCAAyC;kCACnD3K,KAAK,EAAE;oCACL9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAC/B,SAAS;oCAC9B0R,UAAU,EAAG,eAAc2B,QAAQ,CAACtR,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAA+L,QAAA,EAED8E,QAAQ,CAAC5N;gCAAI;kCAAA+I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJ1B,aAAa,iBACZtR,OAAA;kCACE2S,SAAS,EAAC,yDAAyD;kCACnE3K,KAAK,EAAE;oCACLmM,UAAU,EAAE,mDAAmD;oCAC/DjQ,KAAK,EAAE,SAAS;oCAChBiE,SAAS,EAAE,8DAA8D;oCACzEoM,MAAM,EAAE,mBAAmB;oCAC3BwB,UAAU,EAAE,6BAA6B;oCACzC3B,QAAQ,EAAE,MAAM;oCAChB0C,UAAU,EAAE;kCACd,CAAE;kCAAAlE,QAAA,EACH;gCAED;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNhT,OAAA;gCAAK2S,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1C8E,QAAQ,CAACjP,KAAK,EAAC,gBAAS,EAACiP,QAAQ,CAACrN,KAAK;8BAAA;gCAAAwI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGNhT,OAAA;4BAAK2S,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1D5S,OAAA;8BACE2S,SAAS,EAAC,oCAAoC;8BAC9C3K,KAAK,EAAE;gCACL9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAC/B,SAAS;gCAC9B0R,UAAU,EAAG,eAAc2B,QAAQ,CAACtR,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAA+L,QAAA,GAED8E,QAAQ,CAACxR,OAAO,CAACgP,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAArC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGNhT,OAAA;8BAAK2S,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9C5S,OAAA;gCACE2S,SAAS,EAAC,8CAA8C;gCACxD3K,KAAK,EAAE;kCACL6M,eAAe,EAAG,GAAE6C,QAAQ,CAACtR,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAChC;gCACvB,CAAE;gCAAAwO,QAAA,gBAEF5S,OAAA,CAAChB,OAAO;kCAAC2T,SAAS,EAAC;gCAAS;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BhT,OAAA;kCAAM2S,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE8E,QAAQ,CAAC/N;gCAAiB;kCAAAkJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACNhT,OAAA;gCACE2S,SAAS,EAAC,8CAA8C;gCACxD3K,KAAK,EAAE;kCACL6M,eAAe,EAAE,WAAW;kCAC5B3Q,KAAK,EAAE;gCACT,CAAE;gCAAA0O,QAAA,gBAEF5S,OAAA,CAACjB,OAAO;kCAAC4T,SAAS,EAAC;gCAAS;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/BhT,OAAA;kCAAM2S,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE8E,QAAQ,CAACnN;gCAAa;kCAAAsI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAtLD0E,QAAQ,CAACzQ,GAAG;sBAAA4L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuLP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,GAED;YACApN,MAAM,CAACS,IAAI,CAAClD,YAAY,CAAC,CAACgE,MAAM,GAAG,CAAC,iBAClCnH,OAAA,CAACzB,MAAM,CAAC6U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BrL,UAAU,EAAE;gBAAE4L,KAAK,EAAE,CAAC;gBAAEH,QAAQ,EAAE;cAAI,CAAE;cACxChB,SAAS,EAAC,4BAA4B;cACtC1E,EAAE,EAAC,yBAAyB;cAAA2E,QAAA,gBAG5B5S,OAAA;gBAAK2S,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC5S,OAAA,CAACzB,MAAM,CAACkZ,EAAE;kBACR9E,SAAS,EAAC,kDAAkD;kBAC5D3K,KAAK,EAAE;oBACLmM,UAAU,EAAE,mDAAmD;oBAC/D0B,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,UAAU,EAAE,6BAA6B;oBACzClP,MAAM,EAAE;kBACV,CAAE;kBACF2M,OAAO,EAAE;oBAAEwC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC9N,UAAU,EAAE;oBAAEyL,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAjB,QAAA,EAC/C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZhT,OAAA;kBAAG2S,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNhT,OAAA;gBAAK2S,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CxK,iBAAiB,CAAC,CAAC,CAACyB,GAAG,CAAEvD,SAAS,IAAK;kBACtC,MAAMZ,MAAM,GAAG3B,YAAY,CAACuC,SAAS,CAAC;kBACtC,MAAMmK,UAAU,GAAGtN,YAAY,CAACmD,SAAS,CAAC;kBAC1C,MAAMsR,QAAQ,GAAGnH,UAAU,CAAClQ,KAAK,CAACqJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;kBAE/C,oBACE5J,OAAA,CAACzB,MAAM,CAAC6U,GAAG;oBAETmE,GAAG,EAAGM,EAAE,IAAMpU,UAAU,CAACkE,OAAO,CAACrB,SAAS,CAAC,GAAGuR,EAAI;oBAClDxE,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAG,CAAE;oBAC/BC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BrL,UAAU,EAAE;sBAAE4L,KAAK,EAAE,GAAG;sBAAEH,QAAQ,EAAE;oBAAI,CAAE;oBAC1ChB,SAAS,EAAC,mGAAmG;oBAC7G1E,EAAE,EAAG,UAAS3H,SAAU,EAAE;oBAC1B,eAAaA,SAAU;oBAAAsM,QAAA,gBAGvB5S,OAAA;sBAAK2S,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD5S,OAAA;wBAAK2S,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC5S,OAAA;0BACE2S,SAAS,EAAC,gEAAgE;0BAC1E3K,KAAK,EAAE;4BACLmM,UAAU,EAAG,2BAA0BzO,MAAM,CAACf,WAAY,OAAMe,MAAM,CAACtB,SAAU,KAAI;4BACrFmQ,MAAM,EAAG,aAAY7O,MAAM,CAACf,WAAY,IAAG;4BAC3CwD,SAAS,EAAG,cAAazC,MAAM,CAACpB,WAAY;0BAC9C,CAAE;0BAAAsO,QAAA,EAEDlN,MAAM,CAACb;wBAAU;0BAAAgO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACNhT,OAAA;0BAAA4S,QAAA,gBACE5S,OAAA;4BACE2S,SAAS,EAAC,0BAA0B;4BACpC3K,KAAK,EAAE;8BACL9D,KAAK,EAAEwB,MAAM,CAACrB,SAAS;8BACvB0R,UAAU,EAAG,eAAcrQ,MAAM,CAACpB,WAAY,EAAC;8BAC/CuC,MAAM,EAAE;4BACV,CAAE;4BAAA+L,QAAA,GAEDlN,MAAM,CAACjB,KAAK,EAAC,SAChB;0BAAA;4BAAAoO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLhT,OAAA;4BAAG2S,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,GACjCnC,UAAU,CAAClQ,KAAK,CAAC4G,MAAM,EAAC,oBAAa,EAACzB,MAAM,CAAChB,WAAW;0BAAA;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNhT,OAAA,CAACzB,MAAM,CAAC8X,MAAM;wBACZD,UAAU,EAAE;0BAAEJ,KAAK,EAAE;wBAAK,CAAE;wBAC5BM,QAAQ,EAAE;0BAAEN,KAAK,EAAE;wBAAK,CAAE;wBAC1B7C,OAAO,EAAEA,CAAA,KAAM/L,kBAAkB,CAACd,SAAS,CAAE;wBAC7CqM,SAAS,EAAC,gJAAgJ;wBAAAC,QAAA,GAC3J,YACW,EAACnC,UAAU,CAAClQ,KAAK,CAAC4G,MAAM,EAAC,GACrC;sBAAA;wBAAA0L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eAGNhT,OAAA;sBAAK2S,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EAClEgF,QAAQ,CAAC/N,GAAG,CAAC,CAAC6N,QAAQ,EAAEvN,KAAK,KAAK;wBACjC,MAAMmH,aAAa,GAAG7Q,IAAI,IAAIiX,QAAQ,CAACzQ,GAAG,KAAKxG,IAAI,CAACwG,GAAG;wBACvD,MAAM6Q,UAAU,GAAG3N,KAAK,GAAG,CAAC;wBAE5B,oBACEnK,OAAA,CAACzB,MAAM,CAAC6U,GAAG;0BAET,gBAAcsE,QAAQ,CAACzQ,GAAI;0BAC3B,kBAAgB6Q,UAAW;0BAC3BzE,OAAO,EAAE;4BAAEC,OAAO,EAAE,CAAC;4BAAE0C,KAAK,EAAE;0BAAI,CAAE;0BACpCxC,OAAO,EAAE;4BAAEF,OAAO,EAAE,CAAC;4BAAE0C,KAAK,EAAE;0BAAE,CAAE;0BAClC9N,UAAU,EAAE;4BAAE4L,KAAK,EAAE,GAAG,GAAG3J,KAAK,GAAG,GAAG;4BAAEwJ,QAAQ,EAAE;0BAAI,CAAE;0BACxDyC,UAAU,EAAE;4BAAEJ,KAAK,EAAE,IAAI;4BAAEzC,CAAC,EAAE,CAAC;0BAAE,CAAE;0BACnCZ,SAAS,EAAG,YACVpB,mBAAmB,CAACmG,QAAQ,CAACzQ,GAAG,CAAC,GAC7B,2BAA2B,GAC3B,EACL,EAAE;0BAAA2L,QAAA,eAEH5S,OAAA;4BACE2S,SAAS,EAAG,qBAAoB+E,QAAQ,CAACtR,IAAI,CAAClC,KAAM,qBAAoBwT,QAAQ,CAACtR,IAAI,CAAC7B,IAAK,YAAY;4BACvGyD,KAAK,EAAE;8BACLG,SAAS,EAAG,cAAauP,QAAQ,CAACtR,IAAI,CAAC9B,WAAY;4BACrD,CAAE;4BAAAsO,QAAA,eAEF5S,OAAA;8BACE2S,SAAS,EAAG,GAAE+E,QAAQ,CAACtR,IAAI,CAACjC,OAAQ,uEAAuE;8BAAAyO,QAAA,gBAE3G5S,OAAA;gCAAK2S,SAAS,EAAC;8BAAgE;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAGtFhT,OAAA;gCACE2S,SAAS,EAAC,mGAAmG;gCAC7G3K,KAAK,EAAE;kCACLmM,UAAU,EAAEzO,MAAM,CAACf,WAAW;kCAC9BT,KAAK,EAAE,SAAS;kCAChBqQ,MAAM,EAAE;gCACV,CAAE;gCAAA3B,QAAA,GACH,GACE,EAACkF,UAAU;8BAAA;gCAAAjF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAGNhT,OAAA;gCAAK2S,SAAS,EAAG,yBACfrB,aAAa,GACT,wCAAwC,GACxC,EACL,EAAE;gCAAAsB,QAAA,gBACD5S,OAAA,CAACH,cAAc;kCACbY,IAAI,EAAEiX,QAAS;kCACfrD,IAAI,EAAC,IAAI;kCACTC,gBAAgB,EAAE,IAAK;kCACvBtM,KAAK,EAAE;oCACL2M,KAAK,EAAE,MAAM;oCACbC,MAAM,EAAE;kCACV;gCAAE;kCAAA/B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,EACD1B,aAAa,iBACZtR,OAAA;kCACE2S,SAAS,EAAC,iGAAiG;kCAC3G3K,KAAK,EAAE;oCACLmM,UAAU,EAAE,0CAA0C;oCACtDhM,SAAS,EAAE;kCACb,CAAE;kCAAAyK,QAAA,eAEF5S,OAAA,CAAClB,MAAM;oCAAC6T,SAAS,EAAC;kCAA2B;oCAAAE,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CACN;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGNhT,OAAA;gCACE2S,SAAS,EAAC,iCAAiC;gCAC3C3K,KAAK,EAAE;kCAAE9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAC/B;gCAAU,CAAE;gCAAAuO,QAAA,GAEzC8E,QAAQ,CAAC5N,IAAI,EACbwH,aAAa,iBACZtR,OAAA;kCAAM2S,SAAS,EAAC,8BAA8B;kCAAAC,QAAA,EAAC;gCAAE;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACxD;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC,CAAC,eAELhT,OAAA;gCAAK2S,SAAS,EAAC,yBAAyB;gCAAC3K,KAAK,EAAE;kCAAE9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAChC;gCAAU,CAAE;gCAAAwO,QAAA,GAChF8E,QAAQ,CAACxR,OAAO,CAACgP,cAAc,CAAC,CAAC,EAAC,KACrC;8BAAA;gCAAArC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAENhT,OAAA;gCAAK2S,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,gBAChD5S,OAAA;kCAAMgI,KAAK,EAAE;oCAAE9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAChC;kCAAU,CAAE;kCAAAwO,QAAA,GAAC,eAC5C,EAAC8E,QAAQ,CAAC/N,iBAAiB;gCAAA;kCAAAkJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACPhT,OAAA;kCAAMgI,KAAK,EAAE;oCAAE9D,KAAK,EAAEwT,QAAQ,CAACtR,IAAI,CAAChC;kCAAU,CAAE;kCAAAwO,QAAA,GAAC,eAC5C,EAAC8E,QAAQ,CAACnN,aAAa;gCAAA;kCAAAsI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACtB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC,GAxFD0E,QAAQ,CAACzQ,GAAG;0BAAA4L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAyFP,CAAC;sBAEjB,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,EAGLvC,UAAU,CAAClQ,KAAK,CAAC4G,MAAM,GAAG,CAAC,iBAC1BnH,OAAA;sBAAK2S,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/B5S,OAAA;wBAAG2S,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAACnC,UAAU,CAAClQ,KAAK,CAAC4G,MAAM,GAAG,CAAC,EAAC,gCAChC;sBAAA;wBAAA0L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA,GA7JI1M,SAAS;oBAAAuM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8JJ,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAEf,EAMAnR,WAAW,CAACsF,MAAM,KAAK,CAAC,IAAI,CAACpF,OAAO,iBACnC/B,OAAA,CAACzB,MAAM,CAAC6U,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAE0C,KAAK,EAAE;cAAI,CAAE;cACpCxC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAE0C,KAAK,EAAE;cAAE,CAAE;cAClCrD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7B5S,OAAA,CAACpB,QAAQ;gBAAC+T,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DhT,OAAA;gBAAI2S,SAAS,EAAC,yBAAyB;gBAAC3K,KAAK,EAAE;kBAC7C9D,KAAK,EAAE,SAAS;kBAChB6R,UAAU,EAAE,6BAA6B;kBACzCe,UAAU,EAAE;gBACd,CAAE;gBAAAlE,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBhT,OAAA;gBAAG2S,SAAS,EAAC,SAAS;gBAAC3K,KAAK,EAAE;kBAC5B9D,KAAK,EAAE,SAAS;kBAChB6R,UAAU,EAAE,6BAA6B;kBACzCe,UAAU,EAAE;gBACd,CAAE;gBAAAlE,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC5S,EAAA,CA51FID,kBAAkB;EAAA,QACJ1B,WAAW,EA4CZC,WAAW;AAAA;AAAAqZ,EAAA,GA7CxB5X,kBAAkB;AA81FxB,eAAeA,kBAAkB;AAAC,IAAA4X,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}