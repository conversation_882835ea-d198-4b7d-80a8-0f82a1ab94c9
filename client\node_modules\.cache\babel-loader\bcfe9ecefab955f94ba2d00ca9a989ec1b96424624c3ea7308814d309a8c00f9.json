{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt, FaVideo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\", \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\", \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\", \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\", \"Amini unaweza na umefika nusu ya njia.\", \"Kikomo chako—ni mawazo yako tu.\", \"Mambo makuu hayatoki katika mazingira ya starehe.\", \"Ota. Tamani. Fanya.\", \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\", \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"] : [\"Education is the most powerful weapon which you can use to change the world.\", \"The beautiful thing about learning is that no one can take it away from you.\", \"Success is not final, failure is not fatal: it is the courage to continue that counts.\", \"The only way to do great work is to love what you do.\", \"Believe you can and you're halfway there.\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\", \"Success doesn't just find you. You have to go out and get it.\", \"The harder you work for something, the greater you'll feel when you achieve it.\"];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\"Karibu tena\", \"Habari za asubuhi\", \"Umefika salama\", \"Karibu sana\", \"Hujambo\", \"Habari za leo\", \"Karibu kwenye mafunzo\", \"Umekuja vizuri\", \"Karibu darasani\", \"Habari za masomo\"] : [\"Welcome back\", \"Great to see you\", \"Hello there\", \"Good to have you here\", \"Nice to see you again\", \"Ready to learn\", \"Let's get started\", \"Time to shine\", \"Here we go\", \"Ready for success\"];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome(prev => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const navigationItems = [{\n    title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n    description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n    description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n    description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n    icon: FaVideo,\n    path: '/user/video-lessons',\n    color: 'from-red-500 to-red-600',\n    hoverColor: 'from-red-600 to-red-700'\n  }, {\n    title: isKiswahili ? 'Ripoti' : 'Reports',\n    description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n    description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: isKiswahili ? 'Ujuzi' : 'Skills',\n    description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n    icon: FaStar,\n    path: '/user/skills',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 w-1/4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n              style: {\n                background: '#f0f0f0',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                width: window.innerWidth <= 480 ? '24px' : '32px',\n                height: window.innerWidth <= 480 ? '24px' : '32px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/favicon.png\",\n                alt: \"Brainwave Logo\",\n                className: \"w-full h-full object-cover\",\n                style: {\n                  objectFit: 'cover'\n                },\n                onError: e => {\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'flex';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                style: {\n                  display: 'none',\n                  fontSize: '12px'\n                },\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center justify-center\",\n              style: {\n                maxWidth: '800px',\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 w-1/4\",\n            children: user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hub-welcome\",\n          children: [welcomeMessages[currentWelcome], \", \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || (isKiswahili ? 'Mwanafunzi' : 'Student')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"QVrrehD5i6wlocOUY5BYhWKzqMg=\", false, function () {\n  return [useNavigate, useSelector, useLanguage];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "useLanguage", "ProfilePicture", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "FaVideo", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "t", "isKiswahili", "currentQuote", "setCurrentQuote", "currentWelcome", "setCurrentWelcome", "inspiringQuotes", "welcomeMessages", "interval", "setInterval", "prev", "length", "clearInterval", "welcomeInterval", "handleLogout", "localStorage", "removeItem", "success", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "header", "initial", "y", "opacity", "animate", "style", "background", "boxShadow", "width", "window", "innerWidth", "height", "src", "alt", "objectFit", "onError", "e", "target", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "div", "scale", "transition", "duration", "delay", "max<PERSON><PERSON><PERSON>", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "fontWeight", "backgroundColor", "size", "showOnlineStatus", "name", "level", "class", "firstName", "marginRight", "marginLeft", "marginTop", "map", "item", "index", "IconComponent", "onClick", "tabIndex", "role", "onKeyDown", "key", "cursor", "touchAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport './Hub.css';\nimport ProfilePicture from '../../../components/common/ProfilePicture';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt,\n  FaVideo\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  const [currentQuote, setCurrentQuote] = useState(0);\n  const [currentWelcome, setCurrentWelcome] = useState(0);\n\n\n\n\n  // Inspiring quotes that rotate\n  const inspiringQuotes = isKiswahili ? [\n    \"Elimu ni silaha yenye nguvu zaidi ambayo unaweza kuitumia kubadilisha ulimwengu.\",\n    \"Jambo zuri kuhusu kujifunza ni kwamba hakuna mtu anayeweza kuliondoa kwako.\",\n    \"Mafanikio si ya mwisho, kushindwa si kwa kufa: ni ujasiri wa kuendelea ndio muhimu.\",\n    \"Njia pekee ya kufanya kazi kubwa ni kupenda unachofanya.\",\n    \"Amini unaweza na umefika nusu ya njia.\",\n    \"Kikomo chako—ni mawazo yako tu.\",\n    \"Mambo makuu hayatoki katika mazingira ya starehe.\",\n    \"Ota. Tamani. Fanya.\",\n    \"Mafanikio hayakutafuti tu. Lazima uende ukayatafute.\",\n    \"Kadiri unavyofanya kazi kwa bidii kwa kitu, ndivyo utakavyojisikia vizuri zaidi utakapokifikia.\"\n  ] : [\n    \"Education is the most powerful weapon which you can use to change the world.\",\n    \"The beautiful thing about learning is that no one can take it away from you.\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts.\",\n    \"The only way to do great work is to love what you do.\",\n    \"Believe you can and you're halfway there.\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\",\n    \"Success doesn't just find you. You have to go out and get it.\",\n    \"The harder you work for something, the greater you'll feel when you achieve it.\"\n  ];\n\n  // Different welcome messages that rotate\n  const welcomeMessages = isKiswahili ? [\n    \"Karibu tena\",\n    \"Habari za asubuhi\",\n    \"Umefika salama\",\n    \"Karibu sana\",\n    \"Hujambo\",\n    \"Habari za leo\",\n    \"Karibu kwenye mafunzo\",\n    \"Umekuja vizuri\",\n    \"Karibu darasani\",\n    \"Habari za masomo\"\n  ] : [\n    \"Welcome back\",\n    \"Great to see you\",\n    \"Hello there\",\n    \"Good to have you here\",\n    \"Nice to see you again\",\n    \"Ready to learn\",\n    \"Let's get started\",\n    \"Time to shine\",\n    \"Here we go\",\n    \"Ready for success\"\n  ];\n\n  // Rotate quotes every 6 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 6000);\n    return () => clearInterval(interval);\n  }, [inspiringQuotes.length]);\n\n  // Rotate welcome messages every 4 seconds\n  useEffect(() => {\n    const welcomeInterval = setInterval(() => {\n      setCurrentWelcome((prev) => (prev + 1) % welcomeMessages.length);\n    }, 4000);\n    return () => clearInterval(welcomeInterval);\n  }, [welcomeMessages.length]);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n\n\n  const navigationItems = [\n    {\n      title: isKiswahili ? 'Fanya Mtihani' : 'Take Quiz',\n      description: isKiswahili ? 'Jaribu maarifa yako' : 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: isKiswahili ? 'Vifaa vya Kusoma' : 'Study Materials',\n      description: isKiswahili ? 'Vitabu, maelezo na karatasi' : 'Books, notes & papers',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: isKiswahili ? 'Masomo ya Video' : 'Video Lessons',\n      description: isKiswahili ? 'Tazama video za kielimu' : 'Watch educational videos',\n      icon: FaVideo,\n      path: '/user/video-lessons',\n      color: 'from-red-500 to-red-600',\n      hoverColor: 'from-red-600 to-red-700'\n    },\n    {\n      title: isKiswahili ? 'Ripoti' : 'Reports',\n      description: isKiswahili ? 'Fuatilia maendeleo yako' : 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: isKiswahili ? 'Orodha ya Ushindi' : 'Ranking',\n      description: isKiswahili ? 'Ona nafasi yako' : 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: isKiswahili ? 'Ujuzi' : 'Skills',\n      description: isKiswahili ? 'Ongeza ujuzi wako' : 'Enhance your skills',\n      icon: FaStar,\n      path: '/user/skills',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      {/* Header with User Profile and Notification Bell - Same as Home */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-50 shadow-lg shadow-blue-100/20\"\n      >\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\n            {/* Left section - Logo */}\n            <div className=\"flex items-center space-x-2 w-1/4\">\n              <div\n                className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                style={{\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: window.innerWidth <= 480 ? '24px' : '32px',\n                  height: window.innerWidth <= 480 ? '24px' : '32px'\n                }}\n              >\n                <img\n                  src=\"/favicon.png\"\n                  alt=\"Brainwave Logo\"\n                  className=\"w-full h-full object-cover\"\n                  style={{ objectFit: 'cover' }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }}\n                />\n                <div\n                  className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\n                  style={{\n                    display: 'none',\n                    fontSize: '12px'\n                  }}\n                >\n                  🧠\n                </div>\n              </div>\n            </div>\n\n            {/* Center Section - Brainwave Title Aligned with Welcome Message */}\n            <div className=\"flex-1 flex justify-center\">\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"relative group flex items-center justify-center\"\n                style={{\n                  maxWidth: '800px',\n                  width: '100%'\n                }}\n              >\n                {/* Amazing Animated Brainwave Text */}\n                <div className=\"relative brainwave-container\">\n                  <h1 className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black tracking-tight relative z-10 select-none\"\n                      style={{\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                        letterSpacing: '-0.02em'\n                      }}>\n                    {/* Brain - with amazing effects */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      }}\n                    >\n                      Brain\n\n                      {/* Electric spark */}\n                      <motion.div\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        }}\n                        transition={{\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        }}\n                        style={{\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }}\n                      />\n                    </motion.span>\n\n                    {/* Wave - with flowing effects (no space) */}\n                    <motion.span\n                      className=\"relative inline-block\"\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\n                      animate={{\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\n                        ]\n                      }}\n                      transition={{\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      }}\n                      whileHover={{\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: { duration: 0.3 }\n                      }}\n                      style={{\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      }}\n                    >\n                      wave\n\n                      {/* Wave particle */}\n                      <motion.div\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\n                        animate={{\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        }}\n                        transition={{\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        }}\n                        style={{\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }}\n                      />\n                    </motion.span>\n                  </h1>\n\n                  {/* Glowing underline effect */}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\n                    initial={{ width: 0, opacity: 0 }}\n                    animate={{\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: [\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\n                      ]\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    }}\n                    style={{\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }}\n                  />\n                </div>\n\n                {/* Modern Glow Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\n              </motion.div>\n            </div>\n\n            {/* Right Section - User Profile */}\n            <div className=\"flex items-center justify-end space-x-2 w-1/4\">\n              {user && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 }}\n                  className=\"flex items-center space-x-2 group\"\n                >\n                  {/* Profile Picture with Online Status */}\n                  <ProfilePicture\n                    user={user}\n                    size=\"sm\"\n                    showOnlineStatus={true}\n                    style={{\n                      width: '32px',\n                      height: '32px'\n                    }}\n                  />\n\n                  {/* User Name and Class */}\n                  <div className=\"hidden sm:block text-right\">\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\n                      {user?.name || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"hub-content\">\n        <div className=\"hub-header\">\n          <h1 className=\"hub-welcome\">\n            {welcomeMessages[currentWelcome]}, {user?.firstName || user?.name || (isKiswahili ? 'Mwanafunzi' : 'Student')}\n          </h1>\n          <p className=\"hub-subtitle\">\n            {isKiswahili ? 'Chagua njia yako ya kujifunza hapa chini' : 'Choose your learning path below'}\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAO,WAAW;AAClB,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGzB,WAAW,CAAC,CAAC;EACxC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAKvD;EACA,MAAMoC,eAAe,GAAGL,WAAW,GAAG,CACpC,kFAAkF,EAClF,6EAA6E,EAC7E,qFAAqF,EACrF,0DAA0D,EAC1D,wCAAwC,EACxC,iCAAiC,EACjC,mDAAmD,EACnD,qBAAqB,EACrB,sDAAsD,EACtD,iGAAiG,CAClG,GAAG,CACF,8EAA8E,EAC9E,8EAA8E,EAC9E,wFAAwF,EACxF,uDAAuD,EACvD,2CAA2C,EAC3C,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,EAC3B,+DAA+D,EAC/D,iFAAiF,CAClF;;EAED;EACA,MAAMM,eAAe,GAAGN,WAAW,GAAG,CACpC,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,CACnB,GAAG,CACF,cAAc,EACd,kBAAkB,EAClB,aAAa,EACb,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,mBAAmB,CACpB;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMqC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCN,eAAe,CAAEO,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIJ,eAAe,CAACK,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,eAAe,CAACK,MAAM,CAAC,CAAC;;EAE5B;EACAxC,SAAS,CAAC,MAAM;IACd,MAAM0C,eAAe,GAAGJ,WAAW,CAAC,MAAM;MACxCJ,iBAAiB,CAAEK,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACI,MAAM,CAAC;IAClE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACN,eAAe,CAACI,MAAM,CAAC,CAAC;;EAE5B;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAzC,OAAO,CAAC0C,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACApB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAID,MAAMqB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAElB,WAAW,GAAG,eAAe,GAAG,WAAW;IAClDmB,WAAW,EAAEnB,WAAW,GAAG,qBAAqB,GAAG,qBAAqB;IACxEoB,IAAI,EAAE1C,gBAAgB;IACtB2C,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAC3DmB,WAAW,EAAEnB,WAAW,GAAG,6BAA6B,GAAG,uBAAuB;IAClFoB,IAAI,EAAEzC,MAAM;IACZ0C,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,iBAAiB,GAAG,eAAe;IACxDmB,WAAW,EAAEnB,WAAW,GAAG,yBAAyB,GAAG,0BAA0B;IACjFoB,IAAI,EAAE7B,OAAO;IACb8B,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,yBAAyB;IAChCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,QAAQ,GAAG,SAAS;IACzCmB,WAAW,EAAEnB,WAAW,GAAG,yBAAyB,GAAG,qBAAqB;IAC5EoB,IAAI,EAAExC,WAAW;IACjByC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,mBAAmB,GAAG,SAAS;IACpDmB,WAAW,EAAEnB,WAAW,GAAG,iBAAiB,GAAG,mBAAmB;IAClEoB,IAAI,EAAElC,QAAQ;IACdmC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAElB,WAAW,GAAG,OAAO,GAAG,QAAQ;IACvCmB,WAAW,EAAEnB,WAAW,GAAG,mBAAmB,GAAG,qBAAqB;IACtEoB,IAAI,EAAEjC,MAAM;IACZkC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhC,OAAA,CAACpB,MAAM,CAACqD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5KhC,OAAA;QAAK+B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDhC,OAAA;UAAK+B,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7EhC,OAAA;YAAK+B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDhC,OAAA;cACE+B,SAAS,EAAC,gEAAgE;cAC1EO,KAAK,EAAE;gBACLC,UAAU,EAAE,SAAS;gBACrBC,SAAS,EAAE,4BAA4B;gBACvCC,KAAK,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDC,MAAM,EAAEF,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C,CAAE;cAAAX,QAAA,gBAEFhC,OAAA;gBACE6C,GAAG,EAAC,cAAc;gBAClBC,GAAG,EAAC,gBAAgB;gBACpBf,SAAS,EAAC,4BAA4B;gBACtCO,KAAK,EAAE;kBAAES,SAAS,EAAE;gBAAQ,CAAE;gBAC9BC,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACZ,KAAK,CAACa,OAAO,GAAG,MAAM;kBAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACd,KAAK,CAACa,OAAO,GAAG,MAAM;gBAC7C;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxD,OAAA;gBACE+B,SAAS,EAAC,gHAAgH;gBAC1HO,KAAK,EAAE;kBACLa,OAAO,EAAE,MAAM;kBACfM,QAAQ,EAAE;gBACZ,CAAE;gBAAAzB,QAAA,EACH;cAED;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAK+B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzChC,OAAA,CAACpB,MAAM,CAAC8E,GAAG;cACTxB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAI,CAAE;cACpCtB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1C/B,SAAS,EAAC,iDAAiD;cAC3DO,KAAK,EAAE;gBACLyB,QAAQ,EAAE,OAAO;gBACjBtB,KAAK,EAAE;cACT,CAAE;cAAAT,QAAA,gBAGFhC,OAAA;gBAAK+B,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3ChC,OAAA;kBAAI+B,SAAS,EAAC,gGAAgG;kBAC1GO,KAAK,EAAE;oBACL0B,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAAjC,QAAA,gBAEJhC,OAAA,CAACpB,MAAM,CAACsF,IAAI;oBACVnC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE+B,CAAC,EAAE,CAAC,EAAE;sBAAER,KAAK,EAAE;oBAAI,CAAE;oBAC5CtB,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV+B,CAAC,EAAE,CAAC;sBACJR,KAAK,EAAE,CAAC;sBACRS,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFR,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVM,UAAU,EAAE;wBACVP,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVb,KAAK,EAAE,GAAG;sBACVc,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBb,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFvB,KAAK,EAAE;sBACLT,KAAK,EAAE,SAAS;sBAChB6C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAApC,QAAA,GACH,OAGC,eACAhC,OAAA,CAACpB,MAAM,CAAC8E,GAAG;sBACT3B,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBuB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBgB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFf,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACFxB,KAAK,EAAE;wBACLqC,eAAe,EAAE,SAAS;wBAC1BnC,SAAS,EAAE;sBACb;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdxD,OAAA,CAACpB,MAAM,CAACsF,IAAI;oBACVnC,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE+B,CAAC,EAAE,EAAE;sBAAER,KAAK,EAAE;oBAAI,CAAE;oBAC3CtB,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV+B,CAAC,EAAE,CAAC;sBACJR,KAAK,EAAE,CAAC;sBACRxB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnBiC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFR,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACV3B,CAAC,EAAE;wBACD0B,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVP,QAAQ,EAAE,GAAG;wBACbQ,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVb,KAAK,EAAE,GAAG;sBACVc,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBb,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFvB,KAAK,EAAE;sBACLT,KAAK,EAAE,SAAS;sBAChB6C,UAAU,EAAE,KAAK;sBACjBN,UAAU,EAAE;oBACd,CAAE;oBAAApC,QAAA,GACH,MAGC,eACAhC,OAAA,CAACpB,MAAM,CAAC8E,GAAG;sBACT3B,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB+B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACdhC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBwC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFf,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXQ,MAAM,EAAEC,QAAQ;wBAChBR,KAAK,EAAE;sBACT,CAAE;sBACFxB,KAAK,EAAE;wBACLqC,eAAe,EAAE,SAAS;wBAC1BnC,SAAS,EAAE;sBACb;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLxD,OAAA,CAACpB,MAAM,CAAC8E,GAAG;kBACT3B,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEO,KAAK,EAAE,CAAC;oBAAEL,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPI,KAAK,EAAE,MAAM;oBACbL,OAAO,EAAE,CAAC;oBACVI,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFoB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVtB,SAAS,EAAE;sBACTqB,QAAQ,EAAE,CAAC;sBACXQ,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFjC,KAAK,EAAE;oBACLC,UAAU,EAAE,mDAAmD;oBAC/DC,SAAS,EAAE;kBACb;gBAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxD,OAAA;gBAAK+B,SAAS,EAAC;cAAyK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNxD,OAAA;YAAK+B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC3D5B,IAAI,iBACHJ,OAAA,CAACpB,MAAM,CAAC8E,GAAG;cACTxB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAI,CAAE;cACpCtB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEuB,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1C/B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7ChC,OAAA,CAACjB,cAAc;gBACbqB,IAAI,EAAEA,IAAK;gBACXwE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvBvC,KAAK,EAAE;kBACLG,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE;gBACV;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFxD,OAAA;gBAAK+B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzChC,OAAA;kBAAK+B,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,EACnH,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,IAAI,KAAI;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNxD,OAAA;kBAAK+B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAC5F,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,KAAK,MAAK,SAAS,GAAI,SAAQ3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,KAAM,EAAC,GAAG5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBxD,OAAA;MAAK+B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhC,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhC,OAAA;UAAI+B,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBnB,eAAe,CAACH,cAAc,CAAC,EAAC,IAAE,EAAC,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,SAAS,MAAI7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,IAAI,MAAKvE,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACLxD,OAAA;UAAG+B,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBzB,WAAW,GAAG,0CAA0C,GAAG;QAAiC;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAEJxD,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA,CAACN,MAAM;YAAC4C,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAEqD,WAAW,EAAE;YAAS;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC5C,eAAe,CAACJ,YAAY,CAAC,EAAC,IAChC,eAAAR,OAAA,CAACN,MAAM;YAAC4C,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAEsD,UAAU,EAAE;YAAS;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DxD,OAAA;YAAKsC,KAAK,EAAE;cAAEmB,QAAQ,EAAE,UAAU;cAAE5B,KAAK,EAAE,SAAS;cAAEuD,SAAS,EAAE;YAAS,CAAE;YAAApD,QAAA,EAAC;UAE7E;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAMNxD,OAAA;QAAK+B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChC,OAAA;UAAK+B,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAAC6D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC3D,IAAI;YAC/B,oBACE3B,OAAA,CAACpB,MAAM,CAAC8E,GAAG;cAETxB,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9ByB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEyB,KAAK,GAAG;cAAI,CAAE;cAClDxD,SAAS,EAAG,kBAAiBuD,IAAI,CAACxD,UAAW,IAAGwD,IAAI,CAACzD,KAAM,EAAE;cAC7D4D,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACmF,IAAI,CAAC1D,IAAI,CAAE;cACnC8D,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAG3C,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAAC4C,GAAG,KAAK,OAAO,IAAI5C,CAAC,CAAC4C,GAAG,KAAK,GAAG,EAAE;kBACtC1F,QAAQ,CAACmF,IAAI,CAAC1D,IAAI,CAAC;gBACrB;cACF,CAAE;cACFU,KAAK,EAAE;gBACLwD,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAA/D,QAAA,gBAIFhC,OAAA;gBAAK+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BhC,OAAA,CAACwF,aAAa;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENxD,OAAA;gBAAI+B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC3BsD,IAAI,CAAC7D;cAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAELxD,OAAA;gBAAG+B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChCsD,IAAI,CAAC5D;cAAW;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA,GA9BC8B,IAAI,CAAC7D,KAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+BL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxD,OAAA,CAACpB,MAAM,CAAC8E,GAAG;UACTxB,OAAO,EAAE;YAAEE,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBwB,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1C/B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjChC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChC,OAAA,CAACR,eAAe;cAACuC,SAAS,EAAC;YAAuC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrExD,OAAA;cAAAgC,QAAA,EAAM;YAAkC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CxD,OAAA,CAACL,QAAQ;cAACoC,SAAS,EAAC;YAAuC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAreID,GAAG;EAAA,QACUvB,WAAW,EACXC,WAAW,EACDG,WAAW;AAAA;AAAAkH,EAAA,GAHlC/F,GAAG;AAueT,eAAeA,GAAG;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}