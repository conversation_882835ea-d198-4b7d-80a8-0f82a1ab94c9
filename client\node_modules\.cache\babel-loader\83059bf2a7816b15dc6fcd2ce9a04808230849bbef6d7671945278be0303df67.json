{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbMenu2, TbX } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\nimport Logo from '../../../assets/logo.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-white to-blue-50 text-gray-900 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex justify-between items-center px-6 py-4 shadow-md bg-white sticky top-0 z-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://flagcdn.com/w40/tz.png\",\n            alt: \"Tanzania Flag\",\n            className: \"w-full h-full object-cover\",\n            onError: e => {\n              e.target.style.display = 'none';\n              e.target.nextSibling.style.display = 'flex';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\",\n            children: \"TZ\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-blue-700\",\n          children: \"Brainwave\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-x-6 hidden md:flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Study Materials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"px-4 py-2 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"px-4 py-2 bg-green-500 text-white rounded-full hover:bg-green-600\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          className: \"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\",\n          children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 63\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden bg-white border-b border-gray-200 px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Study Materials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"text-gray-700 hover:text-green-600\",\n          children: \"Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col space-y-2 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"px-4 py-2 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50 text-center\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"px-4 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 text-center\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"text-center px-6 py-20 max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm font-medium inline-block px-4 py-1 mb-4 bg-yellow-300 text-black rounded-full\",\n        children: \"\\uD83C\\uDF93 Welcome to Brainwave \\u2014 Study Smarter in Tanzania\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-5xl font-extrabold text-blue-800 leading-tight mb-4\",\n        children: \"Learn Anywhere, Anytime\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-lg text-gray-700 mb-6\",\n        children: \"Explore courses, study materials, quizzes, and boost your academic success with Tanzania's leading learning platform.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"px-6 py-3 bg-green-500 text-white rounded-full shadow hover:bg-green-600 inline-block\",\n          children: \"Get Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-6 py-3 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50\",\n          children: \"Explore\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"grid grid-cols-2 sm:grid-cols-4 gap-6 px-6 py-12 text-center max-w-6xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-green-600\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n            end: 10000,\n            duration: 2000\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), \"+\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-blue-600\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n            end: 50,\n            duration: 2000\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), \"+\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-yellow-500\",\n          children: \"99%\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Success Rate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-black\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatedCounter, {\n            end: 52,\n            duration: 2000\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), \"+\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Study Materials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"px-6 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-hidden rounded-2xl shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://images.unsplash.com/photo-1588072432836-e10032774350?auto=format&fit=crop&w=1500&q=80\",\n          alt: \"Students studying\",\n          className: \"w-full h-80 object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-black text-white text-center py-6 mt-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center space-x-3 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-6 rounded-sm overflow-hidden border border-gray-300\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://flagcdn.com/w40/tz.png\",\n            alt: \"Tanzania Flag\",\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold\",\n          children: \"Brainwave Tanzania\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA9 2025 Brainwave Tanzania. All Rights Reserved.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"r9x0gdSUQQp+9acYbkNrTdTBNAM=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "motion", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbMenu2", "TbX", "message", "useSelector", "contactUs", "Animated<PERSON>ounter", "Logo", "jsxDEV", "_jsxDEV", "Home", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "user", "state", "navigate", "className", "children", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "to", "onClick", "end", "duration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\r\nimport Logo from '../../../assets/logo.png';\r\n\r\n\r\nconst Home = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  return (\r\n    <div className=\"bg-gradient-to-br from-white to-blue-50 text-gray-900 min-h-screen\">\r\n      {/* Navbar */}\r\n      <nav className=\"flex justify-between items-center px-6 py-4 shadow-md bg-white sticky top-0 z-50\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* Tanzania Flag */}\r\n          <div className=\"w-8 h-6 rounded-sm overflow-hidden border border-gray-300 shadow-sm\">\r\n            <img\r\n              src=\"https://flagcdn.com/w40/tz.png\"\r\n              alt=\"Tanzania Flag\"\r\n              className=\"w-full h-full object-cover\"\r\n              onError={(e) => {\r\n                e.target.style.display = 'none';\r\n                e.target.nextSibling.style.display = 'flex';\r\n              }}\r\n            />\r\n            <div className=\"w-full h-full bg-gradient-to-br from-green-500 via-yellow-400 to-blue-600 hidden items-center justify-center text-white text-xs font-bold\">\r\n              TZ\r\n            </div>\r\n          </div>\r\n          <div className=\"text-2xl font-bold text-blue-700\">Brainwave</div>\r\n        </div>\r\n\r\n        <div className=\"space-x-6 hidden md:flex\">\r\n          <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Home</a>\r\n          <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Courses</a>\r\n          <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Study Materials</a>\r\n          <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Contact</a>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"hidden md:flex space-x-4\">\r\n            <Link\r\n              to=\"/login\"\r\n              className=\"px-4 py-2 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50\"\r\n            >\r\n              Login\r\n            </Link>\r\n            <Link\r\n              to=\"/register\"\r\n              className=\"px-4 py-2 bg-green-500 text-white rounded-full hover:bg-green-600\"\r\n            >\r\n              Register\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Mobile Menu Button */}\r\n          <button\r\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n            className=\"md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100\"\r\n          >\r\n            {isMobileMenuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n          </button>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Mobile Menu */}\r\n      {isMobileMenuOpen && (\r\n        <div className=\"md:hidden bg-white border-b border-gray-200 px-6 py-4\">\r\n          <div className=\"flex flex-col space-y-4\">\r\n            <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Home</a>\r\n            <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Courses</a>\r\n            <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Study Materials</a>\r\n            <a href=\"#\" className=\"text-gray-700 hover:text-green-600\">Contact</a>\r\n            <div className=\"flex flex-col space-y-2 pt-4 border-t border-gray-200\">\r\n              <Link\r\n                to=\"/login\"\r\n                className=\"px-4 py-2 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50 text-center\"\r\n              >\r\n                Login\r\n              </Link>\r\n              <Link\r\n                to=\"/register\"\r\n                className=\"px-4 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 text-center\"\r\n              >\r\n                Register\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      {/* Hero Section */}\r\n      <section className=\"text-center px-6 py-20 max-w-4xl mx-auto\">\r\n        <div className=\"text-sm font-medium inline-block px-4 py-1 mb-4 bg-yellow-300 text-black rounded-full\">\r\n          🎓 Welcome to Brainwave — Study Smarter in Tanzania\r\n        </div>\r\n        <h1 className=\"text-5xl font-extrabold text-blue-800 leading-tight mb-4\">\r\n          Learn Anywhere, Anytime\r\n        </h1>\r\n        <p className=\"text-lg text-gray-700 mb-6\">\r\n          Explore courses, study materials, quizzes, and boost your academic success with Tanzania's leading learning platform.\r\n        </p>\r\n        <div className=\"space-x-4\">\r\n          <Link\r\n            to=\"/register\"\r\n            className=\"px-6 py-3 bg-green-500 text-white rounded-full shadow hover:bg-green-600 inline-block\"\r\n          >\r\n            Get Started\r\n          </Link>\r\n          <button className=\"px-6 py-3 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50\">\r\n            Explore\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"grid grid-cols-2 sm:grid-cols-4 gap-6 px-6 py-12 text-center max-w-6xl mx-auto\">\r\n        <div className=\"bg-white rounded-xl shadow p-6\">\r\n          <p className=\"text-2xl font-bold text-green-600\">\r\n            <AnimatedCounter end={10000} duration={2000} />+\r\n          </p>\r\n          <p className=\"text-sm text-gray-600\">Students</p>\r\n        </div>\r\n        <div className=\"bg-white rounded-xl shadow p-6\">\r\n          <p className=\"text-2xl font-bold text-blue-600\">\r\n            <AnimatedCounter end={50} duration={2000} />+\r\n          </p>\r\n          <p className=\"text-sm text-gray-600\">Courses</p>\r\n        </div>\r\n        <div className=\"bg-white rounded-xl shadow p-6\">\r\n          <p className=\"text-2xl font-bold text-yellow-500\">99%</p>\r\n          <p className=\"text-sm text-gray-600\">Success Rate</p>\r\n        </div>\r\n        <div className=\"bg-white rounded-xl shadow p-6\">\r\n          <p className=\"text-2xl font-bold text-black\">\r\n            <AnimatedCounter end={52} duration={2000} />+\r\n          </p>\r\n          <p className=\"text-sm text-gray-600\">Study Materials</p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Banner Image Section */}\r\n      <section className=\"px-6 py-12\">\r\n        <div className=\"overflow-hidden rounded-2xl shadow-lg\">\r\n          <img\r\n            src=\"https://images.unsplash.com/photo-1588072432836-e10032774350?auto=format&fit=crop&w=1500&q=80\"\r\n            alt=\"Students studying\"\r\n            className=\"w-full h-80 object-cover\"\r\n          />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-black text-white text-center py-6 mt-10\">\r\n        <div className=\"flex items-center justify-center space-x-3 mb-4\">\r\n          {/* Tanzania Flag in Footer */}\r\n          <div className=\"w-8 h-6 rounded-sm overflow-hidden border border-gray-300\">\r\n            <img\r\n              src=\"https://flagcdn.com/w40/tz.png\"\r\n              alt=\"Tanzania Flag\"\r\n              className=\"w-full h-full object-cover\"\r\n            />\r\n          </div>\r\n          <span className=\"text-lg font-bold\">Brainwave Tanzania</span>\r\n        </div>\r\n        <p>&copy; 2025 Brainwave Tanzania. All Rights Reserved.</p>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM;IAAEsB;EAAK,CAAC,GAAGV,WAAW,CAAEW,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,oBACEe,OAAA;IAAKQ,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBAEjFT,OAAA;MAAKQ,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAC/FT,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CT,OAAA;UAAKQ,SAAS,EAAC,qEAAqE;UAAAC,QAAA,gBAClFT,OAAA;YACEU,GAAG,EAAC,gCAAgC;YACpCC,GAAG,EAAC,eAAe;YACnBH,SAAS,EAAC,4BAA4B;YACtCI,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;cAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;YAC7C;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFrB,OAAA;YAAKQ,SAAS,EAAC,2IAA2I;YAAAC,QAAA,EAAC;UAE3J;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrB,OAAA;UAAKQ,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENrB,OAAA;QAAKQ,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCT,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9ErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAENrB,OAAA;QAAKQ,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CT,OAAA;UAAKQ,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCT,OAAA,CAAChB,IAAI;YACHuC,EAAE,EAAC,QAAQ;YACXf,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAAChB,IAAI;YACHuC,EAAE,EAAC,WAAW;YACdf,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC9E;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNrB,OAAA;UACEwB,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtDK,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAEnEN,gBAAgB,gBAAGH,OAAA,CAACP,GAAG;YAACe,SAAS,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrB,OAAA,CAACR,OAAO;YAACgB,SAAS,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlB,gBAAgB,iBACfH,OAAA;MAAKQ,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpET,OAAA;QAAKQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCT,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9ErB,OAAA;UAAGsB,IAAI,EAAC,GAAG;UAACd,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtErB,OAAA;UAAKQ,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACpET,OAAA,CAAChB,IAAI;YACHuC,EAAE,EAAC,QAAQ;YACXf,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EACrG;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAAChB,IAAI;YACHuC,EAAE,EAAC,WAAW;YACdf,SAAS,EAAC,+EAA+E;YAAAC,QAAA,EAC1F;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrB,OAAA;MAASQ,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAC3DT,OAAA;QAAKQ,SAAS,EAAC,uFAAuF;QAAAC,QAAA,EAAC;MAEvG;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNrB,OAAA;QAAIQ,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EAAC;MAEzE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrB,OAAA;QAAGQ,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrB,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBT,OAAA,CAAChB,IAAI;UACHuC,EAAE,EAAC,WAAW;UACdf,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EAClG;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPrB,OAAA;UAAQQ,SAAS,EAAC,8EAA8E;UAAAC,QAAA,EAAC;QAEjG;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASQ,SAAS,EAAC,gFAAgF;MAAAC,QAAA,gBACjGT,OAAA;QAAKQ,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CT,OAAA;UAAGQ,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CT,OAAA,CAACH,eAAe;YAAC4B,GAAG,EAAE,KAAM;YAACC,QAAQ,EAAE;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KACjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA;UAAGQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAQ;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACNrB,OAAA;QAAKQ,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CT,OAAA;UAAGQ,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC7CT,OAAA,CAACH,eAAe;YAAC4B,GAAG,EAAE,EAAG;YAACC,QAAQ,EAAE;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAC9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA;UAAGQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACNrB,OAAA;QAAKQ,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CT,OAAA;UAAGQ,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAG;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzDrB,OAAA;UAAGQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACNrB,OAAA;QAAKQ,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CT,OAAA;UAAGQ,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC1CT,OAAA,CAACH,eAAe;YAAC4B,GAAG,EAAE,EAAG;YAACC,QAAQ,EAAE;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAC9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA;UAAGQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAe;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASQ,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC7BT,OAAA;QAAKQ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDT,OAAA;UACEU,GAAG,EAAC,+FAA+F;UACnGC,GAAG,EAAC,mBAAmB;UACvBH,SAAS,EAAC;QAA0B;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAAQQ,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBAC5DT,OAAA;QAAKQ,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE9DT,OAAA;UAAKQ,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxET,OAAA;YACEU,GAAG,EAAC,gCAAgC;YACpCC,GAAG,EAAC,eAAe;YACnBH,SAAS,EAAC;UAA4B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA;UAAMQ,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAkB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNrB,OAAA;QAAAS,QAAA,EAAG;MAAoD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnB,EAAA,CApKID,IAAI;EAAA,QAESN,WAAW,EACXV,WAAW;AAAA;AAAA0C,EAAA,GAHxB1B,IAAI;AAsKV,eAAeA,IAAI;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}