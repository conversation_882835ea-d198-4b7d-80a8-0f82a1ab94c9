{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowRight, TbBrain, TbBook, TbTrophy, TbUsers, TbSchool, TbStar, TbLogin, TbMenu2, TbX } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n\n  // Refs for smooth scrolling\n  const featuresRef = useRef(null);\n  const coursesRef = useRef(null);\n  const successStoriesRef = useRef(null);\n  const contactRef = useRef(null);\n  const scrollToSection = ref => {\n    var _ref$current;\n    (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        message.error(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      message.error(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home relative min-h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(reviewsSectionRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(contactUsRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), user && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${user === null || user === void 0 ? void 0 : user.class}` : user === null || user === void 0 ? void 0 : user.class\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          style: {\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            backgroundSize: '60px 60px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 1.2,\n                delay: 0.2,\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15\n              },\n              className: \"relative mb-2 sm:mb-3 md:mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 -m-8 rounded-3xl\",\n                animate: {\n                  background: ['radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)', 'radial-gradient(ellipse 120% 80% at 60% 40%, rgba(139, 92, 246, 0.04), rgba(59, 130, 246, 0.03), transparent)', 'radial-gradient(ellipse 120% 80% at 40% 60%, rgba(16, 185, 129, 0.03), rgba(139, 92, 246, 0.02), transparent)', 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)']\n                },\n                transition: {\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-1 h-1 rounded-full\",\n                style: {\n                  background: i % 2 === 0 ? '#3b82f6' : '#10b981',\n                  left: `${20 + i * 15}%`,\n                  top: `${10 + i * 10}%`\n                },\n                animate: {\n                  y: [0, -20, 0],\n                  x: [0, 10, -10, 0],\n                  opacity: [0.3, 1, 0.3],\n                  scale: [0.5, 1.2, 0.5]\n                },\n                transition: {\n                  duration: 4 + i * 0.5,\n                  repeat: Infinity,\n                  delay: i * 0.8,\n                  ease: \"easeInOut\"\n                }\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)), /*#__PURE__*/_jsxDEV(motion.h1, {\n                className: \"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\",\n                style: {\n                  fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                  letterSpacing: '-0.02em',\n                  lineHeight: '1.1'\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block mr-4\",\n                  initial: {\n                    opacity: 0,\n                    x: -80,\n                    scale: 0.7,\n                    rotateY: -45\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    rotateY: 0,\n                    textShadow: [\"0 0 30px rgba(59, 130, 246, 0.6)\", \"0 0 60px rgba(59, 130, 246, 0.9)\", \"0 0 30px rgba(59, 130, 246, 0.6)\"]\n                  },\n                  transition: {\n                    duration: 1.8,\n                    delay: 0.4,\n                    type: \"spring\",\n                    stiffness: 80,\n                    damping: 12,\n                    textShadow: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.15,\n                    rotate: [0, -2, 2, 0],\n                    y: [-5, 0],\n                    transition: {\n                      duration: 0.6,\n                      type: \"spring\",\n                      stiffness: 300\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontWeight: '900',\n                    textShadow: '0 0 30px rgba(59, 130, 246, 0.6)',\n                    filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3))'\n                  },\n                  children: [\"Study\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -top-3 -right-3 w-4 h-4 rounded-full\",\n                    animate: {\n                      scale: [0.6, 1.4, 0.6],\n                      opacity: [0.4, 1, 0.4],\n                      rotate: [0, 360],\n                      background: ['linear-gradient(45deg, #3b82f6, #1d4ed8)', 'linear-gradient(45deg, #8b5cf6, #7c3aed)', 'linear-gradient(45deg, #3b82f6, #1d4ed8)']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      delay: 1,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',\n                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -bottom-2 -left-2 w-3 h-3 rounded-full\",\n                    animate: {\n                      scale: [0.8, 1.6, 0.8],\n                      opacity: [0.5, 1, 0.5],\n                      x: [0, 8, 0],\n                      y: [0, -6, 0],\n                      rotate: [0, -180, -360]\n                    },\n                    transition: {\n                      duration: 3.5,\n                      repeat: Infinity,\n                      delay: 0.7,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(135deg, #60a5fa, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(96, 165, 250, 0.9)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute inset-0 rounded-lg\",\n                    animate: {\n                      background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)']\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity,\n                      delay: 2,\n                      ease: \"easeInOut\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"relative inline-block\",\n                  initial: {\n                    opacity: 0,\n                    x: 80,\n                    scale: 0.7,\n                    rotateY: 45\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    scale: 1,\n                    rotateY: 0,\n                    y: [0, -4, 0, 4, 0],\n                    textShadow: [\"0 0 30px rgba(16, 185, 129, 0.6)\", \"0 0 60px rgba(16, 185, 129, 0.9)\", \"0 0 30px rgba(16, 185, 129, 0.6)\"]\n                  },\n                  transition: {\n                    duration: 1.8,\n                    delay: 0.8,\n                    type: \"spring\",\n                    stiffness: 80,\n                    damping: 12,\n                    y: {\n                      duration: 5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    textShadow: {\n                      duration: 4.5,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.15,\n                    rotate: [0, 2, -2, 0],\n                    y: [-8, 0],\n                    transition: {\n                      duration: 0.6,\n                      type: \"spring\",\n                      stiffness: 300\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    fontWeight: '900',\n                    textShadow: '0 0 30px rgba(16, 185, 129, 0.6)',\n                    filter: 'drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3))'\n                  },\n                  children: [\"Smarter\", /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute top-0 left-0 w-3 h-3 rounded-full\",\n                    animate: {\n                      opacity: [0, 1, 0.8, 1, 0],\n                      x: [0, 120, 240, 360, 240, 120, 0],\n                      y: [0, -15, 0, 15, 30, 15, 0],\n                      scale: [0.5, 1.2, 0.8, 1.5, 0.5],\n                      background: ['linear-gradient(45deg, #10b981, #34d399)', 'linear-gradient(45deg, #34d399, #6ee7b7)', 'linear-gradient(45deg, #6ee7b7, #10b981)', 'linear-gradient(45deg, #10b981, #34d399)']\n                    },\n                    transition: {\n                      duration: 6,\n                      repeat: Infinity,\n                      delay: 2.5,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: 'linear-gradient(45deg, #10b981, #34d399)',\n                      boxShadow: '0 0 20px rgba(16, 185, 129, 0.8)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 21\n                  }, this), [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute w-1.5 h-1.5 rounded-full\",\n                    animate: {\n                      opacity: [0, 0.8, 0],\n                      x: [0, 80 + i * 20, 160 + i * 40, 240 + i * 20, 160 + i * 40, 80 + i * 20, 0],\n                      y: [0, -8 - i * 2, 0, 8 + i * 2, 16 + i * 4, 8 + i * 2, 0],\n                      scale: [0.3, 1, 0.6, 1.2, 0.3]\n                    },\n                    transition: {\n                      duration: 6,\n                      repeat: Infinity,\n                      delay: 3 + i * 0.3,\n                      ease: \"easeInOut\"\n                    },\n                    style: {\n                      background: `linear-gradient(45deg, #34d399, #6ee7b7)`,\n                      boxShadow: '0 0 10px rgba(52, 211, 153, 0.6)'\n                    }\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this)), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute inset-0 rounded-lg\",\n                    animate: {\n                      background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)']\n                    },\n                    transition: {\n                      duration: 4,\n                      repeat: Infinity,\n                      delay: 3.5,\n                      ease: \"easeInOut\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\",\n                initial: {\n                  width: 0,\n                  opacity: 0\n                },\n                animate: {\n                  width: '80%',\n                  opacity: 1,\n                  boxShadow: ['0 0 20px rgba(16, 185, 129, 0.5)', '0 0 40px rgba(59, 130, 246, 0.8)', '0 0 20px rgba(16, 185, 129, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  delay: 1.5,\n                  boxShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                style: {\n                  background: 'linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6, #3b82f6)',\n                  boxShadow: '0 0 30px rgba(16, 185, 129, 0.6)'\n                },\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute inset-0 rounded-full\",\n                  animate: {\n                    background: ['linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)', 'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'],\n                    x: ['-100%', '100%']\n                  },\n                  transition: {\n                    background: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    x: {\n                      duration: 3,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 2\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), [...Array(4)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute top-1/2 left-1/2 rounded-full\",\n                style: {\n                  width: `${16 + i * 4}px`,\n                  height: `${16 + i * 4}px`,\n                  background: i % 2 === 0 ? 'linear-gradient(45deg, #3b82f6, #8b5cf6)' : 'linear-gradient(45deg, #10b981, #34d399)',\n                  boxShadow: i % 2 === 0 ? '0 0 20px rgba(59, 130, 246, 0.8)' : '0 0 20px rgba(16, 185, 129, 0.8)',\n                  transform: 'translate(-50%, -50%)'\n                },\n                animate: {\n                  rotate: i % 2 === 0 ? 360 : -360,\n                  x: [0, Math.cos(i * Math.PI / 2) * (120 + i * 20), 0, -Math.cos(i * Math.PI / 2) * (120 + i * 20), 0],\n                  y: [0, -Math.sin(i * Math.PI / 2) * (60 + i * 15), -(120 + i * 20), -Math.sin(i * Math.PI / 2) * (60 + i * 15), 0],\n                  opacity: [0.4, 1, 0.6, 1, 0.4],\n                  scale: [0.8, 1.2, 1, 1.3, 0.8]\n                },\n                transition: {\n                  duration: 10 + i * 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: i * 0.5\n                }\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)), [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-2 h-2 rounded-full\",\n                style: {\n                  background: `linear-gradient(45deg, ${['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]}, #ffffff)`,\n                  left: `${10 + i * 12}%`,\n                  top: `${15 + i % 3 * 25}%`,\n                  boxShadow: `0 0 10px ${['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]}`\n                },\n                animate: {\n                  y: [0, -30, 0],\n                  x: [0, Math.sin(i) * 20, 0],\n                  opacity: [0, 1, 0],\n                  scale: [0.3, 1, 0.3],\n                  rotate: [0, 180, 360]\n                },\n                transition: {\n                  duration: 3 + i * 0.3,\n                  repeat: Infinity,\n                  delay: i * 0.4,\n                  ease: \"easeInOut\"\n                }\n              }, `sparkle-${i}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.8\n              },\n              className: \"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\",\n              style: {\n                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\n                boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\n                border: '2px solid #FFD700'\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\",\n                animate: {\n                  x: ['-100%', '100%']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5 mr-2 text-orange-800 relative z-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-orange-900 relative z-10 font-black\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 rounded-full\",\n                animate: {\n                  boxShadow: ['0 0 20px rgba(255, 215, 0, 0.5)', '0 0 40px rgba(255, 215, 0, 0.8)', '0 0 20px rgba(255, 215, 0, 0.5)']\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.9\n              },\n              className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\",\n              children: !user ? /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/register\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbUserPlus, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 892,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Register Now\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 893,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 891,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    className: \"w-full sm:w-auto\",\n                    children: /*#__PURE__*/_jsxDEV(motion.button, {\n                      className: \"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\",\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-center space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(TbLogin, {\n                          className: \"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 905,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm xs:text-sm sm:text-base\",\n                          children: \"Login\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 906,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 904,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/user/hub\",\n                className: \"w-full sm:w-auto\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                      className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm sm:text-base\",\n                      children: \"Go to Hub\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"grid grid-cols-4 gap-1 sm:gap-2 md:gap-3 pt-3 sm:pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-blue-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"10K+\",\n                    duration: 3000,\n                    delay: 500\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-purple-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"500+\",\n                    duration: 3500,\n                    delay: 700\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Courses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-green-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"95%\",\n                    duration: 4000,\n                    delay: 900\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg sm:text-xl md:text-2xl font-bold text-orange-600\",\n                  children: /*#__PURE__*/_jsxDEV(AnimatedCounter, {\n                    end: \"50K+\",\n                    duration: 4500,\n                    delay: 1100\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs sm:text-sm text-gray-600\",\n                  children: \"Study Materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"relative mt-6 xs:mt-8 sm:mt-10 lg:mt-0 order-1 lg:order-2 px-3 sm:px-4 lg:px-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Image1,\n                  alt: \"Students Learning\",\n                  className: \"w-full h-auto rounded-xl sm:rounded-2xl\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                className: \"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  className: \"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 989,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-5, 5, -5]\n                },\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 0.5\n                },\n                className: \"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl mx-3 sm:mx-4 md:mx-6 lg:mx-8 mt-6 sm:mt-8 md:mt-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 50\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8\",\n            children: [{\n              number: \"15K+\",\n              text: \"Active Students\",\n              icon: TbUsers,\n              color: \"from-blue-500 to-blue-600\"\n            }, {\n              number: \"500+\",\n              text: \"Expert Teachers\",\n              icon: TbSchool,\n              color: \"from-green-500 to-green-600\"\n            }, {\n              number: \"1000+\",\n              text: \"Video Lessons\",\n              icon: TbBook,\n              color: \"from-purple-500 to-purple-600\"\n            }, {\n              number: \"98%\",\n              text: \"Success Rate\",\n              icon: TbTrophy,\n              color: \"from-orange-500 to-orange-600\"\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30,\n                scale: 0.9\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              whileHover: {\n                scale: 1.05,\n                y: -5\n              },\n              className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n                children: stat.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 15\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1014,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"bg-gray-50 py-8 sm:py-12 md:py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Students Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Hear from thousands of students who have transformed their learning journey with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1052,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\",\n          children: [{\n            name: \"Amina Hassan\",\n            class: \"Form 4\",\n            rating: 5,\n            text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\n            avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-pink-400 to-purple-500\"\n          }, {\n            name: \"John Mwalimu\",\n            class: \"Class 7\",\n            rating: 5,\n            text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\n            avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-blue-400 to-indigo-500\"\n          }, {\n            name: \"Fatuma Said\",\n            class: \"Form 2\",\n            rating: 5,\n            text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\n            avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\n            bgColor: \"from-green-400 to-teal-500\"\n          }].map((review, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-8 h-8 text-gray-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [...Array(review.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    scale: 0,\n                    rotate: -180\n                  },\n                  whileInView: {\n                    scale: 1,\n                    rotate: 0\n                  },\n                  transition: {\n                    duration: 0.3,\n                    delay: i * 0.1\n                  },\n                  viewport: {\n                    once: true\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-5 h-5 text-yellow-500 drop-shadow-sm\",\n                    style: {\n                      fill: '#FFD700',\n                      color: '#FFD700'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 25\n                  }, this)\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1115,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1113,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400 font-medium\",\n                children: \"Verified Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1112,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-6 leading-relaxed font-medium relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -top-2 -left-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"relative z-10\",\n                children: review.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl text-blue-200 absolute -bottom-4 -right-1\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative mr-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`,\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: review.avatar,\n                    alt: review.name,\n                    className: \"w-full h-full rounded-full object-cover border-2 border-white\",\n                    onError: e => {\n                      // Fallback to initials if image fails to load\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\",\n                    style: {\n                      display: 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600 font-semibold text-sm\",\n                      children: review.name.split(' ').map(n => n[0]).join('')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1151,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1150,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900 text-lg\",\n                    children: review.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1163,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-blue-500\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1167,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-600\",\n                    children: review.class\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1172,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-400\",\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1173,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Verified Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1174,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1067,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1051,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1050,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"bg-white py-8 sm:py-12 md:py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-6 sm:mb-8 md:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: [\"Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"+255 655 285 549\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1197,\n              columnNumber: 139\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4 sm:space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Your Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your full name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  placeholder: \"Enter your email\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                rows: 5,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                placeholder: \"Tell us how we can help you...\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"tel:+255655285549\",\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCDE Call Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1285,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCAC WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 15\n            }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n              children: responseMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gray-900 text-white py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"BRAIN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-green-400\",\n              children: \"WAVE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm leading-relaxed max-w-2xl mx-auto\",\n            children: \"Tanzania's premier educational platform empowering students to achieve academic excellence through innovative learning solutions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-400\",\n                  children: \"\\xA9 2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-white\",\n                  children: \"Brainwave.zone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-blue-400 font-medium\",\n                children: \"Study Smarter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Kigamboni - Dar es Salaam, Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1324,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-6 text-sm text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1330,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-center text-xs text-gray-500\",\n            children: \"Empowering Tanzanian students since 2022 \\u2022 All rights reserved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        scale: 0,\n        rotate: -180\n      },\n      animate: {\n        scale: 1,\n        rotate: 0\n      },\n      transition: {\n        duration: 0.5,\n        delay: 1\n      },\n      className: \"fixed bottom-6 right-6 z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.a, {\n        href: \"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\",\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        className: \"group flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-white\",\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-8 h-8 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\",\n          children: [\"Chat with us on WhatsApp\", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1344,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"g7KKyuBBBvcIXU/tEQbEtuqSJCU=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useNavigate", "motion", "TbArrowRight", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbSchool", "TbStar", "<PERSON>b<PERSON><PERSON><PERSON>", "TbMenu2", "TbX", "message", "useSelector", "contactUs", "Animated<PERSON>ounter", "jsxDEV", "_jsxDEV", "Home", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "formData", "setFormData", "name", "email", "loading", "setLoading", "user", "state", "navigate", "featuresRef", "coursesRef", "successStoriesRef", "contactRef", "scrollToSection", "ref", "_ref$current", "current", "scrollIntoView", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "reviewsSectionRef", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "contactUsRef", "isAdmin", "NotificationBell", "ProfilePicture", "size", "showOnlineStatus", "level", "class", "backgroundImage", "backgroundSize", "type", "stiffness", "damping", "Array", "map", "_", "i", "left", "top", "h1", "lineHeight", "rotateY", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "filter", "transform", "Math", "cos", "PI", "sin", "border", "p", "Fragment", "to", "button", "whileTap", "TbUserPlus", "TbArrowBigRightLinesFilled", "end", "Image1", "whileInView", "viewport", "once", "number", "text", "icon", "stat", "index", "rating", "avatar", "bgColor", "review", "fill", "viewBox", "d", "split", "n", "join", "onSubmit", "onChange", "placeholder", "required", "rows", "disabled", "a", "href", "rel", "responseMessage", "includes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowRight,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbLogin,\r\n  TbMenu2,\r\n  TbX\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport AnimatedCounter from \"../../../components/AnimatedCounter\";\r\n\r\n\r\nconst Home = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  // Refs for smooth scrolling\r\n  const featuresRef = useRef(null);\r\n  const coursesRef = useRef(null);\r\n  const successStoriesRef = useRef(null);\r\n  const contactRef = useRef(null);\r\n\r\n  const scrollToSection = (ref) => {\r\n    ref.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        message.error(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home relative min-h-screen overflow-hidden\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Reviews */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item text-sm md:text-base\">Reviews</button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Notifications + User Profile */}\r\n            <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n              {/* Contact Us Button */}\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item text-sm md:text-base\">Contact Us</button>\r\n              </div>\r\n\r\n              {/* Notification Bell */}\r\n              {user && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile Section */}\r\n              {user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      {user?.level === 'primary' ? `Class ${user?.class}` : user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* PROFESSIONAL HERO SECTION */}\r\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-5\">\r\n          <div className=\"absolute inset-0\" style={{\r\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\r\n            backgroundSize: '60px 60px'\r\n          }}></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-center\">\r\n\r\n\r\n            {/* Left Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8 }}\r\n              className=\"space-y-4 sm:space-y-5 md:space-y-6 lg:space-y-7 text-center lg:text-left order-2 lg:order-1\"\r\n            >\r\n              {/* Premium Animated Study Smarter Text */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{\r\n                  duration: 1.2,\r\n                  delay: 0.2,\r\n                  type: \"spring\",\r\n                  stiffness: 100,\r\n                  damping: 15\r\n                }}\r\n                className=\"relative mb-2 sm:mb-3 md:mb-4\"\r\n              >\r\n                {/* Premium Background Effects */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 -m-8 rounded-3xl\"\r\n                  animate={{\r\n                    background: [\r\n                      'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 60% 40%, rgba(139, 92, 246, 0.04), rgba(59, 130, 246, 0.03), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 40% 60%, rgba(16, 185, 129, 0.03), rgba(139, 92, 246, 0.02), transparent)',\r\n                      'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.03), rgba(16, 185, 129, 0.02), transparent)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 8,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                />\r\n\r\n                {/* Floating Particles */}\r\n                {[...Array(6)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    className=\"absolute w-1 h-1 rounded-full\"\r\n                    style={{\r\n                      background: i % 2 === 0 ? '#3b82f6' : '#10b981',\r\n                      left: `${20 + i * 15}%`,\r\n                      top: `${10 + i * 10}%`\r\n                    }}\r\n                    animate={{\r\n                      y: [0, -20, 0],\r\n                      x: [0, 10, -10, 0],\r\n                      opacity: [0.3, 1, 0.3],\r\n                      scale: [0.5, 1.2, 0.5]\r\n                    }}\r\n                    transition={{\r\n                      duration: 4 + i * 0.5,\r\n                      repeat: Infinity,\r\n                      delay: i * 0.8,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                ))}\r\n\r\n                <motion.h1\r\n                  className=\"text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-center lg:text-left relative px-2 sm:px-4 md:px-6 lg:px-0\"\r\n                  style={{\r\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                    letterSpacing: '-0.02em',\r\n                    lineHeight: '1.1'\r\n                  }}\r\n                >\r\n                  {/* Study - with premium effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block mr-4\"\r\n                    initial={{ opacity: 0, x: -80, scale: 0.7, rotateY: -45 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      rotateY: 0,\r\n                      textShadow: [\r\n                        \"0 0 30px rgba(59, 130, 246, 0.6)\",\r\n                        \"0 0 60px rgba(59, 130, 246, 0.9)\",\r\n                        \"0 0 30px rgba(59, 130, 246, 0.6)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.8,\r\n                      delay: 0.4,\r\n                      type: \"spring\",\r\n                      stiffness: 80,\r\n                      damping: 12,\r\n                      textShadow: {\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.15,\r\n                      rotate: [0, -2, 2, 0],\r\n                      y: [-5, 0],\r\n                      transition: {\r\n                        duration: 0.6,\r\n                        type: \"spring\",\r\n                        stiffness: 300\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)',\r\n                      WebkitBackgroundClip: 'text',\r\n                      WebkitTextFillColor: 'transparent',\r\n                      backgroundClip: 'text',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 30px rgba(59, 130, 246, 0.6)',\r\n                      filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3))'\r\n                    }}\r\n                  >\r\n                    Study\r\n\r\n                    {/* Premium floating elements around Study */}\r\n                    <motion.div\r\n                      className=\"absolute -top-3 -right-3 w-4 h-4 rounded-full\"\r\n                      animate={{\r\n                        scale: [0.6, 1.4, 0.6],\r\n                        opacity: [0.4, 1, 0.4],\r\n                        rotate: [0, 360],\r\n                        background: [\r\n                          'linear-gradient(45deg, #3b82f6, #1d4ed8)',\r\n                          'linear-gradient(45deg, #8b5cf6, #7c3aed)',\r\n                          'linear-gradient(45deg, #3b82f6, #1d4ed8)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        delay: 1,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',\r\n                        boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)'\r\n                      }}\r\n                    />\r\n\r\n                    <motion.div\r\n                      className=\"absolute -bottom-2 -left-2 w-3 h-3 rounded-full\"\r\n                      animate={{\r\n                        scale: [0.8, 1.6, 0.8],\r\n                        opacity: [0.5, 1, 0.5],\r\n                        x: [0, 8, 0],\r\n                        y: [0, -6, 0],\r\n                        rotate: [0, -180, -360]\r\n                      }}\r\n                      transition={{\r\n                        duration: 3.5,\r\n                        repeat: Infinity,\r\n                        delay: 0.7,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(135deg, #60a5fa, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(96, 165, 250, 0.9)'\r\n                      }}\r\n                    />\r\n\r\n                    {/* Premium light streak effect */}\r\n                    <motion.div\r\n                      className=\"absolute inset-0 rounded-lg\"\r\n                      animate={{\r\n                        background: [\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 3,\r\n                        repeat: Infinity,\r\n                        delay: 2,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n\r\n                  {/* Smarter - with premium flowing effects */}\r\n                  <motion.span\r\n                    className=\"relative inline-block\"\r\n                    initial={{ opacity: 0, x: 80, scale: 0.7, rotateY: 45 }}\r\n                    animate={{\r\n                      opacity: 1,\r\n                      x: 0,\r\n                      scale: 1,\r\n                      rotateY: 0,\r\n                      y: [0, -4, 0, 4, 0],\r\n                      textShadow: [\r\n                        \"0 0 30px rgba(16, 185, 129, 0.6)\",\r\n                        \"0 0 60px rgba(16, 185, 129, 0.9)\",\r\n                        \"0 0 30px rgba(16, 185, 129, 0.6)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.8,\r\n                      delay: 0.8,\r\n                      type: \"spring\",\r\n                      stiffness: 80,\r\n                      damping: 12,\r\n                      y: {\r\n                        duration: 5,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      },\r\n                      textShadow: {\r\n                        duration: 4.5,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    whileHover={{\r\n                      scale: 1.15,\r\n                      rotate: [0, 2, -2, 0],\r\n                      y: [-8, 0],\r\n                      transition: {\r\n                        duration: 0.6,\r\n                        type: \"spring\",\r\n                        stiffness: 300\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\r\n                      WebkitBackgroundClip: 'text',\r\n                      WebkitTextFillColor: 'transparent',\r\n                      backgroundClip: 'text',\r\n                      fontWeight: '900',\r\n                      textShadow: '0 0 30px rgba(16, 185, 129, 0.6)',\r\n                      filter: 'drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3))'\r\n                    }}\r\n                  >\r\n                    Smarter\r\n\r\n                    {/* Premium animated orbital line around Smarter */}\r\n                    <motion.div\r\n                      className=\"absolute top-0 left-0 w-3 h-3 rounded-full\"\r\n                      animate={{\r\n                        opacity: [0, 1, 0.8, 1, 0],\r\n                        x: [0, 120, 240, 360, 240, 120, 0],\r\n                        y: [0, -15, 0, 15, 30, 15, 0],\r\n                        scale: [0.5, 1.2, 0.8, 1.5, 0.5],\r\n                        background: [\r\n                          'linear-gradient(45deg, #10b981, #34d399)',\r\n                          'linear-gradient(45deg, #34d399, #6ee7b7)',\r\n                          'linear-gradient(45deg, #6ee7b7, #10b981)',\r\n                          'linear-gradient(45deg, #10b981, #34d399)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 6,\r\n                        repeat: Infinity,\r\n                        delay: 2.5,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(45deg, #10b981, #34d399)',\r\n                        boxShadow: '0 0 20px rgba(16, 185, 129, 0.8)'\r\n                      }}\r\n                    />\r\n\r\n                    {/* Premium trailing particles */}\r\n                    {[...Array(3)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        className=\"absolute w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 0.8, 0],\r\n                          x: [0, 80 + i * 20, 160 + i * 40, 240 + i * 20, 160 + i * 40, 80 + i * 20, 0],\r\n                          y: [0, -8 - i * 2, 0, 8 + i * 2, 16 + i * 4, 8 + i * 2, 0],\r\n                          scale: [0.3, 1, 0.6, 1.2, 0.3]\r\n                        }}\r\n                        transition={{\r\n                          duration: 6,\r\n                          repeat: Infinity,\r\n                          delay: 3 + i * 0.3,\r\n                          ease: \"easeInOut\"\r\n                        }}\r\n                        style={{\r\n                          background: `linear-gradient(45deg, #34d399, #6ee7b7)`,\r\n                          boxShadow: '0 0 10px rgba(52, 211, 153, 0.6)'\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                    {/* Premium light sweep effect */}\r\n                    <motion.div\r\n                      className=\"absolute inset-0 rounded-lg\"\r\n                      animate={{\r\n                        background: [\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',\r\n                          'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 4,\r\n                        repeat: Infinity,\r\n                        delay: 3.5,\r\n                        ease: \"easeInOut\"\r\n                      }}\r\n                    />\r\n                  </motion.span>\r\n                </motion.h1>\r\n\r\n                {/* Glowing underline effect */}\r\n                <motion.div\r\n                  className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 h-2 rounded-full\"\r\n                  initial={{ width: 0, opacity: 0 }}\r\n                  animate={{\r\n                    width: '80%',\r\n                    opacity: 1,\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)',\r\n                      '0 0 40px rgba(59, 130, 246, 0.8)',\r\n                      '0 0 20px rgba(16, 185, 129, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    delay: 1.5,\r\n                    boxShadow: {\r\n                      duration: 3,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }\r\n                  }}\r\n                  style={{\r\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6, #3b82f6)',\r\n                    boxShadow: '0 0 30px rgba(16, 185, 129, 0.6)'\r\n                  }}\r\n                >\r\n                  {/* Moving light effect inside underline */}\r\n                  <motion.div\r\n                    className=\"absolute inset-0 rounded-full\"\r\n                    animate={{\r\n                      background: [\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)',\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',\r\n                        'linear-gradient(90deg, transparent, rgba(255,255,255,0.0), transparent)'\r\n                      ],\r\n                      x: ['-100%', '100%']\r\n                    }}\r\n                    transition={{\r\n                      background: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      },\r\n                      x: {\r\n                        duration: 3,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\",\r\n                        delay: 2\r\n                      }\r\n                    }}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Premium orbiting elements around the text */}\r\n                {[...Array(4)].map((_, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    className=\"absolute top-1/2 left-1/2 rounded-full\"\r\n                    style={{\r\n                      width: `${16 + i * 4}px`,\r\n                      height: `${16 + i * 4}px`,\r\n                      background: i % 2 === 0\r\n                        ? 'linear-gradient(45deg, #3b82f6, #8b5cf6)'\r\n                        : 'linear-gradient(45deg, #10b981, #34d399)',\r\n                      boxShadow: i % 2 === 0\r\n                        ? '0 0 20px rgba(59, 130, 246, 0.8)'\r\n                        : '0 0 20px rgba(16, 185, 129, 0.8)',\r\n                      transform: 'translate(-50%, -50%)'\r\n                    }}\r\n                    animate={{\r\n                      rotate: i % 2 === 0 ? 360 : -360,\r\n                      x: [\r\n                        0,\r\n                        Math.cos((i * Math.PI) / 2) * (120 + i * 20),\r\n                        0,\r\n                        -Math.cos((i * Math.PI) / 2) * (120 + i * 20),\r\n                        0\r\n                      ],\r\n                      y: [\r\n                        0,\r\n                        -Math.sin((i * Math.PI) / 2) * (60 + i * 15),\r\n                        -(120 + i * 20),\r\n                        -Math.sin((i * Math.PI) / 2) * (60 + i * 15),\r\n                        0\r\n                      ],\r\n                      opacity: [0.4, 1, 0.6, 1, 0.4],\r\n                      scale: [0.8, 1.2, 1, 1.3, 0.8]\r\n                    }}\r\n                    transition={{\r\n                      duration: 10 + i * 2,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\",\r\n                      delay: i * 0.5\r\n                    }}\r\n                  />\r\n                ))}\r\n\r\n                {/* Premium floating sparkles */}\r\n                {[...Array(8)].map((_, i) => (\r\n                  <motion.div\r\n                    key={`sparkle-${i}`}\r\n                    className=\"absolute w-2 h-2 rounded-full\"\r\n                    style={{\r\n                      background: `linear-gradient(45deg, ${\r\n                        ['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]\r\n                      }, #ffffff)`,\r\n                      left: `${10 + i * 12}%`,\r\n                      top: `${15 + (i % 3) * 25}%`,\r\n                      boxShadow: `0 0 10px ${\r\n                        ['#fbbf24', '#f59e0b', '#3b82f6', '#8b5cf6', '#10b981', '#34d399', '#ef4444', '#f97316'][i]\r\n                      }`\r\n                    }}\r\n                    animate={{\r\n                      y: [0, -30, 0],\r\n                      x: [0, Math.sin(i) * 20, 0],\r\n                      opacity: [0, 1, 0],\r\n                      scale: [0.3, 1, 0.3],\r\n                      rotate: [0, 180, 360]\r\n                    }}\r\n                    transition={{\r\n                      duration: 3 + i * 0.3,\r\n                      repeat: Infinity,\r\n                      delay: i * 0.4,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                ))}\r\n              </motion.div>\r\n\r\n              {/* Highlighted Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.8 }}\r\n                className=\"relative inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6 overflow-hidden\"\r\n                style={{\r\n                  background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%)',\r\n                  boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.3)',\r\n                  border: '2px solid #FFD700'\r\n                }}\r\n              >\r\n                {/* Animated background shimmer */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent\"\r\n                  animate={{\r\n                    x: ['-100%', '100%']\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\"\r\n                  }}\r\n                />\r\n\r\n                <TbSchool className=\"w-5 h-5 mr-2 text-orange-800 relative z-10\" />\r\n                <span className=\"text-orange-900 relative z-10 font-black\">\r\n                  #1 Educational Platform in Tanzania\r\n                </span>\r\n\r\n                {/* Glowing border effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 rounded-full\"\r\n                  animate={{\r\n                    boxShadow: [\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)',\r\n                      '0 0 40px rgba(255, 215, 0, 0.8)',\r\n                      '0 0 20px rgba(255, 215, 0, 0.5)'\r\n                    ]\r\n                  }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Description */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl text-gray-600 leading-relaxed max-w-full sm:max-w-2xl mx-auto lg:mx-0 px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </motion.p>\r\n\r\n              {/* Action Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.9 }}\r\n                className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 md:gap-5 items-center justify-center lg:justify-start w-full px-2 sm:px-4 lg:px-0\"\r\n              >\r\n                {!user ? (\r\n                  <React.Fragment>\r\n\r\n\r\n                    {/* Register and Login Buttons - Super Responsive */}\r\n                    <div className=\"flex flex-col xs:flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\">\r\n                      <Link to=\"/register\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbUserPlus className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Register Now</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n\r\n                      <Link to=\"/login\" className=\"w-full sm:w-auto\">\r\n                        <motion.button\r\n                          className=\"w-full sm:w-auto px-4 xs:px-6 sm:px-8 md:px-10 py-3 xs:py-3.5 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 min-h-[48px]\"\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                        >\r\n                          <div className=\"flex items-center justify-center space-x-2\">\r\n                            <TbLogin className=\"w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5\" />\r\n                            <span className=\"text-sm xs:text-sm sm:text-base\">Login</span>\r\n                          </div>\r\n                        </motion.button>\r\n                      </Link>\r\n                    </div>\r\n                  </React.Fragment>\r\n                ) : (\r\n                  <Link to=\"/user/hub\" className=\"w-full sm:w-auto\">\r\n                    <motion.button\r\n                      className=\"w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\"\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                    >\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        <TbArrowBigRightLinesFilled className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                        <span className=\"text-sm sm:text-base\">Go to Hub</span>\r\n                      </div>\r\n                    </motion.button>\r\n                  </Link>\r\n                )}\r\n              </motion.div>\r\n\r\n              {/* Stats */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"grid grid-cols-4 gap-1 sm:gap-2 md:gap-3 pt-3 sm:pt-4\"\r\n              >\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-blue-600\">\r\n                    <AnimatedCounter end=\"10K+\" duration={3000} delay={500} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Students</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-purple-600\">\r\n                    <AnimatedCounter end=\"500+\" duration={3500} delay={700} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Courses</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-green-600\">\r\n                    <AnimatedCounter end=\"95%\" duration={4000} delay={900} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Success Rate</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-lg sm:text-xl md:text-2xl font-bold text-orange-600\">\r\n                    <AnimatedCounter end=\"50K+\" duration={4500} delay={1100} />\r\n                  </div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-600\">Study Materials</div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right Content - Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.2 }}\r\n              className=\"relative mt-6 xs:mt-8 sm:mt-10 lg:mt-0 order-1 lg:order-2 px-3 sm:px-4 lg:px-0\"\r\n            >\r\n              <div className=\"relative\">\r\n                {/* Main Image */}\r\n                <div className=\"relative z-10 bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8\">\r\n                  <img\r\n                    src={Image1}\r\n                    alt=\"Students Learning\"\r\n                    className=\"w-full h-auto rounded-xl sm:rounded-2xl\"\r\n                    loading=\"lazy\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\r\n                  className=\"absolute -top-3 sm:-top-6 -right-3 sm:-right-6 bg-blue-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbTrophy className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\r\n                  className=\"absolute -bottom-3 sm:-bottom-6 -left-3 sm:-left-6 bg-green-500 text-white p-2 sm:p-3 md:p-4 rounded-xl sm:rounded-2xl shadow-lg z-20\"\r\n                >\r\n                  <TbBook className=\"w-4 h-4 sm:w-6 sm:h-6 md:w-8 md:h-8\" />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [-5, 5, -5] }}\r\n                  transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 }}\r\n                  className=\"absolute top-1/2 -left-4 sm:-left-6 md:-left-8 bg-purple-500 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-lg z-20\"\r\n                >\r\n                  <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6\" />\r\n                </motion.div>\r\n\r\n                {/* Background Decoration */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl sm:rounded-3xl transform rotate-3 sm:rotate-6 scale-105\"></div>\r\n              </div>\r\n            </motion.div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Section - PART OF HERO SECTION */}\r\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl mx-3 sm:mx-4 md:mx-6 lg:mx-8 mt-6 sm:mt-8 md:mt-10\">\r\n          <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8\"\r\n            >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n        </div>\r\n      </section>\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"bg-gray-50 py-8 sm:py-12 md:py-16\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              What Our Students Say\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Hear from thousands of students who have transformed their learning journey with BrainWave\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8\">\r\n            {[\r\n              {\r\n                name: \"Amina Hassan\",\r\n                class: \"Form 4\",\r\n                rating: 5,\r\n                text: \"BrainWave helped me improve my grades significantly. The interactive quizzes and study materials are amazing!\",\r\n                avatar: \"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-pink-400 to-purple-500\"\r\n              },\r\n              {\r\n                name: \"John Mwalimu\",\r\n                class: \"Class 7\",\r\n                rating: 5,\r\n                text: \"I love the AI-powered questions and the ranking system. It makes learning competitive and fun!\",\r\n                avatar: \"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-blue-400 to-indigo-500\"\r\n              },\r\n              {\r\n                name: \"Fatuma Said\",\r\n                class: \"Form 2\",\r\n                rating: 5,\r\n                text: \"The platform is so easy to use and the content is exactly what we need for our exams. Highly recommended!\",\r\n                avatar: \"https://images.unsplash.com/photo-1595152772835-219674b2a8a6?w=150&h=150&fit=crop&crop=face&auto=format&q=80\",\r\n                bgColor: \"from-green-400 to-teal-500\"\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group overflow-hidden\"\r\n              >\r\n                {/* Premium Background Gradient */}\r\n                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${review.bgColor}`}></div>\r\n\r\n                {/* Floating Quote Icon */}\r\n                <div className=\"absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300\">\r\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"/>\r\n                  </svg>\r\n                </div>\r\n                {/* Premium Star Rating */}\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    {[...Array(review.rating)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        initial={{ scale: 0, rotate: -180 }}\r\n                        whileInView={{ scale: 1, rotate: 0 }}\r\n                        transition={{ duration: 0.3, delay: i * 0.1 }}\r\n                        viewport={{ once: true }}\r\n                      >\r\n                        <TbStar className=\"w-5 h-5 text-yellow-500 drop-shadow-sm\" style={{ fill: '#FFD700', color: '#FFD700' }} />\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-400 font-medium\">Verified Review</div>\r\n                </div>\r\n                {/* Premium Review Text */}\r\n                <p className=\"text-gray-700 mb-6 leading-relaxed font-medium relative\">\r\n                  <span className=\"text-2xl text-blue-200 absolute -top-2 -left-1\">\"</span>\r\n                  <span className=\"relative z-10\">{review.text}</span>\r\n                  <span className=\"text-2xl text-blue-200 absolute -bottom-4 -right-1\">\"</span>\r\n                </p>\r\n                {/* Premium Profile Section */}\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"relative mr-4\">\r\n                    {/* Profile Picture with Premium Border */}\r\n                    <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${review.bgColor} p-0.5`}>\r\n                      <img\r\n                        src={review.avatar}\r\n                        alt={review.name}\r\n                        className=\"w-full h-full rounded-full object-cover border-2 border-white\"\r\n                        onError={(e) => {\r\n                          // Fallback to initials if image fails to load\r\n                          e.target.style.display = 'none';\r\n                          e.target.nextSibling.style.display = 'flex';\r\n                        }}\r\n                      />\r\n                      {/* Fallback initials */}\r\n                      <div className=\"w-full h-full rounded-full bg-white flex items-center justify-center absolute top-0 left-0\" style={{display: 'none'}}>\r\n                        <span className=\"text-gray-600 font-semibold text-sm\">\r\n                          {review.name.split(' ').map(n => n[0]).join('')}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Online Status Indicator */}\r\n                    <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"></div>\r\n                  </div>\r\n\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <h4 className=\"font-bold text-gray-900 text-lg\">{review.name}</h4>\r\n                      {/* Verified Badge */}\r\n                      <div className=\"flex items-center\">\r\n                        <svg className=\"w-4 h-4 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                        </svg>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-1\">\r\n                      <span className=\"text-sm font-medium text-gray-600\">{review.class}</span>\r\n                      <span className=\"text-xs text-gray-400\">•</span>\r\n                      <span className=\"text-xs text-gray-500\">Verified Student</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"bg-white py-8 sm:py-12 md:py-16\">\r\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-6 sm:mb-8 md:mb-12\"\r\n          >\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Get in Touch\r\n            </h2>\r\n            <p className=\"text-base sm:text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\">\r\n              Have questions? We'd love to hear from you. Send us a message through the form below or contact us directly via WhatsApp at <strong>+255 655 285 549</strong>.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n            className=\"bg-gray-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8\"\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\r\n              <div className=\"grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Your Name\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your full name\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Email Address\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"Enter your email\"\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Message\r\n                </label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  rows={5}\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"Tell us how we can help you...\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"flex flex-col sm:flex-row gap-4 items-center\">\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n\r\n                {/* Call Now Button */}\r\n                <motion.a\r\n                  href=\"tel:+255655285549\"\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z\"/>\r\n                  </svg>\r\n                  <span>📞 Call Now</span>\r\n                </motion.a>\r\n\r\n                {/* WhatsApp Text Button */}\r\n                <motion.a\r\n                  href=\"https://wa.me/255655285549?text=Hello%20BrainWave%20Team!%20I%20need%20help%20with%20the%20educational%20platform.%20Can%20you%20assist%20me?\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n                  </svg>\r\n                  <span>💬 WhatsApp</span>\r\n                </motion.a>\r\n              </div>\r\n\r\n              {responseMessage && (\r\n                <div className={`p-4 rounded-lg ${responseMessage.includes('success') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>\r\n                  {responseMessage}\r\n                </div>\r\n              )}\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Professional Copyright Footer */}\r\n      <footer className=\"bg-gray-900 text-white py-8\">\r\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\r\n          {/* Company Info */}\r\n          <div className=\"text-center mb-6\">\r\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\r\n              <span className=\"text-2xl font-bold text-white\">BRAIN</span>\r\n              <span className=\"text-2xl font-bold text-green-400\">WAVE</span>\r\n            </div>\r\n            <p className=\"text-gray-400 text-sm leading-relaxed max-w-2xl mx-auto\">\r\n              Tanzania's premier educational platform empowering students to achieve academic excellence through innovative learning solutions.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Copyright Bar */}\r\n          <div className=\"border-t border-gray-700 pt-6\">\r\n            <div className=\"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\">\r\n              <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <span className=\"text-sm text-gray-400\">© 2022</span>\r\n                  <span className=\"text-lg font-bold text-white\">Brainwave.zone</span>\r\n                </div>\r\n                <span className=\"text-sm text-blue-400 font-medium\">Study Smarter</span>\r\n                <span className=\"text-sm text-gray-400\">Kigamboni - Dar es Salaam, Tanzania</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-6 text-sm text-gray-400\">\r\n                <span>Privacy Policy</span>\r\n                <span>Terms of Service</span>\r\n                <span>Support</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-4 text-center text-xs text-gray-500\">\r\n              Empowering Tanzanian students since 2022 • All rights reserved\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n\r\n\r\n\r\n      {/* Floating WhatsApp Button - Blue & White */}\r\n      <motion.div\r\n        initial={{ scale: 0, rotate: -180 }}\r\n        animate={{ scale: 1, rotate: 0 }}\r\n        transition={{ duration: 0.5, delay: 1 }}\r\n        className=\"fixed bottom-6 right-6 z-50\"\r\n      >\r\n        <motion.a\r\n          href=\"https://wa.me/255655285549?text=Hello! I'm interested in BrainWave educational platform. Can you help me get started?\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"group flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-white\"\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          {/* WhatsApp Icon */}\r\n          <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\r\n          </svg>\r\n\r\n          {/* Pulse Animation - Blue */}\r\n          <div className=\"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20\"></div>\r\n\r\n          {/* Tooltip */}\r\n          <div className=\"absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n            Chat with us on WhatsApp\r\n            <div className=\"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45\"></div>\r\n          </div>\r\n        </motion.a>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEb,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE+B;EAAK,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+B,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,UAAU,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmC,iBAAiB,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMoC,UAAU,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMqC,eAAe,GAAIC,GAAG,IAAK;IAAA,IAAAC,YAAA;IAC/B,CAAAA,YAAA,GAAAD,GAAG,CAACE,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACrD,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGmB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBnB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoB,IAAI,GAAG,MAAMjC,SAAS,CAACQ,QAAQ,CAAC;MACtC,IAAIyB,IAAI,CAACC,OAAO,EAAE;QAChBpC,OAAO,CAACoC,OAAO,CAAC,4BAA4B,CAAC;QAC7CzB,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEb,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLA,OAAO,CAACqC,KAAK,CAACF,IAAI,CAACnC,OAAO,IAAI,uBAAuB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,0CAA0C,CAAC;IAC3D;IACAtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKiC,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzDlC,OAAA,CAAChB,MAAM,CAACmD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5KlC,OAAA;QAAKiC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDlC,OAAA;UAAKiC,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAE7ElC,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1ClC,OAAA;cAAKiC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjElC,OAAA;gBAAQwC,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAACuB,iBAAiB,CAAE;gBAACR,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7C,OAAA;YAAKiC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzClC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAI,CAAE;cACpCR,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtDlC,OAAA;gBACEiC,SAAS,EAAC,wEAAwE;gBAClFkB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAnB,QAAA,eAEFlC,OAAA;kBACEsD,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBtB,SAAS,EAAC,4BAA4B;kBACtCkB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGhC,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAAC2B,GAAG,GAAG,8GAA8G;oBAC7H7B,CAAC,CAACE,MAAM,CAAC+B,OAAO,GAAG,MAAM;sBACvB;sBACAjC,CAAC,CAACE,MAAM,CAACwB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BlC,CAAC,CAACE,MAAM,CAACiC,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN7C,OAAA;gBAAKiC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3ClC,OAAA;kBAAIiC,SAAS,EAAC,qFAAqF;kBAC/FkB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA7B,QAAA,gBAEJlC,OAAA,CAAChB,MAAM,CAACgF,IAAI;oBACV/B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE2B,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CR,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV2B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,GACH,OAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;sBACTb,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBS,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGd7C,OAAA,CAAChB,MAAM,CAACgF,IAAI;oBACV/B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE2B,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CR,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV2B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRV,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB6B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVb,CAAC,EAAE;wBACDY,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,GACH,MAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;sBACTb,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB2B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd5B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBqC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGL7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBACTb,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEgB,KAAK,EAAE,CAAC;oBAAEd,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPa,KAAK,EAAE,MAAM;oBACbd,OAAO,EAAE,CAAC;oBACVqC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN7C,OAAA;gBACEiC,SAAS,EAAC,gEAAgE;gBAC1EkB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAnB,QAAA,gBAEFlC,OAAA;kBACEsD,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBtB,SAAS,EAAC,4BAA4B;kBACtCkB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAGhC,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACwB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BlC,CAAC,CAACE,MAAM,CAACkD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7C,OAAA;kBACEiC,SAAS,EAAC,gHAAgH;kBAC1HkB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA5C,QAAA,EACH;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN7C,OAAA;gBAAKiC,SAAS,EAAC;cAAyK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN7C,OAAA;YAAKiC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnElC,OAAA;cAAKiC,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjElC,OAAA;gBAAQwC,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC6D,YAAY,CAAE;gBAAC9C,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,EAGLlC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqE,OAAO,kBACrBhF,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAI,CAAE;cACpCR,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE1ClC,OAAA,CAACiF,gBAAgB;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGAlC,IAAI,iBACHX,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAI,CAAE;cACpCR,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAES,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7ClC,OAAA,CAACkF,cAAc;gBACbvE,IAAI,EAAEA,IAAK;gBACXwE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvBjC,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGF7C,OAAA;gBAAKiC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClC,OAAA;kBAAKiC,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEJ,IAAI,KAAI;gBAAM;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,EAC7F,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,KAAK,MAAK,SAAS,GAAI,SAAQ1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,KAAM,EAAC,GAAG3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E;gBAAK;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhB7C,OAAA;MAASiC,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAEjFlC,OAAA;QAAKiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClC,OAAA;UAAKiC,SAAS,EAAC,kBAAkB;UAACkB,KAAK,EAAE;YACvCoC,eAAe,EAAG,kQAAiQ;YACnRC,cAAc,EAAE;UAClB;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7C,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DlC,OAAA;UAAKiC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAI9FlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE2B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChC1B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE2B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BhB,SAAS,EAAC,8FAA8F;YAAAC,QAAA,gBAGxGlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,EAAE;gBAAEU,KAAK,EAAE;cAAI,CAAE;cAC3CR,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;gBAAEU,KAAK,EAAE;cAAE,CAAE;cACxCC,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbC,KAAK,EAAE,GAAG;gBACVuC,IAAI,EAAE,QAAQ;gBACdC,SAAS,EAAE,GAAG;gBACdC,OAAO,EAAE;cACX,CAAE;cACF1D,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAGzClC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,mCAAmC;gBAC7CM,OAAO,EAAE;kBACPqC,UAAU,EAAE,CACV,+GAA+G,EAC/G,+GAA+G,EAC/G,+GAA+G,EAC/G,+GAA+G;gBAEnH,CAAE;gBACF5B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGD,CAAC,GAAG+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB/F,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,+BAA+B;gBACzCkB,KAAK,EAAE;kBACLyB,UAAU,EAAEmB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;kBAC/CC,IAAI,EAAG,GAAE,EAAE,GAAGD,CAAC,GAAG,EAAG,GAAE;kBACvBE,GAAG,EAAG,GAAE,EAAE,GAAGF,CAAC,GAAG,EAAG;gBACtB,CAAE;gBACFxD,OAAO,EAAE;kBACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACd4B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBAClB3B,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBACtBS,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;gBACvB,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC,GAAG8C,CAAC,GAAG,GAAG;kBACrB5B,MAAM,EAAEC,QAAQ;kBAChBlB,KAAK,EAAE6C,CAAC,GAAG,GAAG;kBACd1B,IAAI,EAAE;gBACR;cAAE,GAlBG0B,CAAC;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBP,CACF,CAAC,eAEF7C,OAAA,CAAChB,MAAM,CAACkH,EAAE;gBACRjE,SAAS,EAAC,gJAAgJ;gBAC1JkB,KAAK,EAAE;kBACLW,UAAU,EAAE,yDAAyD;kBACrEC,aAAa,EAAE,SAAS;kBACxBoC,UAAU,EAAE;gBACd,CAAE;gBAAAjE,QAAA,gBAGFlC,OAAA,CAAChB,MAAM,CAACgF,IAAI;kBACV/B,SAAS,EAAC,4BAA4B;kBACtCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE,CAAC,EAAE;oBAAElB,KAAK,EAAE,GAAG;oBAAEqD,OAAO,EAAE,CAAC;kBAAG,CAAE;kBAC1D7D,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACV2B,CAAC,EAAE,CAAC;oBACJlB,KAAK,EAAE,CAAC;oBACRqD,OAAO,EAAE,CAAC;oBACVlC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVuC,IAAI,EAAE,QAAQ;oBACdC,SAAS,EAAE,EAAE;oBACbC,OAAO,EAAE,EAAE;oBACXzB,UAAU,EAAE;sBACVjB,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFC,UAAU,EAAE;oBACVvB,KAAK,EAAE,IAAI;oBACXwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACrBlC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACVW,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbwC,IAAI,EAAE,QAAQ;sBACdC,SAAS,EAAE;oBACb;kBACF,CAAE;kBACFvC,KAAK,EAAE;oBACLyB,UAAU,EAAE,gEAAgE;oBAC5EyB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtB9B,UAAU,EAAE,KAAK;oBACjBP,UAAU,EAAE,kCAAkC;oBAC9CsC,MAAM,EAAE;kBACV,CAAE;kBAAAtE,QAAA,GACH,OAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,+CAA+C;oBACzDM,OAAO,EAAE;sBACPQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBACtBT,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;sBACtBiC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;sBAChBK,UAAU,EAAE,CACV,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C;oBAE9C,CAAE;oBACF5B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,CAAC;sBACRmB,IAAI,EAAE;oBACR,CAAE;oBACFlB,KAAK,EAAE;sBACLyB,UAAU,EAAE,0CAA0C;sBACtDD,SAAS,EAAE;oBACb;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,iDAAiD;oBAC3DM,OAAO,EAAE;sBACPQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBACtBT,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;sBACtB2B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACZ5B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACbkC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG;oBACxB,CAAE;oBACFvB,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,GAAG;sBACVmB,IAAI,EAAE;oBACR,CAAE;oBACFlB,KAAK,EAAE;sBACLyB,UAAU,EAAE,2CAA2C;sBACvDD,SAAS,EAAE;oBACb;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGF7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,6BAA6B;oBACvCM,OAAO,EAAE;sBACPqC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE;oBAE7E,CAAE;oBACF5B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,CAAC;sBACRmB,IAAI,EAAE;oBACR;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAGd7C,OAAA,CAAChB,MAAM,CAACgF,IAAI;kBACV/B,SAAS,EAAC,uBAAuB;kBACjCG,OAAO,EAAE;oBAAEE,OAAO,EAAE,CAAC;oBAAE2B,CAAC,EAAE,EAAE;oBAAElB,KAAK,EAAE,GAAG;oBAAEqD,OAAO,EAAE;kBAAG,CAAE;kBACxD7D,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACV2B,CAAC,EAAE,CAAC;oBACJlB,KAAK,EAAE,CAAC;oBACRqD,OAAO,EAAE,CAAC;oBACV/D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACnB6B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFlB,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVuC,IAAI,EAAE,QAAQ;oBACdC,SAAS,EAAE,EAAE;oBACbC,OAAO,EAAE,EAAE;oBACXtD,CAAC,EAAE;sBACDY,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAC;oBACDH,UAAU,EAAE;sBACVjB,QAAQ,EAAE,GAAG;sBACbkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFC,UAAU,EAAE;oBACVvB,KAAK,EAAE,IAAI;oBACXwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrBlC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACVW,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbwC,IAAI,EAAE,QAAQ;sBACdC,SAAS,EAAE;oBACb;kBACF,CAAE;kBACFvC,KAAK,EAAE;oBACLyB,UAAU,EAAE,6EAA6E;oBACzFyB,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCC,cAAc,EAAE,MAAM;oBACtB9B,UAAU,EAAE,KAAK;oBACjBP,UAAU,EAAE,kCAAkC;oBAC9CsC,MAAM,EAAE;kBACV,CAAE;kBAAAtE,QAAA,GACH,SAGC,eACAlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,4CAA4C;oBACtDM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;sBAC1B2B,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;sBAClC5B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;sBAC7BU,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;sBAChC6B,UAAU,EAAE,CACV,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C;oBAE9C,CAAE;oBACF5B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,GAAG;sBACVmB,IAAI,EAAE;oBACR,CAAE;oBACFlB,KAAK,EAAE;sBACLyB,UAAU,EAAE,0CAA0C;sBACtDD,SAAS,EAAE;oBACb;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGD,CAAC,GAAG+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB/F,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBAETb,SAAS,EAAC,mCAAmC;oBAC7CM,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;sBACpB2B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG8B,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,EAAE,EAAE,GAAGA,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;sBAC7E1D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG0D,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE,EAAE,GAAGA,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;sBAC1DhD,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;oBAC/B,CAAE;oBACFC,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,CAAC,GAAG6C,CAAC,GAAG,GAAG;sBAClB1B,IAAI,EAAE;oBACR,CAAE;oBACFlB,KAAK,EAAE;sBACLyB,UAAU,EAAG,0CAAyC;sBACtDD,SAAS,EAAE;oBACb;kBAAE,GAjBGoB,CAAC;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkBP,CACF,CAAC,eAGF7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;oBACTb,SAAS,EAAC,6BAA6B;oBACvCM,OAAO,EAAE;sBACPqC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE;oBAE7E,CAAE;oBACF5B,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBlB,KAAK,EAAE,GAAG;sBACVmB,IAAI,EAAE;oBACR;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGZ7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,yEAAyE;gBACnFG,OAAO,EAAE;kBAAEgB,KAAK,EAAE,CAAC;kBAAEd,OAAO,EAAE;gBAAE,CAAE;gBAClCC,OAAO,EAAE;kBACPa,KAAK,EAAE,KAAK;kBACZd,OAAO,EAAE,CAAC;kBACVqC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACF3B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,KAAK,EAAE,GAAG;kBACVyB,SAAS,EAAE;oBACT1B,QAAQ,EAAE,CAAC;oBACXkB,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR;gBACF,CAAE;gBACFlB,KAAK,EAAE;kBACLyB,UAAU,EAAE,4DAA4D;kBACxED,SAAS,EAAE;gBACb,CAAE;gBAAAzC,QAAA,eAGFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBACTb,SAAS,EAAC,+BAA+B;kBACzCM,OAAO,EAAE;oBACPqC,UAAU,EAAE,CACV,yEAAyE,EACzE,yEAAyE,EACzE,yEAAyE,CAC1E;oBACDX,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;kBACrB,CAAE;kBACFjB,UAAU,EAAE;oBACV4B,UAAU,EAAE;sBACV3B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR,CAAC;oBACDJ,CAAC,EAAE;sBACDhB,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE,WAAW;sBACjBnB,KAAK,EAAE;oBACT;kBACF;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EAGZ,CAAC,GAAG+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB/F,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,wCAAwC;gBAClDkB,KAAK,EAAE;kBACLC,KAAK,EAAG,GAAE,EAAE,GAAG2C,CAAC,GAAG,CAAE,IAAG;kBACxB1C,MAAM,EAAG,GAAE,EAAE,GAAG0C,CAAC,GAAG,CAAE,IAAG;kBACzBnB,UAAU,EAAEmB,CAAC,GAAG,CAAC,KAAK,CAAC,GACnB,0CAA0C,GAC1C,0CAA0C;kBAC9CpB,SAAS,EAAEoB,CAAC,GAAG,CAAC,KAAK,CAAC,GAClB,kCAAkC,GAClC,kCAAkC;kBACtCU,SAAS,EAAE;gBACb,CAAE;gBACFlE,OAAO,EAAE;kBACPgC,MAAM,EAAEwB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;kBAChC9B,CAAC,EAAE,CACD,CAAC,EACDyC,IAAI,CAACC,GAAG,CAAEZ,CAAC,GAAGW,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,GAAG,GAAGb,CAAC,GAAG,EAAE,CAAC,EAC5C,CAAC,EACD,CAACW,IAAI,CAACC,GAAG,CAAEZ,CAAC,GAAGW,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,GAAG,GAAGb,CAAC,GAAG,EAAE,CAAC,EAC7C,CAAC,CACF;kBACD1D,CAAC,EAAE,CACD,CAAC,EACD,CAACqE,IAAI,CAACG,GAAG,CAAEd,CAAC,GAAGW,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,GAAGb,CAAC,GAAG,EAAE,CAAC,EAC5C,EAAE,GAAG,GAAGA,CAAC,GAAG,EAAE,CAAC,EACf,CAACW,IAAI,CAACG,GAAG,CAAEd,CAAC,GAAGW,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,GAAGb,CAAC,GAAG,EAAE,CAAC,EAC5C,CAAC,CACF;kBACDzD,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBAC9BS,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG;gBAC/B,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,EAAE,GAAG8C,CAAC,GAAG,CAAC;kBACpB5B,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE,WAAW;kBACjBnB,KAAK,EAAE6C,CAAC,GAAG;gBACb;cAAE,GArCGA,CAAC;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCP,CACF,CAAC,EAGD,CAAC,GAAG+C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB/F,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBAETb,SAAS,EAAC,+BAA+B;gBACzCkB,KAAK,EAAE;kBACLyB,UAAU,EAAG,0BACX,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACmB,CAAC,CAC3F,YAAW;kBACZC,IAAI,EAAG,GAAE,EAAE,GAAGD,CAAC,GAAG,EAAG,GAAE;kBACvBE,GAAG,EAAG,GAAE,EAAE,GAAIF,CAAC,GAAG,CAAC,GAAI,EAAG,GAAE;kBAC5BpB,SAAS,EAAG,YACV,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACoB,CAAC,CAC3F;gBACH,CAAE;gBACFxD,OAAO,EAAE;kBACPF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACd4B,CAAC,EAAE,CAAC,CAAC,EAAEyC,IAAI,CAACG,GAAG,CAACd,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;kBAC3BzD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAClBS,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;kBACpBwB,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;gBACtB,CAAE;gBACFvB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC,GAAG8C,CAAC,GAAG,GAAG;kBACrB5B,MAAM,EAAEC,QAAQ;kBAChBlB,KAAK,EAAE6C,CAAC,GAAG,GAAG;kBACd1B,IAAI,EAAE;gBACR;cAAE,GAxBI,WAAU0B,CAAE,EAAC;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBpB,CACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,iGAAiG;cAC3GkB,KAAK,EAAE;gBACLyB,UAAU,EAAE,gEAAgE;gBAC5ED,SAAS,EAAE,oEAAoE;gBAC/EmC,MAAM,EAAE;cACV,CAAE;cAAA5E,QAAA,gBAGFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,gFAAgF;gBAC1FM,OAAO,EAAE;kBACP0B,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;gBACrB,CAAE;gBACFjB,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEF7C,OAAA,CAACV,QAAQ;gBAAC2C,SAAS,EAAC;cAA4C;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnE7C,OAAA;gBAAMiC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE3D;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGP7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTb,SAAS,EAAC,+BAA+B;gBACzCM,OAAO,EAAE;kBACPoC,SAAS,EAAE,CACT,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC;gBAErC,CAAE;gBACF3B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXkB,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAAC+H,CAAC;cACP3E,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAID;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGX7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,wIAAwI;cAAAC,QAAA,EAEjJ,CAACvB,IAAI,gBACJX,OAAA,CAACrB,KAAK,CAACqI,QAAQ;gBAAA9E,QAAA,eAIblC,OAAA;kBAAKiC,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBACpFlC,OAAA,CAAClB,IAAI;oBAACmI,EAAE,EAAC,WAAW;oBAAChF,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/ClC,OAAA,CAAChB,MAAM,CAACkI,MAAM;sBACZjF,SAAS,EAAC,yOAAyO;sBACnPqC,UAAU,EAAE;wBAAEvB,KAAK,EAAE;sBAAK,CAAE;sBAC5BoE,QAAQ,EAAE;wBAAEpE,KAAK,EAAE;sBAAK,CAAE;sBAAAb,QAAA,eAE1BlC,OAAA;wBAAKiC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzDlC,OAAA,CAACoH,UAAU;0BAACnF,SAAS,EAAC;wBAAqC;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9D7C,OAAA;0BAAMiC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eAEP7C,OAAA,CAAClB,IAAI;oBAACmI,EAAE,EAAC,QAAQ;oBAAChF,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC5ClC,OAAA,CAAChB,MAAM,CAACkI,MAAM;sBACZjF,SAAS,EAAC,yOAAyO;sBACnPqC,UAAU,EAAE;wBAAEvB,KAAK,EAAE;sBAAK,CAAE;sBAC5BoE,QAAQ,EAAE;wBAAEpE,KAAK,EAAE;sBAAK,CAAE;sBAAAb,QAAA,eAE1BlC,OAAA;wBAAKiC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACzDlC,OAAA,CAACR,OAAO;0BAACyC,SAAS,EAAC;wBAAqC;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3D7C,OAAA;0BAAMiC,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,EAAC;wBAAK;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,gBAEjB7C,OAAA,CAAClB,IAAI;gBAACmI,EAAE,EAAC,WAAW;gBAAChF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/ClC,OAAA,CAAChB,MAAM,CAACkI,MAAM;kBACZjF,SAAS,EAAC,iMAAiM;kBAC3MqC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5BoE,QAAQ,EAAE;oBAAEpE,KAAK,EAAE;kBAAK,CAAE;kBAAAb,QAAA,eAE1BlC,OAAA;oBAAKiC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDlC,OAAA,CAACqH,0BAA0B;sBAACpF,SAAS,EAAC;oBAAuB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChE7C,OAAA;sBAAMiC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cACTV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BW,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CjB,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBAEjElC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,eACrElC,OAAA,CAACF,eAAe;oBAACwH,GAAG,EAAC,MAAM;oBAACrE,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN7C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,eACvElC,OAAA,CAACF,eAAe;oBAACwH,GAAG,EAAC,MAAM;oBAACrE,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACN7C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,eACtElC,OAAA,CAACF,eAAe;oBAACwH,GAAG,EAAC,KAAK;oBAACrE,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN7C,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,eACvElC,OAAA,CAACF,eAAe;oBAACwH,GAAG,EAAC,MAAM;oBAACrE,QAAQ,EAAE,IAAK;oBAACC,KAAK,EAAE;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE2B,CAAC,EAAE;YAAG,CAAE;YAC/B1B,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE2B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CjB,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAE1FlC,OAAA;cAAKiC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAEvBlC,OAAA;gBAAKiC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7FlC,OAAA;kBACEsD,GAAG,EAAEiE,MAAO;kBACZhE,GAAG,EAAC,mBAAmB;kBACvBtB,SAAS,EAAC,yCAAyC;kBACnDxB,OAAO,EAAC;gBAAM;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE;gBAAY,CAAE;gBACjEpC,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,eAE5IlC,OAAA,CAACZ,QAAQ;kBAAC6C,SAAS,EAAC;gBAAqC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAEb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAE,CAAE;gBAC3EjB,SAAS,EAAC,uIAAuI;gBAAAC,QAAA,eAEjJlC,OAAA,CAACb,MAAM;kBAAC8C,SAAS,EAAC;gBAAqC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAEb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;gBACTP,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAE;gBAC5BW,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC,QAAQ;kBAAEC,IAAI,EAAE,WAAW;kBAAEnB,KAAK,EAAE;gBAAI,CAAE;gBAC7EjB,SAAS,EAAC,4HAA4H;gBAAAC,QAAA,eAEtIlC,OAAA,CAACd,OAAO;kBAAC+C,SAAS,EAAC;gBAAqC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAGb7C,OAAA;gBAAKiC,SAAS,EAAC;cAAoI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7C,OAAA;QAAKiC,SAAS,EAAC,2GAA2G;QAAAC,QAAA,eACxHlC,OAAA;UAAKiC,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAClFlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YACTV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BmF,WAAW,EAAE;cAAElF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BwE,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBzF,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EAEnF,CACC;cAAEyF,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAExI,OAAO;cAAEmF,KAAK,EAAE;YAA4B,CAAC,EAC9F;cAAEmD,MAAM,EAAE,MAAM;cAAEC,IAAI,EAAE,iBAAiB;cAAEC,IAAI,EAAEvI,QAAQ;cAAEkF,KAAK,EAAE;YAA8B,CAAC,EACjG;cAAEmD,MAAM,EAAE,OAAO;cAAEC,IAAI,EAAE,eAAe;cAAEC,IAAI,EAAE1I,MAAM;cAAEqF,KAAK,EAAE;YAAgC,CAAC,EAChG;cAAEmD,MAAM,EAAE,KAAK;cAAEC,IAAI,EAAE,cAAc;cAAEC,IAAI,EAAEzI,QAAQ;cAAEoF,KAAK,EAAE;YAAgC,CAAC,CAChG,CAACqB,GAAG,CAAC,CAACiC,IAAI,EAAEC,KAAK,kBAChB/H,OAAA,CAAChB,MAAM,CAAC8D,GAAG;cAETV,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,EAAE;gBAAEU,KAAK,EAAE;cAAI,CAAE;cAC3CyE,WAAW,EAAE;gBAAElF,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE,CAAC;gBAAEU,KAAK,EAAE;cAAE,CAAE;cAC5CC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE6E,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBpD,UAAU,EAAE;gBAAEvB,KAAK,EAAE,IAAI;gBAAEV,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCJ,SAAS,EAAC,gIAAgI;cAAAC,QAAA,gBAE1IlC,OAAA;gBAAKiC,SAAS,EAAG,gFAA+E6F,IAAI,CAACtD,KAAM,2FAA2F;gBAAAtC,QAAA,eACpMlC,OAAA,CAAC8H,IAAI,CAACD,IAAI;kBAAC5F,SAAS,EAAC;gBAAkC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN7C,OAAA;gBAAKiC,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EAAE4F,IAAI,CAACH;cAAM;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1G7C,OAAA;gBAAKiC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,EAAE4F,IAAI,CAACF;cAAI;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAZvFkF,KAAK;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV7C,OAAA;MAASmB,GAAG,EAAEsB,iBAAkB;MAACR,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC5ElC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BmF,WAAW,EAAE;YAAElF,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BwE,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBzF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7ClC,OAAA;YAAIiC,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7C,OAAA;YAAGiC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb7C,OAAA;UAAKiC,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EACnG,CACC;YACE3B,IAAI,EAAE,cAAc;YACpB+E,KAAK,EAAE,QAAQ;YACf0C,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,+GAA+G;YACrHK,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACE3H,IAAI,EAAE,cAAc;YACpB+E,KAAK,EAAE,SAAS;YAChB0C,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,gGAAgG;YACtGK,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,EACD;YACE3H,IAAI,EAAE,aAAa;YACnB+E,KAAK,EAAE,QAAQ;YACf0C,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,2GAA2G;YACjHK,MAAM,EAAE,8GAA8G;YACtHC,OAAO,EAAE;UACX,CAAC,CACF,CAACrC,GAAG,CAAC,CAACsC,MAAM,EAAEJ,KAAK,kBAClB/H,OAAA,CAAChB,MAAM,CAAC8D,GAAG;YAETV,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BmF,WAAW,EAAE;cAAElF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCW,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE6E,KAAK,GAAG;YAAI,CAAE;YAClDN,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBzF,SAAS,EAAC,6JAA6J;YAAAC,QAAA,gBAGvKlC,OAAA;cAAKiC,SAAS,EAAG,qDAAoDkG,MAAM,CAACD,OAAQ;YAAE;cAAAxF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG7F7C,OAAA;cAAKiC,SAAS,EAAC,0FAA0F;cAAAC,QAAA,eACvGlC,OAAA;gBAAKiC,SAAS,EAAC,uBAAuB;gBAACmG,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAnG,QAAA,eAC5ElC,OAAA;kBAAMsI,CAAC,EAAC;gBAAmN;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1N;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7C,OAAA;cAAKiC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDlC,OAAA;gBAAKiC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC,CAAC,GAAG0D,KAAK,CAACuC,MAAM,CAACH,MAAM,CAAC,CAAC,CAACnC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClC/F,OAAA,CAAChB,MAAM,CAAC8D,GAAG;kBAETV,OAAO,EAAE;oBAAEW,KAAK,EAAE,CAAC;oBAAEwB,MAAM,EAAE,CAAC;kBAAI,CAAE;kBACpCiD,WAAW,EAAE;oBAAEzE,KAAK,EAAE,CAAC;oBAAEwB,MAAM,EAAE;kBAAE,CAAE;kBACrCvB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,KAAK,EAAE6C,CAAC,GAAG;kBAAI,CAAE;kBAC9C0B,QAAQ,EAAE;oBAAEC,IAAI,EAAE;kBAAK,CAAE;kBAAAxF,QAAA,eAEzBlC,OAAA,CAACT,MAAM;oBAAC0C,SAAS,EAAC,wCAAwC;oBAACkB,KAAK,EAAE;sBAAEiF,IAAI,EAAE,SAAS;sBAAE5D,KAAK,EAAE;oBAAU;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GANtGkD,CAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOI,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7C,OAAA;gBAAKiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEN7C,OAAA;cAAGiC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACpElC,OAAA;gBAAMiC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzE7C,OAAA;gBAAMiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEiG,MAAM,CAACP;cAAI;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD7C,OAAA;gBAAMiC,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAAC;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eAEJ7C,OAAA;cAAKiC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClC,OAAA;gBAAKiC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAE5BlC,OAAA;kBAAKiC,SAAS,EAAG,2CAA0CkG,MAAM,CAACD,OAAQ,QAAQ;kBAAAhG,QAAA,gBAChFlC,OAAA;oBACEsD,GAAG,EAAE6E,MAAM,CAACF,MAAO;oBACnB1E,GAAG,EAAE4E,MAAM,CAAC5H,IAAK;oBACjB0B,SAAS,EAAC,+DAA+D;oBACzEwB,OAAO,EAAGhC,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACE,MAAM,CAACwB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BlC,CAAC,CAACE,MAAM,CAACkD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF7C,OAAA;oBAAKiC,SAAS,EAAC,4FAA4F;oBAACkB,KAAK,EAAE;sBAACQ,OAAO,EAAE;oBAAM,CAAE;oBAAAzB,QAAA,eACnIlC,OAAA;sBAAMiC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAClDiG,MAAM,CAAC5H,IAAI,CAACgI,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAC2C,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;oBAAC;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN7C,OAAA;kBAAKiC,SAAS,EAAC;gBAAqF;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC,eAEN7C,OAAA;gBAAKiC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlC,OAAA;kBAAKiC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ClC,OAAA;oBAAIiC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEiG,MAAM,CAAC5H;kBAAI;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAElE7C,OAAA;oBAAKiC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,eAChClC,OAAA;sBAAKiC,SAAS,EAAC,uBAAuB;sBAACmG,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAnG,QAAA,eAC5ElC,OAAA;wBAAMsI,CAAC,EAAC;sBAA8F;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7C,OAAA;kBAAKiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/ClC,OAAA;oBAAMiC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEiG,MAAM,CAAC7C;kBAAK;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzE7C,OAAA;oBAAMiC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChD7C,OAAA;oBAAMiC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAlFDkF,KAAK;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmFA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV7C,OAAA;MAASmB,GAAG,EAAE4D,YAAa;MAAC9C,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eACrElC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DlC,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BmF,WAAW,EAAE;YAAElF,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BwE,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBzF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7ClC,OAAA;YAAIiC,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE9E;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7C,OAAA;YAAGiC,SAAS,EAAC,iEAAiE;YAAAC,QAAA,GAAC,8HAC+C,eAAAlC,OAAA;cAAAkC,QAAA,EAAQ;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAC/J;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;UACTV,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BmF,WAAW,EAAE;YAAElF,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCW,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CuE,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBzF,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eAElElC,OAAA;YAAM0I,QAAQ,EAAE9G,YAAa;YAACK,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAC9DlC,OAAA;cAAKiC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChElC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEyF,IAAI,EAAC,MAAM;kBACXlF,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrBoI,QAAQ,EAAEnH,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxH2G,WAAW,EAAC,sBAAsB;kBAClCC,QAAQ;gBAAA;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7C,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACEyF,IAAI,EAAC,OAAO;kBACZlF,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtBmI,QAAQ,EAAEnH,YAAa;kBACvBS,SAAS,EAAC,8GAA8G;kBACxH2G,WAAW,EAAC,kBAAkB;kBAC9BC,QAAQ;gBAAA;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7C,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEO,IAAI,EAAC,SAAS;gBACdmB,KAAK,EAAErB,QAAQ,CAACV,OAAQ;gBACxBgJ,QAAQ,EAAEnH,YAAa;gBACvBsH,IAAI,EAAE,CAAE;gBACR7G,SAAS,EAAC,8GAA8G;gBACxH2G,WAAW,EAAC,gCAAgC;gBAC5CC,QAAQ;cAAA;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7C,OAAA;cAAKiC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC3DlC,OAAA;gBACEyF,IAAI,EAAC,QAAQ;gBACbsD,QAAQ,EAAEtI,OAAQ;gBAClBwB,SAAS,EAAC,iMAAiM;gBAAAC,QAAA,EAE1MzB,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAGT7C,OAAA,CAAChB,MAAM,CAACgK,CAAC;gBACPC,IAAI,EAAC,mBAAmB;gBACxBhH,SAAS,EAAC,kPAAkP;gBAC5PqC,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoE,QAAQ,EAAE;kBAAEpE,KAAK,EAAE;gBAAK,CAAE;gBAAAb,QAAA,gBAE1BlC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAACmG,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAnG,QAAA,eAC9DlC,OAAA;oBAAMsI,CAAC,EAAC;kBAAkP;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzP,CAAC,eACN7C,OAAA;kBAAAkC,QAAA,EAAM;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eAGX7C,OAAA,CAAChB,MAAM,CAACgK,CAAC;gBACPC,IAAI,EAAC,+IAA+I;gBACpJtH,MAAM,EAAC,QAAQ;gBACfuH,GAAG,EAAC,qBAAqB;gBACzBjH,SAAS,EAAC,sPAAsP;gBAChQqC,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5BoE,QAAQ,EAAE;kBAAEpE,KAAK,EAAE;gBAAK,CAAE;gBAAAb,QAAA,gBAE1BlC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAACmG,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAnG,QAAA,eAC9DlC,OAAA;oBAAMsI,CAAC,EAAC;kBAAklC;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACN7C,OAAA;kBAAAkC,QAAA,EAAM;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAELsG,eAAe,iBACdnJ,OAAA;cAAKiC,SAAS,EAAG,kBAAiBkH,eAAe,CAACC,QAAQ,CAAC,SAAS,CAAC,GAAG,6BAA6B,GAAG,yBAA0B,EAAE;cAAAlH,QAAA,EACjIiH;YAAe;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV7C,OAAA;MAAQiC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7ClC,OAAA;QAAKiC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE7DlC,OAAA;UAAKiC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlC,OAAA;YAAKiC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DlC,OAAA;cAAMiC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D7C,OAAA;cAAMiC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN7C,OAAA;YAAGiC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN7C,OAAA;UAAKiC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5ClC,OAAA;YAAKiC,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FlC,OAAA;cAAKiC,SAAS,EAAC,4EAA4E;cAAAC,QAAA,gBACzFlC,OAAA;gBAAKiC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClC,OAAA;kBAAMiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD7C,OAAA;kBAAMiC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN7C,OAAA;gBAAMiC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxE7C,OAAA;gBAAMiC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eAEN7C,OAAA;cAAKiC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChElC,OAAA;gBAAAkC,QAAA,EAAM;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3B7C,OAAA;gBAAAkC,QAAA,EAAM;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7B7C,OAAA;gBAAAkC,QAAA,EAAM;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7C,OAAA;YAAKiC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAExD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAKT7C,OAAA,CAAChB,MAAM,CAAC8D,GAAG;MACTV,OAAO,EAAE;QAAEW,KAAK,EAAE,CAAC;QAAEwB,MAAM,EAAE,CAAC;MAAI,CAAE;MACpChC,OAAO,EAAE;QAAEQ,KAAK,EAAE,CAAC;QAAEwB,MAAM,EAAE;MAAE,CAAE;MACjCvB,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACxCjB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAEvClC,OAAA,CAAChB,MAAM,CAACgK,CAAC;QACPC,IAAI,EAAC,uHAAuH;QAC5HtH,MAAM,EAAC,QAAQ;QACfuH,GAAG,EAAC,qBAAqB;QACzBjH,SAAS,EAAC,0KAA0K;QACpLqC,UAAU,EAAE;UAAEvB,KAAK,EAAE;QAAI,CAAE;QAC3BoE,QAAQ,EAAE;UAAEpE,KAAK,EAAE;QAAK,CAAE;QAAAb,QAAA,gBAG1BlC,OAAA;UAAKiC,SAAS,EAAC,oBAAoB;UAACmG,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAnG,QAAA,eACzElC,OAAA;YAAMsI,CAAC,EAAC;UAAklC;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzlC,CAAC,eAGN7C,OAAA;UAAKiC,SAAS,EAAC;QAAmE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGzF7C,OAAA;UAAKiC,SAAS,EAAC,8LAA8L;UAAAC,QAAA,GAAC,0BAE5M,eAAAlC,OAAA;YAAKiC,SAAS,EAAC;UAAoF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAx0CID,IAAI;EAAA,QAISL,WAAW,EACXb,WAAW;AAAA;AAAAsK,EAAA,GALxBpJ,IAAI;AA00CV,eAAeA,IAAI;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}